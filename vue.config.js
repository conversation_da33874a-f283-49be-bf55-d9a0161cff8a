const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  transpileDependencies: true,
  chainWebpack: config => {
    config.plugin('define').tap(args => {
      args[0]['__VUE_PROD_HYDRATION_MISMATCH_DETAILS__'] = JSON.stringify(false);
      return args;
    });
  },
  pluginOptions: {
    electronBuilder: {
      mainProcessFile: 'background.js',
      preload: 'src/preload/preload.js',
      chainWebpackMainProcess: config => {
        config.output.filename('background.js');
      },
      chainWebpackPreload: config => {
        config.externals({
          electron: 'electron'
        });
      },
      builderOptions: {
        extraResources: [
          {
            from: 'public',
            to: 'public',
            filter: ['**/*']
          }
        ]
      }
    }
  }
});
