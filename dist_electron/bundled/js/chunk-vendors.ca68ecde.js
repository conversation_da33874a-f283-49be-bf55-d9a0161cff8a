"use strict";(self["webpackChunkprompt_crafter"]=self["webpackChunkprompt_crafter"]||[]).push([[504],{0:(e,t,n)=>{n.d(t,{P7L:()=>fu,y8v:()=>cu});var o=n(641),a=n(953),r=n(33),l=n(751);let i=new Map;class s{format(e){return this.formatter.format(e)}formatToParts(e){return this.formatter.formatToParts(e)}formatRange(e,t){if("function"===typeof this.formatter.formatRange)return this.formatter.formatRange(e,t);if(t<e)throw new RangeError("End date must be >= start date");return`${this.formatter.format(e)} – ${this.formatter.format(t)}`}formatRangeToParts(e,t){if("function"===typeof this.formatter.formatRangeToParts)return this.formatter.formatRangeToParts(e,t);if(t<e)throw new RangeError("End date must be >= start date");let n=this.formatter.formatToParts(e),o=this.formatter.formatToParts(t);return[...n.map(e=>({...e,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...o.map(e=>({...e,source:"endRange"}))]}resolvedOptions(){let e=this.formatter.resolvedOptions();return v()&&(this.resolvedHourCycle||(this.resolvedHourCycle=h(e.locale,this.options)),e.hourCycle=this.resolvedHourCycle,e.hour12="h11"===this.resolvedHourCycle||"h12"===this.resolvedHourCycle),"ethiopic-amete-alem"===e.calendar&&(e.calendar="ethioaa"),e}constructor(e,t={}){this.formatter=d(e,t),this.options=t}}const u={true:{ja:"h11"},false:{}};function d(e,t={}){if("boolean"===typeof t.hour12&&f()){t={...t};let n=u[String(t.hour12)][e.split("-")[0]],o=t.hour12?"h12":"h23";t.hourCycle=null!==n&&void 0!==n?n:o,delete t.hour12}let n=e+(t?Object.entries(t).sort((e,t)=>e[0]<t[0]?-1:1).join():"");if(i.has(n))return i.get(n);let o=new Intl.DateTimeFormat(e,t);return i.set(n,o),o}let c=null;function f(){return null==c&&(c="24"===new Intl.DateTimeFormat("en-US",{hour:"numeric",hour12:!1}).format(new Date(2020,2,3,0))),c}let p=null;function v(){return null==p&&(p="h12"===new Intl.DateTimeFormat("fr",{hour:"numeric",hour12:!1}).resolvedOptions().hourCycle),p}function h(e,t){if(!t.timeStyle&&!t.hour)return;e=e.replace(/(-u-)?-nu-[a-zA-Z0-9]+/,""),e+=(e.includes("-u-")?"":"-u")+"-nu-latn";let n=d(e,{...t,timeZone:void 0}),o=parseInt(n.formatToParts(new Date(2020,2,3,0)).find(e=>"hour"===e.type).value,10),a=parseInt(n.formatToParts(new Date(2020,2,3,23)).find(e=>"hour"===e.type).value,10);if(0===o&&23===a)return"h23";if(24===o&&23===a)return"h24";if(0===o&&11===a)return"h11";if(12===o&&11===a)return"h12";throw new Error("Unexpected hour cycle result")}function m(e,t){return e-t*Math.floor(e/t)}const y=1721426;function g(e,t,n,o){t=w(e,t);let a=t-1,r=-2;return n<=2?r=0:b(t)&&(r=-1),y-1+365*a+Math.floor(a/4)-Math.floor(a/100)+Math.floor(a/400)+Math.floor((367*n-362)/12+r+o)}function b(e){return e%4===0&&(e%100!==0||e%400===0)}function w(e,t){return"BC"===e?1-t:t}function R(e){let t="AD";return e<=0&&(t="BC",e=1-e),[t,e]}const x={standard:[31,28,31,30,31,30,31,31,30,31,30,31],leapyear:[31,29,31,30,31,30,31,31,30,31,30,31]};class B{fromJulianDay(e){let t=e,n=t-y,o=Math.floor(n/146097),a=m(n,146097),r=Math.floor(a/36524),l=m(a,36524),i=Math.floor(l/1461),s=m(l,1461),u=Math.floor(s/365),d=400*o+100*r+4*i+u+(4!==r&&4!==u?1:0),[c,f]=R(d),p=t-g(c,f,1,1),v=2;t<g(c,f,3,1)?v=0:b(f)&&(v=1);let h=Math.floor((12*(p+v)+373)/367),w=t-g(c,f,h,1)+1;return new Ve(c,f,h,w)}toJulianDay(e){return g(e.era,e.year,e.month,e.day)}getDaysInMonth(e){return x[b(e.year)?"leapyear":"standard"][e.month-1]}getMonthsInYear(e){return 12}getDaysInYear(e){return b(e.year)?366:365}getYearsInEra(e){return 9999}getEras(){return["BC","AD"]}isInverseEra(e){return"BC"===e.era}balanceDate(e){e.year<=0&&(e.era="BC"===e.era?"AD":"BC",e.year=1-e.year)}constructor(){this.identifier="gregory"}}const _={"001":1,AD:1,AE:6,AF:6,AI:1,AL:1,AM:1,AN:1,AR:1,AT:1,AU:1,AX:1,AZ:1,BA:1,BE:1,BG:1,BH:6,BM:1,BN:1,BY:1,CH:1,CL:1,CM:1,CN:1,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DZ:6,EC:1,EE:1,EG:6,ES:1,FI:1,FJ:1,FO:1,FR:1,GB:1,GE:1,GF:1,GP:1,GR:1,HR:1,HU:1,IE:1,IQ:6,IR:6,IS:1,IT:1,JO:6,KG:1,KW:6,KZ:1,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MK:1,MN:1,MQ:1,MV:5,MY:1,NL:1,NO:1,NZ:1,OM:6,PL:1,QA:6,RE:1,RO:1,RS:1,RU:1,SD:6,SE:1,SI:1,SK:1,SM:1,SY:6,TJ:1,TM:1,TR:1,UA:1,UY:1,UZ:1,VA:1,VN:1,XK:1};function E(e,t){return t=ae(t,e.calendar),e.era===t.era&&e.year===t.year&&e.month===t.month&&e.day===t.day}function C(e,t){return t=ae(t,e.calendar),e=V(e),t=V(t),e.era===t.era&&e.year===t.year&&e.month===t.month}function k(e,t){return S(e.calendar,t.calendar)&&E(e,t)}function M(e,t){return S(e.calendar,t.calendar)&&C(e,t)}function S(e,t){var n,o,a,r;return null!==(r=null!==(a=null===(n=e.isEqual)||void 0===n?void 0:n.call(e,t))&&void 0!==a?a:null===(o=t.isEqual)||void 0===o?void 0:o.call(t,e))&&void 0!==r?r:e.identifier===t.identifier}function D(e,t){return E(e,P(t))}const A={sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6};function O(e,t,n){let o=e.calendar.toJulianDay(e),a=n?A[n]:G(t),r=Math.ceil(o+1-a)%7;return r<0&&(r+=7),r}function T(e){return te(Date.now(),e)}function P(e){return ne(T(e))}function I(e,t){return e.calendar.toJulianDay(e)-t.calendar.toJulianDay(t)}function W(e,t){return F(e)-F(t)}function F(e){return 36e5*e.hour+6e4*e.minute+1e3*e.second+e.millisecond}let $=null;function N(){return null==$&&($=(new Intl.DateTimeFormat).resolvedOptions().timeZone),$}function V(e){return e.subtract({days:e.day-1})}function L(e){return e.add({days:e.calendar.getDaysInMonth(e)-e.day})}const K=new Map;function j(e){if(Intl.Locale){let t=K.get(e);return t||(t=new Intl.Locale(e).maximize().region,t&&K.set(e,t)),t}let t=e.split("-")[1];return"u"===t?void 0:t}function G(e){let t=j(e);return t&&_[t]||0}function X(e){e=ae(e,new B);let t=w(e.era,e.year);return U(t,e.month,e.day,e.hour,e.minute,e.second,e.millisecond)}function U(e,t,n,o,a,r,l){let i=new Date;return i.setUTCHours(o,a,r,l),i.setUTCFullYear(e,t-1,n),i.getTime()}function z(e,t){if("UTC"===t)return 0;if(e>0&&t===N())return-6e4*new Date(e).getTimezoneOffset();let{year:n,month:o,day:a,hour:r,minute:l,second:i}=Y(e,t),s=U(n,o,a,r,l,i,0);return s-1e3*Math.floor(e/1e3)}const H=new Map;function Y(e,t){let n=H.get(t);n||(n=new Intl.DateTimeFormat("en-US",{timeZone:t,hour12:!1,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}),H.set(t,n));let o=n.formatToParts(new Date(e)),a={};for(let r of o)"literal"!==r.type&&(a[r.type]=r.value);return{year:"BC"===a.era||"B"===a.era?1-a.year:+a.year,month:+a.month,day:+a.day,hour:"24"===a.hour?0:+a.hour,minute:+a.minute,second:+a.second}}const Z=864e5;function q(e,t,n,o){let a=n===o?[n]:[n,o];return a.filter(n=>J(e,t,n))}function J(e,t,n){let o=Y(n,t);return e.year===o.year&&e.month===o.month&&e.day===o.day&&e.hour===o.hour&&e.minute===o.minute&&e.second===o.second}function Q(e,t,n="compatible"){let o=oe(e);if("UTC"===t)return X(o);if(t===N()&&"compatible"===n){o=ae(o,new B);let e=new Date,t=w(o.era,o.year);return e.setFullYear(t,o.month-1,o.day),e.setHours(o.hour,o.minute,o.second,o.millisecond),e.getTime()}let a=X(o),r=z(a-Z,t),l=z(a+Z,t),i=q(o,t,a-r,a-l);if(1===i.length)return i[0];if(i.length>1)switch(n){case"compatible":case"earlier":return i[0];case"later":return i[i.length-1];case"reject":throw new RangeError("Multiple possible absolute times found")}switch(n){case"earlier":return Math.min(a-r,a-l);case"compatible":case"later":return Math.max(a-r,a-l);case"reject":throw new RangeError("No such absolute time found")}}function ee(e,t,n="compatible"){return new Date(Q(e,t,n))}function te(e,t){let n=z(e,t),o=new Date(e+n),a=o.getUTCFullYear(),r=o.getUTCMonth()+1,l=o.getUTCDate(),i=o.getUTCHours(),s=o.getUTCMinutes(),u=o.getUTCSeconds(),d=o.getUTCMilliseconds();return new Ge(a<1?"BC":"AD",a<1?1-a:a,r,l,t,n,i,s,u,d)}function ne(e){return new Ve(e.calendar,e.era,e.year,e.month,e.day)}function oe(e,t){let n=0,o=0,a=0,r=0;if("timeZone"in e)({hour:n,minute:o,second:a,millisecond:r}=e);else if("hour"in e&&!t)return e;return t&&({hour:n,minute:o,second:a,millisecond:r}=t),new Ke(e.calendar,e.era,e.year,e.month,e.day,n,o,a,r)}function ae(e,t){if(S(e.calendar,t))return e;let n=t.fromJulianDay(e.calendar.toJulianDay(e)),o=e.copy();return o.calendar=t,o.era=n.era,o.year=n.year,o.month=n.month,o.day=n.day,ve(o),o}function re(e,t,n){if(e instanceof Ge)return e.timeZone===t?e:ie(e,t);let o=Q(e,t,n);return te(o,t)}function le(e){let t=X(e)-e.offset;return new Date(t)}function ie(e,t){let n=X(e)-e.offset;return ae(te(n,t),e.calendar)}const se=36e5;function ue(e,t){let n=e.copy(),o="hour"in n?xe(n,t):0;de(n,t.years||0),n.calendar.balanceYearMonth&&n.calendar.balanceYearMonth(n,e),n.month+=t.months||0,ce(n),pe(n),n.day+=7*(t.weeks||0),n.day+=t.days||0,n.day+=o,fe(n),n.calendar.balanceDate&&n.calendar.balanceDate(n),n.year<1&&(n.year=1,n.month=1,n.day=1);let a=n.calendar.getYearsInEra(n);if(n.year>a){var r,l;let e=null===(r=(l=n.calendar).isInverseEra)||void 0===r?void 0:r.call(l,n);n.year=a,n.month=e?1:n.calendar.getMonthsInYear(n),n.day=e?1:n.calendar.getDaysInMonth(n)}n.month<1&&(n.month=1,n.day=1);let i=n.calendar.getMonthsInYear(n);return n.month>i&&(n.month=i,n.day=n.calendar.getDaysInMonth(n)),n.day=Math.max(1,Math.min(n.calendar.getDaysInMonth(n),n.day)),n}function de(e,t){var n,o;(null===(n=(o=e.calendar).isInverseEra)||void 0===n?void 0:n.call(o,e))&&(t=-t),e.year+=t}function ce(e){while(e.month<1)de(e,-1),e.month+=e.calendar.getMonthsInYear(e);let t=0;while(e.month>(t=e.calendar.getMonthsInYear(e)))e.month-=t,de(e,1)}function fe(e){while(e.day<1)e.month--,ce(e),e.day+=e.calendar.getDaysInMonth(e);while(e.day>e.calendar.getDaysInMonth(e))e.day-=e.calendar.getDaysInMonth(e),e.month++,ce(e)}function pe(e){e.month=Math.max(1,Math.min(e.calendar.getMonthsInYear(e),e.month)),e.day=Math.max(1,Math.min(e.calendar.getDaysInMonth(e),e.day))}function ve(e){e.calendar.constrainDate&&e.calendar.constrainDate(e),e.year=Math.max(1,Math.min(e.calendar.getYearsInEra(e),e.year)),pe(e)}function he(e){let t={};for(let n in e)"number"===typeof e[n]&&(t[n]=-e[n]);return t}function me(e,t){return ue(e,he(t))}function ye(e,t){let n=e.copy();return null!=t.era&&(n.era=t.era),null!=t.year&&(n.year=t.year),null!=t.month&&(n.month=t.month),null!=t.day&&(n.day=t.day),ve(n),n}function ge(e,t){let n=e.copy();return null!=t.hour&&(n.hour=t.hour),null!=t.minute&&(n.minute=t.minute),null!=t.second&&(n.second=t.second),null!=t.millisecond&&(n.millisecond=t.millisecond),we(n),n}function be(e){e.second+=Math.floor(e.millisecond/1e3),e.millisecond=Re(e.millisecond,1e3),e.minute+=Math.floor(e.second/60),e.second=Re(e.second,60),e.hour+=Math.floor(e.minute/60),e.minute=Re(e.minute,60);let t=Math.floor(e.hour/24);return e.hour=Re(e.hour,24),t}function we(e){e.millisecond=Math.max(0,Math.min(e.millisecond,1e3)),e.second=Math.max(0,Math.min(e.second,59)),e.minute=Math.max(0,Math.min(e.minute,59)),e.hour=Math.max(0,Math.min(e.hour,23))}function Re(e,t){let n=e%t;return n<0&&(n+=t),n}function xe(e,t){return e.hour+=t.hours||0,e.minute+=t.minutes||0,e.second+=t.seconds||0,e.millisecond+=t.milliseconds||0,be(e)}function Be(e,t,n,o){let a=e.copy();switch(t){case"era":{let t=e.calendar.getEras(),r=t.indexOf(e.era);if(r<0)throw new Error("Invalid era: "+e.era);r=Ee(r,n,0,t.length-1,null===o||void 0===o?void 0:o.round),a.era=t[r],ve(a);break}case"year":var r,l;(null===(r=(l=a.calendar).isInverseEra)||void 0===r?void 0:r.call(l,a))&&(n=-n),a.year=Ee(e.year,n,-1/0,9999,null===o||void 0===o?void 0:o.round),a.year===-1/0&&(a.year=1),a.calendar.balanceYearMonth&&a.calendar.balanceYearMonth(a,e);break;case"month":a.month=Ee(e.month,n,1,e.calendar.getMonthsInYear(e),null===o||void 0===o?void 0:o.round);break;case"day":a.day=Ee(e.day,n,1,e.calendar.getDaysInMonth(e),null===o||void 0===o?void 0:o.round);break;default:throw new Error("Unsupported field "+t)}return e.calendar.balanceDate&&e.calendar.balanceDate(a),ve(a),a}function _e(e,t,n,o){let a=e.copy();switch(t){case"hour":{let t=e.hour,r=0,l=23;if(12===(null===o||void 0===o?void 0:o.hourCycle)){let e=t>=12;r=e?12:0,l=e?23:11}a.hour=Ee(t,n,r,l,null===o||void 0===o?void 0:o.round);break}case"minute":a.minute=Ee(e.minute,n,0,59,null===o||void 0===o?void 0:o.round);break;case"second":a.second=Ee(e.second,n,0,59,null===o||void 0===o?void 0:o.round);break;case"millisecond":a.millisecond=Ee(e.millisecond,n,0,999,null===o||void 0===o?void 0:o.round);break;default:throw new Error("Unsupported field "+t)}return a}function Ee(e,t,n,o,a=!1){if(a){e+=Math.sign(t),e<n&&(e=o);let a=Math.abs(t);e=t>0?Math.ceil(e/a)*a:Math.floor(e/a)*a,e>o&&(e=n)}else e+=t,e<n?e=o-(n-e-1):e>o&&(e=n+(e-o-1));return e}function Ce(e,t){let n;if(null!=t.years&&0!==t.years||null!=t.months&&0!==t.months||null!=t.weeks&&0!==t.weeks||null!=t.days&&0!==t.days){let o=ue(oe(e),{years:t.years,months:t.months,weeks:t.weeks,days:t.days});n=Q(o,e.timeZone)}else n=X(e)-e.offset;n+=t.milliseconds||0,n+=1e3*(t.seconds||0),n+=6e4*(t.minutes||0),n+=36e5*(t.hours||0);let o=te(n,e.timeZone);return ae(o,e.calendar)}function ke(e,t){return Ce(e,he(t))}function Me(e,t,n,o){switch(t){case"hour":{let t=0,a=23;if(12===(null===o||void 0===o?void 0:o.hourCycle)){let n=e.hour>=12;t=n?12:0,a=n?23:11}let r=oe(e),l=ae(ge(r,{hour:t}),new B),i=[Q(l,e.timeZone,"earlier"),Q(l,e.timeZone,"later")].filter(t=>te(t,e.timeZone).day===l.day)[0],s=ae(ge(r,{hour:a}),new B),u=[Q(s,e.timeZone,"earlier"),Q(s,e.timeZone,"later")].filter(t=>te(t,e.timeZone).day===s.day).pop(),d=X(e)-e.offset,c=Math.floor(d/se),f=d%se;return d=Ee(c,n,Math.floor(i/se),Math.floor(u/se),null===o||void 0===o?void 0:o.round)*se+f,ae(te(d,e.timeZone),e.calendar)}case"minute":case"second":case"millisecond":return _e(e,t,n,o);case"era":case"year":case"month":case"day":{let a=Be(oe(e),t,n,o),r=Q(a,e.timeZone);return ae(te(r,e.timeZone),e.calendar)}default:throw new Error("Unsupported field "+t)}}function Se(e,t,n){let o=oe(e),a=ge(ye(o,t),t);if(0===a.compare(o))return e;let r=Q(a,e.timeZone,n);return ae(te(r,e.timeZone),e.calendar)}const De=["hours","minutes","seconds"];function Ae(e){return`${String(e.hour).padStart(2,"0")}:${String(e.minute).padStart(2,"0")}:${String(e.second).padStart(2,"0")}${e.millisecond?String(e.millisecond/1e3).slice(1):""}`}function Oe(e){let t,n=ae(e,new B);return t="BC"===n.era?1===n.year?"0000":"-"+String(Math.abs(1-n.year)).padStart(6,"00"):String(n.year).padStart(4,"0"),`${t}-${String(n.month).padStart(2,"0")}-${String(n.day).padStart(2,"0")}`}function Te(e){return`${Oe(e)}T${Ae(e)}`}function Pe(e){let t=Math.sign(e)<0?"-":"+";e=Math.abs(e);let n=Math.floor(e/36e5),o=e%36e5/6e4;return`${t}${String(n).padStart(2,"0")}:${String(o).padStart(2,"0")}`}function Ie(e){return`${Te(e)}${Pe(e.offset)}[${e.timeZone}]`}function We(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Fe(e,t,n){We(e,t),t.set(e,n)}function $e(e){let t,n="object"===typeof e[0]?e.shift():new B;if("string"===typeof e[0])t=e.shift();else{let e=n.getEras();t=e[e.length-1]}let o=e.shift(),a=e.shift(),r=e.shift();return[n,t,o,a,r]}var Ne=new WeakMap;class Ve{copy(){return this.era?new Ve(this.calendar,this.era,this.year,this.month,this.day):new Ve(this.calendar,this.year,this.month,this.day)}add(e){return ue(this,e)}subtract(e){return me(this,e)}set(e){return ye(this,e)}cycle(e,t,n){return Be(this,e,t,n)}toDate(e){return ee(this,e)}toString(){return Oe(this)}compare(e){return I(this,e)}constructor(...e){Fe(this,Ne,{writable:!0,value:void 0});let[t,n,o,a,r]=$e(e);this.calendar=t,this.era=n,this.year=o,this.month=a,this.day=r,ve(this)}}var Le=new WeakMap;class Ke{copy(){return this.era?new Ke(this.calendar,this.era,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond):new Ke(this.calendar,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond)}add(e){return ue(this,e)}subtract(e){return me(this,e)}set(e){return ye(ge(this,e),e)}cycle(e,t,n){switch(e){case"era":case"year":case"month":case"day":return Be(this,e,t,n);default:return _e(this,e,t,n)}}toDate(e,t){return ee(this,e,t)}toString(){return Te(this)}compare(e){let t=I(this,e);return 0===t?W(this,oe(e)):t}constructor(...e){Fe(this,Le,{writable:!0,value:void 0});let[t,n,o,a,r]=$e(e);this.calendar=t,this.era=n,this.year=o,this.month=a,this.day=r,this.hour=e.shift()||0,this.minute=e.shift()||0,this.second=e.shift()||0,this.millisecond=e.shift()||0,ve(this)}}var je=new WeakMap;class Ge{copy(){return this.era?new Ge(this.calendar,this.era,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond):new Ge(this.calendar,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond)}add(e){return Ce(this,e)}subtract(e){return ke(this,e)}set(e,t){return Se(this,e,t)}cycle(e,t,n){return Me(this,e,t,n)}toDate(){return le(this)}toString(){return Ie(this)}toAbsoluteString(){return this.toDate().toISOString()}compare(e){return this.toDate().getTime()-re(e,this.timeZone).toDate().getTime()}constructor(...e){Fe(this,je,{writable:!0,value:void 0});let[t,n,o,a,r]=$e(e),l=e.shift(),i=e.shift();this.calendar=t,this.era=n,this.year=o,this.month=a,this.day=r,this.timeZone=l,this.offset=i,this.hour=e.shift()||0,this.minute=e.shift()||0,this.second=e.shift()||0,this.millisecond=e.shift()||0,ve(this)}}const Xe=-543;class Ue extends B{fromJulianDay(e){let t=super.fromJulianDay(e),n=w(t.era,t.year);return new Ve(this,n-Xe,t.month,t.day)}toJulianDay(e){return super.toJulianDay(ze(e))}getEras(){return["BE"]}getDaysInMonth(e){return super.getDaysInMonth(ze(e))}balanceDate(){}constructor(...e){super(...e),this.identifier="buddhist"}}function ze(e){let[t,n]=R(e.year+Xe);return new Ve(t,n,e.month,e.day)}const He=1723856,Ye=1824665,Ze=5500;function qe(e,t,n,o){return e+365*t+Math.floor(t/4)+30*(n-1)+o-1}function Je(e,t){let n=Math.floor(4*(t-e)/1461),o=1+Math.floor((t-qe(e,n,1,1))/30),a=t+1-qe(e,n,o,1);return[n,o,a]}function Qe(e){return Math.floor(e%4/3)}function et(e,t){return t%13!==0?30:Qe(e)+5}class tt{fromJulianDay(e){let[t,n,o]=Je(He,e),a="AM";return t<=0&&(a="AA",t+=Ze),new Ve(this,a,t,n,o)}toJulianDay(e){let t=e.year;return"AA"===e.era&&(t-=Ze),qe(He,t,e.month,e.day)}getDaysInMonth(e){return et(e.year,e.month)}getMonthsInYear(){return 13}getDaysInYear(e){return 365+Qe(e.year)}getYearsInEra(e){return"AA"===e.era?9999:9991}getEras(){return["AA","AM"]}constructor(){this.identifier="ethiopic"}}class nt extends tt{fromJulianDay(e){let[t,n,o]=Je(He,e);return t+=Ze,new Ve(this,"AA",t,n,o)}getEras(){return["AA"]}getYearsInEra(){return 9999}constructor(...e){super(...e),this.identifier="ethioaa"}}class ot extends tt{fromJulianDay(e){let[t,n,o]=Je(Ye,e),a="CE";return t<=0&&(a="BCE",t=1-t),new Ve(this,a,t,n,o)}toJulianDay(e){let t=e.year;return"BCE"===e.era&&(t=1-t),qe(Ye,t,e.month,e.day)}getDaysInMonth(e){let t=e.year;return"BCE"===e.era&&(t=1-t),et(t,e.month)}isInverseEra(e){return"BCE"===e.era}balanceDate(e){e.year<=0&&(e.era="BCE"===e.era?"CE":"BCE",e.year=1-e.year)}getEras(){return["BCE","CE"]}getYearsInEra(e){return"BCE"===e.era?9999:9715}constructor(...e){super(...e),this.identifier="coptic"}}const at=347997,rt=1080,lt=24*rt,it=29,st=12*rt+793,ut=it*lt+st;function dt(e){return m(7*e+1,19)<7}function ct(e){let t=Math.floor((235*e-234)/19),n=12084+13753*t,o=29*t+Math.floor(n/25920);return m(3*(o+1),7)<3&&(o+=1),o}function ft(e){let t=ct(e-1),n=ct(e),o=ct(e+1);return o-n===356?2:n-t===382?1:0}function pt(e){return ct(e)+ft(e)}function vt(e){return pt(e+1)-pt(e)}function ht(e){let t=vt(e);switch(t>380&&(t-=30),t){case 353:return 0;case 354:return 1;case 355:return 2}}function mt(e,t){if(t>=6&&!dt(e)&&t++,4===t||7===t||9===t||11===t||13===t)return 29;let n=ht(e);return 2===t?2===n?30:29:3===t?0===n?29:30:6===t?dt(e)?30:0:30}class yt{fromJulianDay(e){let t=e-at,n=t*lt/ut,o=Math.floor((19*n+234)/235)+1,a=pt(o),r=Math.floor(t-a);while(r<1)o--,a=pt(o),r=Math.floor(t-a);let l=1,i=0;while(i<r)i+=mt(o,l),l++;l--,i-=mt(o,l);let s=r-i;return new Ve(this,o,l,s)}toJulianDay(e){let t=pt(e.year);for(let n=1;n<e.month;n++)t+=mt(e.year,n);return t+e.day+at}getDaysInMonth(e){return mt(e.year,e.month)}getMonthsInYear(e){return dt(e.year)?13:12}getDaysInYear(e){return vt(e.year)}getYearsInEra(){return 9999}getEras(){return["AM"]}balanceYearMonth(e,t){t.year!==e.year&&(dt(t.year)&&!dt(e.year)&&t.month>6?e.month--:!dt(t.year)&&dt(e.year)&&t.month>6&&e.month++)}constructor(){this.identifier="hebrew"}}const gt=78,bt=80;class wt extends B{fromJulianDay(e){let t,n,o,a=super.fromJulianDay(e),r=a.year-gt,l=e-g(a.era,a.year,1,1);if(l<bt?(r--,t=b(a.year-1)?31:30,l+=t+155+90+10):(t=b(a.year)?31:30,l-=bt),l<t)n=1,o=l+1;else{let e=l-t;e<155?(n=Math.floor(e/31)+2,o=e%31+1):(e-=155,n=Math.floor(e/30)+7,o=e%30+1)}return new Ve(this,r,n,o)}toJulianDay(e){let t,n,o=e.year+gt,[a,r]=R(o);return b(r)?(t=31,n=g(a,r,3,21)):(t=30,n=g(a,r,3,22)),1===e.month?n+e.day-1:(n+=t+31*Math.min(e.month-2,5),e.month>=8&&(n+=30*(e.month-7)),n+=e.day-1,n)}getDaysInMonth(e){return 1===e.month&&b(e.year+gt)||e.month>=2&&e.month<=6?31:30}getYearsInEra(){return 9919}getEras(){return["saka"]}balanceDate(){}constructor(...e){super(...e),this.identifier="indian"}}const Rt=1948440,xt=1948439,Bt=1300,_t=1600,Et=460322;function Ct(e,t,n,o){return o+Math.ceil(29.5*(n-1))+354*(t-1)+Math.floor((3+11*t)/30)+e-1}function kt(e,t,n){let o=Math.floor((30*(n-t)+10646)/10631),a=Math.min(12,Math.ceil((n-(29+Ct(t,o,1,1)))/29.5)+1),r=n-Ct(t,o,a,1)+1;return new Ve(e,o,a,r)}function Mt(e){return(14+11*e)%30<11}class St{fromJulianDay(e){return kt(this,Rt,e)}toJulianDay(e){return Ct(Rt,e.year,e.month,e.day)}getDaysInMonth(e){let t=29+e.month%2;return 12===e.month&&Mt(e.year)&&t++,t}getMonthsInYear(){return 12}getDaysInYear(e){return Mt(e.year)?355:354}getYearsInEra(){return 9665}getEras(){return["AH"]}constructor(){this.identifier="islamic-civil"}}class Dt extends St{fromJulianDay(e){return kt(this,xt,e)}toJulianDay(e){return Ct(xt,e.year,e.month,e.day)}constructor(...e){super(...e),this.identifier="islamic-tbla"}}const At="qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=";let Ot,Tt;function Pt(e){return Et+Tt[e-Bt]}function It(e,t){let n=e-Bt,o=1<<11-(t-1);return 0===(Ot[n]&o)?29:30}function Wt(e,t){let n=Pt(e);for(let o=1;o<t;o++)n+=It(e,o);return n}function Ft(e){return Tt[e+1-Bt]-Tt[e-Bt]}class $t extends St{fromJulianDay(e){let t=e-Rt,n=Pt(Bt),o=Pt(_t);if(t<n||t>o)return super.fromJulianDay(e);{let e=Bt-1,n=1,o=1;while(o>0){e++,o=t-Pt(e)+1;let a=Ft(e);if(o===a){n=12;break}if(o<a){let t=It(e,n);n=1;while(o>t)o-=t,n++,t=It(e,n);break}}return new Ve(this,e,n,t-Wt(e,n)+1)}}toJulianDay(e){return e.year<Bt||e.year>_t?super.toJulianDay(e):Rt+Wt(e.year,e.month)+(e.day-1)}getDaysInMonth(e){return e.year<Bt||e.year>_t?super.getDaysInMonth(e):It(e.year,e.month)}getDaysInYear(e){return e.year<Bt||e.year>_t?super.getDaysInYear(e):Ft(e.year)}constructor(){if(super(),this.identifier="islamic-umalqura",Ot||(Ot=new Uint16Array(Uint8Array.from(atob(At),e=>e.charCodeAt(0)).buffer)),!Tt){Tt=new Uint32Array(_t-Bt+1);let e=0;for(let t=Bt;t<=_t;t++){Tt[t-Bt]=e;for(let n=1;n<=12;n++)e+=It(t,n)}}}}const Nt=[[1868,9,8],[1912,7,30],[1926,12,25],[1989,1,8],[2019,5,1]],Vt=[[1912,7,29],[1926,12,24],[1989,1,7],[2019,4,30]],Lt=[1867,1911,1925,1988,2018],Kt=["meiji","taisho","showa","heisei","reiwa"];function jt(e){const t=Nt.findIndex(([t,n,o])=>e.year<t||(e.year===t&&e.month<n||e.year===t&&e.month===n&&e.day<o));return-1===t?Nt.length-1:0===t?0:t-1}function Gt(e){let t=Lt[Kt.indexOf(e.era)];if(!t)throw new Error("Unknown era: "+e.era);return new Ve(e.year+t,e.month,e.day)}class Xt extends B{fromJulianDay(e){let t=super.fromJulianDay(e),n=jt(t);return new Ve(this,Kt[n],t.year-Lt[n],t.month,t.day)}toJulianDay(e){return super.toJulianDay(Gt(e))}balanceDate(e){let t=Gt(e),n=jt(t);Kt[n]!==e.era&&(e.era=Kt[n],e.year=t.year-Lt[n]),this.constrainDate(e)}constrainDate(e){let t=Kt.indexOf(e.era),n=Vt[t];if(null!=n){let[o,a,r]=n,l=o-Lt[t];e.year=Math.max(1,Math.min(l,e.year)),e.year===l&&(e.month=Math.min(a,e.month),e.month===a&&(e.day=Math.min(r,e.day)))}if(1===e.year&&t>=0){let[,n,o]=Nt[t];e.month=Math.max(n,e.month),e.month===n&&(e.day=Math.max(o,e.day))}}getEras(){return Kt}getYearsInEra(e){let t=Kt.indexOf(e.era),n=Nt[t],o=Nt[t+1];if(null==o)return 9999-n[0]+1;let a=o[0]-n[0];return(e.month<o[1]||e.month===o[1]&&e.day<o[2])&&a++,a}getDaysInMonth(e){return super.getDaysInMonth(Gt(e))}getMinimumMonthInYear(e){let t=Ut(e);return t?t[1]:1}getMinimumDayInMonth(e){let t=Ut(e);return t&&e.month===t[1]?t[2]:1}constructor(...e){super(...e),this.identifier="japanese"}}function Ut(e){if(1===e.year){let t=Kt.indexOf(e.era);return Nt[t]}}const zt=1948320,Ht=[0,31,62,93,124,155,186,216,246,276,306,336];class Yt{fromJulianDay(e){let t=e-zt,n=1+Math.floor((33*t+3)/12053),o=365*(n-1)+Math.floor((8*n+21)/33),a=t-o,r=a<216?Math.floor(a/31):Math.floor((a-6)/30),l=a-Ht[r]+1;return new Ve(this,n,r+1,l)}toJulianDay(e){let t=zt-1+365*(e.year-1)+Math.floor((8*e.year+21)/33);return t+=Ht[e.month-1],t+=e.day,t}getMonthsInYear(){return 12}getDaysInMonth(e){if(e.month<=6)return 31;if(e.month<=11)return 30;let t=m(25*e.year+11,33)<8;return t?30:29}getEras(){return["AP"]}getYearsInEra(){return 9377}constructor(){this.identifier="persian"}}const Zt=1911;function qt(e){return"minguo"===e.era?e.year+Zt:1-e.year+Zt}function Jt(e){let t=e-Zt;return t>0?["minguo",t]:["before_minguo",1-t]}class Qt extends B{fromJulianDay(e){let t=super.fromJulianDay(e),n=w(t.era,t.year),[o,a]=Jt(n);return new Ve(this,o,a,t.month,t.day)}toJulianDay(e){return super.toJulianDay(en(e))}getEras(){return["before_minguo","minguo"]}balanceDate(e){let[t,n]=Jt(qt(e));e.era=t,e.year=n}isInverseEra(e){return"before_minguo"===e.era}getDaysInMonth(e){return super.getDaysInMonth(en(e))}getYearsInEra(e){return"before_minguo"===e.era?9999:9999-Zt}constructor(...e){super(...e),this.identifier="roc"}}function en(e){let[t,n]=R(qt(e));return new Ve(t,n,e.month,e.day)}function tn(e){switch(e){case"buddhist":return new Ue;case"ethiopic":return new tt;case"ethioaa":return new nt;case"coptic":return new ot;case"hebrew":return new yt;case"indian":return new wt;case"islamic-civil":return new St;case"islamic-tbla":return new Dt;case"islamic-umalqura":return new $t;case"japanese":return new Xt;case"persian":return new Yt;case"roc":return new Qt;case"gregory":default:return new B}}function nn(e,t){const n=[];for(let o=0;o<e.length;o+=t)n.push(e.slice(o,o+t));return n}function on(e,t=N()){return rn(e)?e.toDate():e.toDate(t)}function an(e){return e instanceof Ke}function rn(e){return e instanceof Ge}function ln(e){return an(e)||rn(e)}function sn(e){if(e instanceof Date){const t=e.getFullYear(),n=e.getMonth()+1;return new Date(t,n,0).getDate()}return e.set({day:100}).day}function un(e,t){return e.compare(t)<0}function dn(e,t){return e.compare(t)>0}function cn(e,t){return e.compare(t)<=0}function fn(e,t){return e.compare(t)>=0}function pn(e,t,n){return fn(e,t)&&cn(e,n)}function vn(e,t,n){return dn(e,t)&&un(e,n)}function hn(e,t,n){const o=O(e,n);return t>o?e.subtract({days:o+7-t}):t===o?e:e.subtract({days:o-t})}function mn(e,t,n){const o=O(e,n),a=0===t?6:t-1;return o===a?e:o>a?e.add({days:7-o+a}):e.add({days:a-o})}function yn(e,t,n,o){if(void 0===n&&void 0===o)return!0;let a=e.add({days:1});if(null!=o&&o(a)||null!=n&&n(a))return!1;const r=t;for(;a.compare(r)<0;)if(a=a.add({days:1}),null!=o&&o(a)||null!=n&&n(a))return!1;return!0}function gn(e,t){const n=[];let o=e.add({days:1});const a=t;for(;o.compare(a)<0;)n.push(o),o=o.add({days:1});return n}function bn(e){const{dateObj:t,weekStartsOn:n,fixedWeeks:o,locale:a}=e,r=sn(t),l=Array.from({length:r},(e,n)=>t.set({day:n+1})),i=V(t),s=L(t),u=hn(i,n,a),d=mn(s,n,a),c=gn(u.subtract({days:1}),i),f=gn(s,d.add({days:1})),p=c.length+l.length+f.length;if(o&&p<42){const e=42-p;let n=f[f.length-1];n||(n=L(t));const o=Array.from({length:e},(e,t)=>{const o=t+1;return n.add({days:o})});f.push(...o)}const v=c.concat(l,f),h=nn(v,7);return{value:t,cells:v,rows:h}}function wn(e){const{numberOfMonths:t,dateObj:n,...o}=e,a=[];if(!t||1===t)return a.push(bn({...o,dateObj:n})),a;a.push(bn({...o,dateObj:n}));for(let r=1;r<t;r++){const e=n.add({months:r});a.push(bn({...o,dateObj:e}))}return a}const Rn=["top","right","bottom","left"],xn=Math.min,Bn=Math.max,_n=Math.round,En=Math.floor,Cn=e=>({x:e,y:e}),kn={left:"right",right:"left",bottom:"top",top:"bottom"},Mn={start:"end",end:"start"};function Sn(e,t,n){return Bn(e,xn(t,n))}function Dn(e,t){return"function"===typeof e?e(t):e}function An(e){return e.split("-")[0]}function On(e){return e.split("-")[1]}function Tn(e){return"x"===e?"y":"x"}function Pn(e){return"y"===e?"height":"width"}const In=new Set(["top","bottom"]);function Wn(e){return In.has(An(e))?"y":"x"}function Fn(e){return Tn(Wn(e))}function $n(e,t,n){void 0===n&&(n=!1);const o=On(e),a=Fn(e),r=Pn(a);let l="x"===a?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return t.reference[r]>t.floating[r]&&(l=zn(l)),[l,zn(l)]}function Nn(e){const t=zn(e);return[Vn(e),t,Vn(t)]}function Vn(e){return e.replace(/start|end/g,e=>Mn[e])}const Ln=["left","right"],Kn=["right","left"],jn=["top","bottom"],Gn=["bottom","top"];function Xn(e,t,n){switch(e){case"top":case"bottom":return n?t?Kn:Ln:t?Ln:Kn;case"left":case"right":return t?jn:Gn;default:return[]}}function Un(e,t,n,o){const a=On(e);let r=Xn(An(e),"start"===n,o);return a&&(r=r.map(e=>e+"-"+a),t&&(r=r.concat(r.map(Vn)))),r}function zn(e){return e.replace(/left|right|bottom|top/g,e=>kn[e])}function Hn(e){return{top:0,right:0,bottom:0,left:0,...e}}function Yn(e){return"number"!==typeof e?Hn(e):{top:e,right:e,bottom:e,left:e}}function Zn(e){const{x:t,y:n,width:o,height:a}=e;return{width:o,height:a,top:n,left:t,right:t+o,bottom:n+a,x:t,y:n}}function qn(e,t,n){let{reference:o,floating:a}=e;const r=Wn(t),l=Fn(t),i=Pn(l),s=An(t),u="y"===r,d=o.x+o.width/2-a.width/2,c=o.y+o.height/2-a.height/2,f=o[i]/2-a[i]/2;let p;switch(s){case"top":p={x:d,y:o.y-a.height};break;case"bottom":p={x:d,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:c};break;case"left":p={x:o.x-a.width,y:c};break;default:p={x:o.x,y:o.y}}switch(On(t)){case"start":p[l]-=f*(n&&u?-1:1);break;case"end":p[l]+=f*(n&&u?-1:1);break}return p}const Jn=async(e,t,n)=>{const{placement:o="bottom",strategy:a="absolute",middleware:r=[],platform:l}=n,i=r.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:a}),{x:d,y:c}=qn(u,o,s),f=o,p={},v=0;for(let h=0;h<i.length;h++){const{name:n,fn:r}=i[h],{x:m,y,data:g,reset:b}=await r({x:d,y:c,initialPlacement:o,placement:f,strategy:a,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});d=null!=m?m:d,c=null!=y?y:c,p={...p,[n]:{...p[n],...g}},b&&v<=50&&(v++,"object"===typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:a}):b.rects),({x:d,y:c}=qn(u,f,s))),h=-1)}return{x:d,y:c,placement:f,strategy:a,middlewareData:p}};async function Qn(e,t){var n;void 0===t&&(t={});const{x:o,y:a,platform:r,rects:l,elements:i,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:c="floating",altBoundary:f=!1,padding:p=0}=Dn(t,e),v=Yn(p),h="floating"===c?"reference":"floating",m=i[f?h:c],y=Zn(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(m)))||n?m:m.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(i.floating)),boundary:u,rootBoundary:d,strategy:s})),g="floating"===c?{x:o,y:a,width:l.floating.width,height:l.floating.height}:l.reference,b=await(null==r.getOffsetParent?void 0:r.getOffsetParent(i.floating)),w=await(null==r.isElement?void 0:r.isElement(b))&&await(null==r.getScale?void 0:r.getScale(b))||{x:1,y:1},R=Zn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:g,offsetParent:b,strategy:s}):g);return{top:(y.top-R.top+v.top)/w.y,bottom:(R.bottom-y.bottom+v.bottom)/w.y,left:(y.left-R.left+v.left)/w.x,right:(R.right-y.right+v.right)/w.x}}const eo=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:a,rects:r,platform:l,elements:i,middlewareData:s}=t,{element:u,padding:d=0}=Dn(e,t)||{};if(null==u)return{};const c=Yn(d),f={x:n,y:o},p=Fn(a),v=Pn(p),h=await l.getDimensions(u),m="y"===p,y=m?"top":"left",g=m?"bottom":"right",b=m?"clientHeight":"clientWidth",w=r.reference[v]+r.reference[p]-f[p]-r.floating[v],R=f[p]-r.reference[p],x=await(null==l.getOffsetParent?void 0:l.getOffsetParent(u));let B=x?x[b]:0;B&&await(null==l.isElement?void 0:l.isElement(x))||(B=i.floating[b]||r.floating[v]);const _=w/2-R/2,E=B/2-h[v]/2-1,C=xn(c[y],E),k=xn(c[g],E),M=C,S=B-h[v]-k,D=B/2-h[v]/2+_,A=Sn(M,D,S),O=!s.arrow&&null!=On(a)&&D!==A&&r.reference[v]/2-(D<M?C:k)-h[v]/2<0,T=O?D<M?D-M:D-S:0;return{[p]:f[p]+T,data:{[p]:A,centerOffset:D-A-T,...O&&{alignmentOffset:T}},reset:O}}});const to=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:a,middlewareData:r,rects:l,initialPlacement:i,platform:s,elements:u}=t,{mainAxis:d=!0,crossAxis:c=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:h=!0,...m}=Dn(e,t);if(null!=(n=r.arrow)&&n.alignmentOffset)return{};const y=An(a),g=Wn(i),b=An(i)===i,w=await(null==s.isRTL?void 0:s.isRTL(u.floating)),R=f||(b||!h?[zn(i)]:Nn(i)),x="none"!==v;!f&&x&&R.push(...Un(i,h,v,w));const B=[i,...R],_=await Qn(t,m),E=[];let C=(null==(o=r.flip)?void 0:o.overflows)||[];if(d&&E.push(_[y]),c){const e=$n(a,l,w);E.push(_[e[0]],_[e[1]])}if(C=[...C,{placement:a,overflows:E}],!E.every(e=>e<=0)){var k,M;const e=((null==(k=r.flip)?void 0:k.index)||0)+1,t=B[e];if(t){const n="alignment"===c&&g!==Wn(t);if(!n||C.every(e=>e.overflows[0]>0&&Wn(e.placement)===g))return{data:{index:e,overflows:C},reset:{placement:t}}}let n=null==(M=C.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:M.placement;if(!n)switch(p){case"bestFit":{var S;const e=null==(S=C.filter(e=>{if(x){const t=Wn(e.placement);return t===g||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:S[0];e&&(n=e);break}case"initialPlacement":n=i;break}if(a!==n)return{reset:{placement:n}}}return{}}}};function no(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function oo(e){return Rn.some(t=>e[t]>=0)}const ao=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...a}=Dn(e,t);switch(o){case"referenceHidden":{const e=await Qn(t,{...a,elementContext:"reference"}),o=no(e,n.reference);return{data:{referenceHiddenOffsets:o,referenceHidden:oo(o)}}}case"escaped":{const e=await Qn(t,{...a,altBoundary:!0}),o=no(e,n.floating);return{data:{escapedOffsets:o,escaped:oo(o)}}}default:return{}}}}};const ro=new Set(["left","top"]);async function lo(e,t){const{placement:n,platform:o,elements:a}=e,r=await(null==o.isRTL?void 0:o.isRTL(a.floating)),l=An(n),i=On(n),s="y"===Wn(n),u=ro.has(l)?-1:1,d=r&&s?-1:1,c=Dn(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"===typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return i&&"number"===typeof v&&(p="end"===i?-1*v:v),s?{x:p*d,y:f*u}:{x:f*u,y:p*d}}const io=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:a,y:r,placement:l,middlewareData:i}=t,s=await lo(t,e);return l===(null==(n=i.offset)?void 0:n.placement)&&null!=(o=i.arrow)&&o.alignmentOffset?{}:{x:a+s.x,y:r+s.y,data:{...s,placement:l}}}}},so=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:a}=t,{mainAxis:r=!0,crossAxis:l=!1,limiter:i={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=Dn(e,t),u={x:n,y:o},d=await Qn(t,s),c=Wn(An(a)),f=Tn(c);let p=u[f],v=u[c];if(r){const e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+d[e],o=p-d[t];p=Sn(n,p,o)}if(l){const e="y"===c?"top":"left",t="y"===c?"bottom":"right",n=v+d[e],o=v-d[t];v=Sn(n,v,o)}const h=i.fn({...t,[f]:p,[c]:v});return{...h,data:{x:h.x-n,y:h.y-o,enabled:{[f]:r,[c]:l}}}}}},uo=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:a,rects:r,middlewareData:l}=t,{offset:i=0,mainAxis:s=!0,crossAxis:u=!0}=Dn(e,t),d={x:n,y:o},c=Wn(a),f=Tn(c);let p=d[f],v=d[c];const h=Dn(i,t),m="number"===typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){const e="y"===f?"height":"width",t=r.reference[f]-r.floating[e]+m.mainAxis,n=r.reference[f]+r.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var y,g;const e="y"===f?"width":"height",t=ro.has(An(a)),n=r.reference[c]-r.floating[e]+(t&&(null==(y=l.offset)?void 0:y[c])||0)+(t?0:m.crossAxis),o=r.reference[c]+r.reference[e]+(t?0:(null==(g=l.offset)?void 0:g[c])||0)-(t?m.crossAxis:0);v<n?v=n:v>o&&(v=o)}return{[f]:p,[c]:v}}}},co=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:a,rects:r,platform:l,elements:i}=t,{apply:s=()=>{},...u}=Dn(e,t),d=await Qn(t,u),c=An(a),f=On(a),p="y"===Wn(a),{width:v,height:h}=r.floating;let m,y;"top"===c||"bottom"===c?(m=c,y=f===(await(null==l.isRTL?void 0:l.isRTL(i.floating))?"start":"end")?"left":"right"):(y=c,m="end"===f?"top":"bottom");const g=h-d.top-d.bottom,b=v-d.left-d.right,w=xn(h-d[m],g),R=xn(v-d[y],b),x=!t.middlewareData.shift;let B=w,_=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(_=b),null!=(o=t.middlewareData.shift)&&o.enabled.y&&(B=g),x&&!f){const e=Bn(d.left,0),t=Bn(d.right,0),n=Bn(d.top,0),o=Bn(d.bottom,0);p?_=v-2*(0!==e||0!==t?e+t:Bn(d.left,d.right)):B=h-2*(0!==n||0!==o?n+o:Bn(d.top,d.bottom))}await s({...t,availableWidth:_,availableHeight:B});const E=await l.getDimensions(i.floating);return v!==E.width||h!==E.height?{reset:{rects:!0}}:{}}}};function fo(){return"undefined"!==typeof window}function po(e){return mo(e)?(e.nodeName||"").toLowerCase():"#document"}function vo(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ho(e){var t;return null==(t=(mo(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function mo(e){return!!fo()&&(e instanceof Node||e instanceof vo(e).Node)}function yo(e){return!!fo()&&(e instanceof Element||e instanceof vo(e).Element)}function go(e){return!!fo()&&(e instanceof HTMLElement||e instanceof vo(e).HTMLElement)}function bo(e){return!(!fo()||"undefined"===typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof vo(e).ShadowRoot)}const wo=new Set(["inline","contents"]);function Ro(e){const{overflow:t,overflowX:n,overflowY:o,display:a}=Po(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!wo.has(a)}const xo=new Set(["table","td","th"]);function Bo(e){return xo.has(po(e))}const _o=[":popover-open",":modal"];function Eo(e){return _o.some(t=>{try{return e.matches(t)}catch(n){return!1}})}const Co=["transform","translate","scale","rotate","perspective"],ko=["transform","translate","scale","rotate","perspective","filter"],Mo=["paint","layout","strict","content"];function So(e){const t=Ao(),n=yo(e)?Po(e):e;return Co.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ko.some(e=>(n.willChange||"").includes(e))||Mo.some(e=>(n.contain||"").includes(e))}function Do(e){let t=Wo(e);while(go(t)&&!To(t)){if(So(t))return t;if(Eo(t))return null;t=Wo(t)}return null}function Ao(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const Oo=new Set(["html","body","#document"]);function To(e){return Oo.has(po(e))}function Po(e){return vo(e).getComputedStyle(e)}function Io(e){return yo(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Wo(e){if("html"===po(e))return e;const t=e.assignedSlot||e.parentNode||bo(e)&&e.host||ho(e);return bo(t)?t.host:t}function Fo(e){const t=Wo(e);return To(t)?e.ownerDocument?e.ownerDocument.body:e.body:go(t)&&Ro(t)?t:Fo(t)}function $o(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);const a=Fo(e),r=a===(null==(o=e.ownerDocument)?void 0:o.body),l=vo(a);if(r){const e=No(l);return t.concat(l,l.visualViewport||[],Ro(a)?a:[],e&&n?$o(e):[])}return t.concat(a,$o(a,[],n))}function No(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Vo(e){const t=Po(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const a=go(e),r=a?e.offsetWidth:n,l=a?e.offsetHeight:o,i=_n(n)!==r||_n(o)!==l;return i&&(n=r,o=l),{width:n,height:o,$:i}}function Lo(e){return yo(e)?e:e.contextElement}function Ko(e){const t=Lo(e);if(!go(t))return Cn(1);const n=t.getBoundingClientRect(),{width:o,height:a,$:r}=Vo(t);let l=(r?_n(n.width):n.width)/o,i=(r?_n(n.height):n.height)/a;return l&&Number.isFinite(l)||(l=1),i&&Number.isFinite(i)||(i=1),{x:l,y:i}}const jo=Cn(0);function Go(e){const t=vo(e);return Ao()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:jo}function Xo(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==vo(e))&&t}function Uo(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const a=e.getBoundingClientRect(),r=Lo(e);let l=Cn(1);t&&(o?yo(o)&&(l=Ko(o)):l=Ko(e));const i=Xo(r,n,o)?Go(r):Cn(0);let s=(a.left+i.x)/l.x,u=(a.top+i.y)/l.y,d=a.width/l.x,c=a.height/l.y;if(r){const e=vo(r),t=o&&yo(o)?vo(o):o;let n=e,a=No(n);while(a&&o&&t!==n){const e=Ko(a),t=a.getBoundingClientRect(),o=Po(a),r=t.left+(a.clientLeft+parseFloat(o.paddingLeft))*e.x,l=t.top+(a.clientTop+parseFloat(o.paddingTop))*e.y;s*=e.x,u*=e.y,d*=e.x,c*=e.y,s+=r,u+=l,n=vo(a),a=No(n)}}return Zn({width:d,height:c,x:s,y:u})}function zo(e,t){const n=Io(e).scrollLeft;return t?t.left+n:Uo(ho(e)).left+n}function Ho(e,t,n){void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=o.left+t.scrollLeft-(n?0:zo(e,o)),r=o.top+t.scrollTop;return{x:a,y:r}}function Yo(e){let{elements:t,rect:n,offsetParent:o,strategy:a}=e;const r="fixed"===a,l=ho(o),i=!!t&&Eo(t.floating);if(o===l||i&&r)return n;let s={scrollLeft:0,scrollTop:0},u=Cn(1);const d=Cn(0),c=go(o);if((c||!c&&!r)&&(("body"!==po(o)||Ro(l))&&(s=Io(o)),go(o))){const e=Uo(o);u=Ko(o),d.x=e.x+o.clientLeft,d.y=e.y+o.clientTop}const f=!l||c||r?Cn(0):Ho(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+d.x+f.x,y:n.y*u.y-s.scrollTop*u.y+d.y+f.y}}function Zo(e){return Array.from(e.getClientRects())}function qo(e){const t=ho(e),n=Io(e),o=e.ownerDocument.body,a=Bn(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),r=Bn(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let l=-n.scrollLeft+zo(e);const i=-n.scrollTop;return"rtl"===Po(o).direction&&(l+=Bn(t.clientWidth,o.clientWidth)-a),{width:a,height:r,x:l,y:i}}function Jo(e,t){const n=vo(e),o=ho(e),a=n.visualViewport;let r=o.clientWidth,l=o.clientHeight,i=0,s=0;if(a){r=a.width,l=a.height;const e=Ao();(!e||e&&"fixed"===t)&&(i=a.offsetLeft,s=a.offsetTop)}return{width:r,height:l,x:i,y:s}}const Qo=new Set(["absolute","fixed"]);function ea(e,t){const n=Uo(e,!0,"fixed"===t),o=n.top+e.clientTop,a=n.left+e.clientLeft,r=go(e)?Ko(e):Cn(1),l=e.clientWidth*r.x,i=e.clientHeight*r.y,s=a*r.x,u=o*r.y;return{width:l,height:i,x:s,y:u}}function ta(e,t,n){let o;if("viewport"===t)o=Jo(e,n);else if("document"===t)o=qo(ho(e));else if(yo(t))o=ea(t,n);else{const n=Go(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Zn(o)}function na(e,t){const n=Wo(e);return!(n===t||!yo(n)||To(n))&&("fixed"===Po(n).position||na(n,t))}function oa(e,t){const n=t.get(e);if(n)return n;let o=$o(e,[],!1).filter(e=>yo(e)&&"body"!==po(e)),a=null;const r="fixed"===Po(e).position;let l=r?Wo(e):e;while(yo(l)&&!To(l)){const t=Po(l),n=So(l);n||"fixed"!==t.position||(a=null);const i=r?!n&&!a:!n&&"static"===t.position&&!!a&&Qo.has(a.position)||Ro(l)&&!n&&na(e,l);i?o=o.filter(e=>e!==l):a=t,l=Wo(l)}return t.set(e,o),o}function aa(e){let{element:t,boundary:n,rootBoundary:o,strategy:a}=e;const r="clippingAncestors"===n?Eo(t)?[]:oa(t,this._c):[].concat(n),l=[...r,o],i=l[0],s=l.reduce((e,n)=>{const o=ta(t,n,a);return e.top=Bn(o.top,e.top),e.right=xn(o.right,e.right),e.bottom=xn(o.bottom,e.bottom),e.left=Bn(o.left,e.left),e},ta(t,i,a));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}}function ra(e){const{width:t,height:n}=Vo(e);return{width:t,height:n}}function la(e,t,n){const o=go(t),a=ho(t),r="fixed"===n,l=Uo(e,!0,r,t);let i={scrollLeft:0,scrollTop:0};const s=Cn(0);function u(){s.x=zo(a)}if(o||!o&&!r)if(("body"!==po(t)||Ro(a))&&(i=Io(t)),o){const e=Uo(t,!0,r,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else a&&u();r&&!o&&a&&u();const d=!a||o||r?Cn(0):Ho(a,i),c=l.left+i.scrollLeft-s.x-d.x,f=l.top+i.scrollTop-s.y-d.y;return{x:c,y:f,width:l.width,height:l.height}}function ia(e){return"static"===Po(e).position}function sa(e,t){if(!go(e)||"fixed"===Po(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ho(e)===n&&(n=n.ownerDocument.body),n}function ua(e,t){const n=vo(e);if(Eo(e))return n;if(!go(e)){let t=Wo(e);while(t&&!To(t)){if(yo(t)&&!ia(t))return t;t=Wo(t)}return n}let o=sa(e,t);while(o&&Bo(o)&&ia(o))o=sa(o,t);return o&&To(o)&&ia(o)&&!So(o)?n:o||Do(e)||n}const da=async function(e){const t=this.getOffsetParent||ua,n=this.getDimensions,o=await n(e.floating);return{reference:la(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function ca(e){return"rtl"===Po(e).direction}const fa={convertOffsetParentRelativeRectToViewportRelativeRect:Yo,getDocumentElement:ho,getClippingRect:aa,getOffsetParent:ua,getElementRects:da,getClientRects:Zo,getDimensions:ra,getScale:Ko,isElement:yo,isRTL:ca};function pa(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function va(e,t){let n,o=null;const a=ho(e);function r(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}function l(i,s){void 0===i&&(i=!1),void 0===s&&(s=1),r();const u=e.getBoundingClientRect(),{left:d,top:c,width:f,height:p}=u;if(i||t(),!f||!p)return;const v=En(c),h=En(a.clientWidth-(d+f)),m=En(a.clientHeight-(c+p)),y=En(d),g=-v+"px "+-h+"px "+-m+"px "+-y+"px",b={rootMargin:g,threshold:Bn(0,xn(1,s))||1};let w=!0;function R(t){const o=t[0].intersectionRatio;if(o!==s){if(!w)return l();o?l(!1,o):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==o||pa(u,e.getBoundingClientRect())||l(),w=!1}try{o=new IntersectionObserver(R,{...b,root:a.ownerDocument})}catch(x){o=new IntersectionObserver(R,b)}o.observe(e)}return l(!0),r}function ha(e,t,n,o){void 0===o&&(o={});const{ancestorScroll:a=!0,ancestorResize:r=!0,elementResize:l="function"===typeof ResizeObserver,layoutShift:i="function"===typeof IntersectionObserver,animationFrame:s=!1}=o,u=Lo(e),d=a||r?[...u?$o(u):[],...$o(t)]:[];d.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),r&&e.addEventListener("resize",n)});const c=u&&i?va(u,n):null;let f,p=-1,v=null;l&&(v=new ResizeObserver(e=>{let[o]=e;o&&o.target===u&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),u&&!s&&v.observe(u),v.observe(t));let h=s?Uo(e):null;function m(){const t=Uo(e);h&&!pa(h,t)&&n(),h=t,f=requestAnimationFrame(m)}return s&&m(),n(),()=>{var e;d.forEach(e=>{a&&e.removeEventListener("scroll",n),r&&e.removeEventListener("resize",n)}),null==c||c(),null==(e=v)||e.disconnect(),v=null,s&&cancelAnimationFrame(f)}}const ma=io,ya=so,ga=to,ba=co,wa=ao,Ra=eo,xa=uo,Ba=(e,t,n)=>{const o=new Map,a={platform:fa,...n},r={...a.platform,_c:o};return Jn(e,t,{...a,platform:r})};function _a(e){return null!=e&&"object"===typeof e&&"$el"in e}function Ea(e){if(_a(e)){const t=e.$el;return mo(t)&&"#comment"===po(t)?null:t}return e}function Ca(e){return"function"===typeof e?e():(0,a.R1)(e)}function ka(e){return{name:"arrow",options:e,fn(t){const n=Ea(Ca(e.element));return null==n?{}:Ra({element:n,padding:e.padding}).fn(t)}}}function Ma(e){if("undefined"===typeof window)return 1;const t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function Sa(e,t){const n=Ma(e);return Math.round(t*n)/n}function Da(e,t,n){void 0===n&&(n={});const r=n.whileElementsMounted,l=(0,o.EW)(()=>{var e;return null==(e=Ca(n.open))||e}),i=(0,o.EW)(()=>Ca(n.middleware)),s=(0,o.EW)(()=>{var e;return null!=(e=Ca(n.placement))?e:"bottom"}),u=(0,o.EW)(()=>{var e;return null!=(e=Ca(n.strategy))?e:"absolute"}),d=(0,o.EW)(()=>{var e;return null==(e=Ca(n.transform))||e}),c=(0,o.EW)(()=>Ea(e.value)),f=(0,o.EW)(()=>Ea(t.value)),p=(0,a.KR)(0),v=(0,a.KR)(0),h=(0,a.KR)(u.value),m=(0,a.KR)(s.value),y=(0,a.IJ)({}),g=(0,a.KR)(!1),b=(0,o.EW)(()=>{const e={position:h.value,left:"0",top:"0"};if(!f.value)return e;const t=Sa(f.value,p.value),n=Sa(f.value,v.value);return d.value?{...e,transform:"translate("+t+"px, "+n+"px)",...Ma(f.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:t+"px",top:n+"px"}});let w;function R(){if(null==c.value||null==f.value)return;const e=l.value;Ba(c.value,f.value,{middleware:i.value,placement:s.value,strategy:u.value}).then(t=>{p.value=t.x,v.value=t.y,h.value=t.strategy,m.value=t.placement,y.value=t.middlewareData,g.value=!1!==e})}function x(){"function"===typeof w&&(w(),w=void 0)}function B(){x(),void 0!==r?null==c.value||null==f.value||(w=r(c.value,f.value,R)):R()}function _(){l.value||(g.value=!1)}return(0,o.wB)([i,s,u,l],R,{flush:"sync"}),(0,o.wB)([c,f],B,{flush:"sync"}),(0,o.wB)(l,_,{flush:"sync"}),(0,a.o5)()&&(0,a.jr)(x),{x:(0,a.nD)(p),y:(0,a.nD)(v),strategy:(0,a.nD)(h),placement:(0,a.nD)(m),middlewareData:(0,a.nD)(y),isPositioned:(0,a.nD)(g),floatingStyles:b,update:R}}let Aa=new Map,Oa=!1;try{Oa="exceptZero"===new Intl.NumberFormat("de-DE",{signDisplay:"exceptZero"}).resolvedOptions().signDisplay}catch{}let Ta=!1;try{Ta="unit"===new Intl.NumberFormat("de-DE",{style:"unit",unit:"degree"}).resolvedOptions().style}catch{}const Pa={degree:{narrow:{default:"°","ja-JP":" 度","zh-TW":"度","sl-SI":" °"}}};class Ia{format(e){let t="";if(t=Oa||null==this.options.signDisplay?this.numberFormatter.format(e):Fa(this.numberFormatter,this.options.signDisplay,e),"unit"===this.options.style&&!Ta){var n;let{unit:e,unitDisplay:o="short",locale:a}=this.resolvedOptions();if(!e)return t;let r=null===(n=Pa[e])||void 0===n?void 0:n[o];t+=r[a]||r.default}return t}formatToParts(e){return this.numberFormatter.formatToParts(e)}formatRange(e,t){if("function"===typeof this.numberFormatter.formatRange)return this.numberFormatter.formatRange(e,t);if(t<e)throw new RangeError("End date must be >= start date");return`${this.format(e)} – ${this.format(t)}`}formatRangeToParts(e,t){if("function"===typeof this.numberFormatter.formatRangeToParts)return this.numberFormatter.formatRangeToParts(e,t);if(t<e)throw new RangeError("End date must be >= start date");let n=this.numberFormatter.formatToParts(e),o=this.numberFormatter.formatToParts(t);return[...n.map(e=>({...e,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...o.map(e=>({...e,source:"endRange"}))]}resolvedOptions(){let e=this.numberFormatter.resolvedOptions();return Oa||null==this.options.signDisplay||(e={...e,signDisplay:this.options.signDisplay}),Ta||"unit"!==this.options.style||(e={...e,style:"unit",unit:this.options.unit,unitDisplay:this.options.unitDisplay}),e}constructor(e,t={}){this.numberFormatter=Wa(e,t),this.options=t}}function Wa(e,t={}){let{numberingSystem:n}=t;if(n&&e.includes("-nu-")&&(e.includes("-u-")||(e+="-u-"),e+=`-nu-${n}`),"unit"===t.style&&!Ta){var o;let{unit:e,unitDisplay:n="short"}=t;if(!e)throw new Error('unit option must be provided with style: "unit"');if(!(null===(o=Pa[e])||void 0===o?void 0:o[n]))throw new Error(`Unsupported unit ${e} with unitDisplay = ${n}`);t={...t,style:"decimal"}}let a=e+(t?Object.entries(t).sort((e,t)=>e[0]<t[0]?-1:1).join():"");if(Aa.has(a))return Aa.get(a);let r=new Intl.NumberFormat(e,t);return Aa.set(a,r),r}function Fa(e,t,n){if("auto"===t)return e.format(n);if("never"===t)return e.format(Math.abs(n));{let o=!1;if("always"===t?o=n>0||Object.is(n,0):"exceptZero"===t&&(Object.is(n,-0)||Object.is(n,0)?n=Math.abs(n):o=n>0),o){let t=e.format(-n),o=e.format(n),a=t.replace(o,"").replace(/\u200e|\u061C/,"");1!==[...a].length&&console.warn("@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case");let r=t.replace(o,"!!!").replace(a,"+").replace("!!!",o);return r}return e.format(n)}}const $a=new RegExp("^.*\\(.*\\).*$"),Na=["latn","arab","hanidec","deva","beng"];class Va{parse(e){return Ka(this.locale,this.options,e).parse(e)}isValidPartialNumber(e,t,n){return Ka(this.locale,this.options,e).isValidPartialNumber(e,t,n)}getNumberingSystem(e){return Ka(this.locale,this.options,e).options.numberingSystem}constructor(e,t={}){this.locale=e,this.options=t}}const La=new Map;function Ka(e,t,n){let o=ja(e,t);if(!e.includes("-nu-")&&!o.isValidPartialNumber(n))for(let a of Na)if(a!==o.options.numberingSystem){let o=ja(e+(e.includes("-u-")?"-nu-":"-u-nu-")+a,t);if(o.isValidPartialNumber(n))return o}return o}function ja(e,t){let n=e+(t?Object.entries(t).sort((e,t)=>e[0]<t[0]?-1:1).join():""),o=La.get(n);return o||(o=new Ga(e,t),La.set(n,o)),o}class Ga{parse(e){let t=this.sanitize(e);if(this.symbols.group&&(t=Ha(t,this.symbols.group,"")),this.symbols.decimal&&(t=t.replace(this.symbols.decimal,".")),this.symbols.minusSign&&(t=t.replace(this.symbols.minusSign,"-")),t=t.replace(this.symbols.numeral,this.symbols.index),"percent"===this.options.style){let e=t.indexOf("-");t=t.replace("-",""),t=t.replace("+","");let n=t.indexOf(".");-1===n&&(n=t.length),t=t.replace(".",""),t=n-2===0?`0.${t}`:n-2===-1?`0.0${t}`:n-2===-2?"0.00":`${t.slice(0,n-2)}.${t.slice(n-2)}`,e>-1&&(t=`-${t}`)}let n=t?+t:NaN;if(isNaN(n))return NaN;if("percent"===this.options.style){var o,a;let e={...this.options,style:"decimal",minimumFractionDigits:Math.min((null!==(o=this.options.minimumFractionDigits)&&void 0!==o?o:0)+2,20),maximumFractionDigits:Math.min((null!==(a=this.options.maximumFractionDigits)&&void 0!==a?a:0)+2,20)};return new Va(this.locale,e).parse(new Ia(this.locale,e).format(n))}return"accounting"===this.options.currencySign&&$a.test(e)&&(n*=-1),n}sanitize(e){return e=e.replace(this.symbols.literals,""),this.symbols.minusSign&&(e=e.replace("-",this.symbols.minusSign)),"arab"===this.options.numberingSystem&&(this.symbols.decimal&&(e=e.replace(",",this.symbols.decimal),e=e.replace(String.fromCharCode(1548),this.symbols.decimal)),this.symbols.group&&(e=Ha(e,".",this.symbols.group))),"fr-FR"===this.options.locale&&this.symbols.group&&(e=Ha(e," ",this.symbols.group),e=Ha(e,/\u00A0/g,this.symbols.group)),e}isValidPartialNumber(e,t=-1/0,n=1/0){return e=this.sanitize(e),this.symbols.minusSign&&e.startsWith(this.symbols.minusSign)&&t<0?e=e.slice(this.symbols.minusSign.length):this.symbols.plusSign&&e.startsWith(this.symbols.plusSign)&&n>0&&(e=e.slice(this.symbols.plusSign.length)),(!this.symbols.group||!e.startsWith(this.symbols.group))&&(!(this.symbols.decimal&&e.indexOf(this.symbols.decimal)>-1&&0===this.options.maximumFractionDigits)&&(this.symbols.group&&(e=Ha(e,this.symbols.group,"")),e=e.replace(this.symbols.numeral,""),this.symbols.decimal&&(e=e.replace(this.symbols.decimal,"")),0===e.length))}constructor(e,t={}){var n,o;this.locale=e,1!==t.roundingIncrement&&null!=t.roundingIncrement&&(null==t.maximumFractionDigits&&null==t.minimumFractionDigits?(t.maximumFractionDigits=0,t.minimumFractionDigits=0):null==t.maximumFractionDigits?t.maximumFractionDigits=t.minimumFractionDigits:null==t.minimumFractionDigits&&(t.minimumFractionDigits=t.maximumFractionDigits)),this.formatter=new Intl.NumberFormat(e,t),this.options=this.formatter.resolvedOptions(),this.symbols=za(e,this.formatter,this.options,t),"percent"===this.options.style&&((null!==(n=this.options.minimumFractionDigits)&&void 0!==n?n:0)>18||(null!==(o=this.options.maximumFractionDigits)&&void 0!==o?o:0)>18)&&console.warn("NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.")}}const Xa=new Set(["decimal","fraction","integer","minusSign","plusSign","group"]),Ua=[0,4,2,1,11,20,3,7,100,21,.1,1.1];function za(e,t,n,o){var a,r,l,i;let s=new Intl.NumberFormat(e,{...n,minimumSignificantDigits:1,maximumSignificantDigits:21,roundingIncrement:1,roundingPriority:"auto",roundingMode:"halfExpand"}),u=s.formatToParts(-10000.111),d=s.formatToParts(10000.111),c=Ua.map(e=>s.formatToParts(e));var f;let p=null!==(f=null===(a=u.find(e=>"minusSign"===e.type))||void 0===a?void 0:a.value)&&void 0!==f?f:"-",v=null===(r=d.find(e=>"plusSign"===e.type))||void 0===r?void 0:r.value;v||"exceptZero"!==(null===o||void 0===o?void 0:o.signDisplay)&&"always"!==(null===o||void 0===o?void 0:o.signDisplay)||(v="+");let h=new Intl.NumberFormat(e,{...n,minimumFractionDigits:2,maximumFractionDigits:2}).formatToParts(.001),m=null===(l=h.find(e=>"decimal"===e.type))||void 0===l?void 0:l.value,y=null===(i=u.find(e=>"group"===e.type))||void 0===i?void 0:i.value,g=u.filter(e=>!Xa.has(e.type)).map(e=>Ya(e.value)),b=c.flatMap(e=>e.filter(e=>!Xa.has(e.type)).map(e=>Ya(e.value))),w=[...new Set([...g,...b])].sort((e,t)=>t.length-e.length),R=0===w.length?new RegExp("[\\p{White_Space}]","gu"):new RegExp(`${w.join("|")}|[\\p{White_Space}]`,"gu"),x=[...new Intl.NumberFormat(n.locale,{useGrouping:!1}).format(9876543210)].reverse(),B=new Map(x.map((e,t)=>[e,t])),_=new RegExp(`[${x.join("")}]`,"g"),E=e=>String(B.get(e));return{minusSign:p,plusSign:v,decimal:m,group:y,literals:R,numeral:_,index:E}}function Ha(e,t,n){return e.replaceAll?e.replaceAll(t,n):e.split(t).join(n)}function Ya(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Za(e,t){const n="string"!=typeof e||t?t:`${e}Context`,a=Symbol(n);return[t=>{const n=(0,o.WQ)(a,t);if(n||null===n)return n;throw new Error(`Injection \`${a.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(", ")}`:`\`${e}\``}`)},e=>((0,o.Gt)(a,e),e)]}function qa(e,t,n){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),o.dispatchEvent(a)}function Ja(e,t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY){return Math.min(n,Math.max(t,e))}function Qa(e,t){let n=e;const o=t.toString(),a=o.indexOf("."),r=a>=0?o.length-a:0;if(r>0){const e=10**r;n=Math.round(n*e)/e}return n}function er(e,t,n,o){t=Number(t),n=Number(n);const a=(e-(Number.isNaN(t)?0:t))%o;let r=Qa(2*Math.abs(a)>=o?e+Math.sign(a)*(o-Math.abs(a)):e-a,o);return Number.isNaN(t)?!Number.isNaN(n)&&r>n&&(r=Math.floor(Qa(n/o,o))*o):r<t?r=t:!Number.isNaN(n)&&r>n&&(r=t+Math.floor(Qa((n-t)/o,o))*o),r=Qa(r,o),r}function tr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var nr=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var o,a,r;if(Array.isArray(t)){if(o=t.length,o!=n.length)return!1;for(a=o;0!==a--;)if(!e(t[a],n[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(r=Object.keys(t),o=r.length,o!==Object.keys(n).length)return!1;for(a=o;0!==a--;)if(!Object.prototype.hasOwnProperty.call(n,r[a]))return!1;for(a=o;0!==a--;){var l=r[a];if(!e(t[l],n[l]))return!1}return!0}return t!==t&&n!==n};const or=tr(nr);function ar(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function rr(e,t,n){const o=e.findIndex(e=>or(e,t)),a=e.findIndex(e=>or(e,n));if(-1===o||-1===a)return[];const[r,l]=[o,a].sort((e,t)=>e-t);return e.slice(r,l+1)}const lr=typeof document<"u";function ir(e){return null==e}function sr(e){const{defaultValue:t,defaultPlaceholder:n,granularity:o="day",locale:a="en"}=e;if(Array.isArray(t)&&t.length)return t.at(-1).copy();if(t&&!Array.isArray(t))return t.copy();if(n)return n.copy();const r=new Date,l=r.getFullYear(),i=r.getMonth()+1,u=r.getDate(),d=["hour","minute","second"],c=new s(a),f=tn(c.resolvedOptions().calendar);return d.includes(o??"day")?ae(new Ke(l,i,u,0,0,0),f):ae(new Ve(l,i,u),f)}const ur=["ach","af","am","an","ar","ast","az","be","bg","bn","br","bs","ca","cak","ckb","cs","cy","da","de","dsb","el","en","eo","es","et","eu","fa","ff","fi","fr","fy","ga","gd","gl","he","hr","hsb","hu","ia","id","it","ja","ka","kk","kn","ko","lb","lo","lt","lv","meh","ml","ms","nl","nn","no","oc","pl","pt","rm","ro","ru","sc","scn","sk","sl","sr","sv","szl","tg","th","tr","uk","zh-CN","zh-TW"],dr=["year","month","day"],cr={ach:{year:"mwaka",month:"dwe",day:"nino"},af:{year:"jjjj",month:"mm",day:"dd"},am:{year:"ዓዓዓዓ",month:"ሚሜ",day:"ቀቀ"},an:{year:"aaaa",month:"mm",day:"dd"},ar:{year:"سنة",month:"شهر",day:"يوم"},ast:{year:"aaaa",month:"mm",day:"dd"},az:{year:"iiii",month:"aa",day:"gg"},be:{year:"гггг",month:"мм",day:"дд"},bg:{year:"гггг",month:"мм",day:"дд"},bn:{year:"yyyy",month:"মিমি",day:"dd"},br:{year:"bbbb",month:"mm",day:"dd"},bs:{year:"gggg",month:"mm",day:"dd"},ca:{year:"aaaa",month:"mm",day:"dd"},cak:{year:"jjjj",month:"ii",day:"q'q'"},ckb:{year:"ساڵ",month:"مانگ",day:"ڕۆژ"},cs:{year:"rrrr",month:"mm",day:"dd"},cy:{year:"bbbb",month:"mm",day:"dd"},da:{year:"åååå",month:"mm",day:"dd"},de:{year:"jjjj",month:"mm",day:"tt"},dsb:{year:"llll",month:"mm",day:"źź"},el:{year:"εεεε",month:"μμ",day:"ηη"},en:{year:"yyyy",month:"mm",day:"dd"},eo:{year:"jjjj",month:"mm",day:"tt"},es:{year:"aaaa",month:"mm",day:"dd"},et:{year:"aaaa",month:"kk",day:"pp"},eu:{year:"uuuu",month:"hh",day:"ee"},fa:{year:"سال",month:"ماه",day:"روز"},ff:{year:"hhhh",month:"ll",day:"ññ"},fi:{year:"vvvv",month:"kk",day:"pp"},fr:{year:"aaaa",month:"mm",day:"jj"},fy:{year:"jjjj",month:"mm",day:"dd"},ga:{year:"bbbb",month:"mm",day:"ll"},gd:{year:"bbbb",month:"mm",day:"ll"},gl:{year:"aaaa",month:"mm",day:"dd"},he:{year:"שנה",month:"חודש",day:"יום"},hr:{year:"gggg",month:"mm",day:"dd"},hsb:{year:"llll",month:"mm",day:"dd"},hu:{year:"éééé",month:"hh",day:"nn"},ia:{year:"aaaa",month:"mm",day:"dd"},id:{year:"tttt",month:"bb",day:"hh"},it:{year:"aaaa",month:"mm",day:"gg"},ja:{year:" 年 ",month:"月",day:"日"},ka:{year:"წწწწ",month:"თთ",day:"რრ"},kk:{year:"жжжж",month:"аа",day:"кк"},kn:{year:"ವವವವ",month:"ಮಿಮೀ",day:"ದಿದಿ"},ko:{year:"연도",month:"월",day:"일"},lb:{year:"jjjj",month:"mm",day:"dd"},lo:{year:"ປປປປ",month:"ດດ",day:"ວວ"},lt:{year:"mmmm",month:"mm",day:"dd"},lv:{year:"gggg",month:"mm",day:"dd"},meh:{year:"aaaa",month:"mm",day:"dd"},ml:{year:"വർഷം",month:"മാസം",day:"തീയതി"},ms:{year:"tttt",month:"mm",day:"hh"},nl:{year:"jjjj",month:"mm",day:"dd"},nn:{year:"åååå",month:"mm",day:"dd"},no:{year:"åååå",month:"mm",day:"dd"},oc:{year:"aaaa",month:"mm",day:"jj"},pl:{year:"rrrr",month:"mm",day:"dd"},pt:{year:"aaaa",month:"mm",day:"dd"},rm:{year:"oooo",month:"mm",day:"dd"},ro:{year:"aaaa",month:"ll",day:"zz"},ru:{year:"гггг",month:"мм",day:"дд"},sc:{year:"aaaa",month:"mm",day:"dd"},scn:{year:"aaaa",month:"mm",day:"jj"},sk:{year:"rrrr",month:"mm",day:"dd"},sl:{year:"llll",month:"mm",day:"dd"},sr:{year:"гггг",month:"мм",day:"дд"},sv:{year:"åååå",month:"mm",day:"dd"},szl:{year:"rrrr",month:"mm",day:"dd"},tg:{year:"сссс",month:"мм",day:"рр"},th:{year:"ปปปป",month:"ดด",day:"วว"},tr:{year:"yyyy",month:"aa",day:"gg"},uk:{year:"рррр",month:"мм",day:"дд"},"zh-CN":{year:"年",month:"月",day:"日"},"zh-TW":{year:"年",month:"月",day:"日"}};function fr(e){if(vr(e))return cr[e];{const t=gr(e);return vr(t)?cr[t]:cr.en}}function pr(e,t,n){return hr(e)?fr(n)[e]:yr(e)?t:mr(e)?"––":""}function vr(e){return ur.includes(e)}function hr(e){return dr.includes(e)}function mr(e){return"hour"===e||"minute"===e||"second"===e}function yr(e){return"era"===e||"dayPeriod"===e}function gr(e){return Intl.Locale?new Intl.Locale(e).language:e.split("-")[0]}const br=["day","month","year"],wr=["hour","minute","second","dayPeriod"],Rr=[...br,...wr];function xr(e){return br.includes(e)}function Br(e){return Rr.includes(e)}function _r(e,t){const n={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:"short",hourCycle:24===t?"h24":void 0,hour12:24!==t&&void 0};return"day"===e&&(delete n.second,delete n.hour,delete n.minute,delete n.timeZoneName),"hour"===e&&(delete n.minute,delete n.second),"minute"===e&&delete n.second,n}function Er(e){const t=e.querySelector("[data-selected]");if(t)return t.focus();const n=e.querySelector("[data-today]");if(n)return n.focus();const o=e.querySelector("[data-radix-vue-calendar-day]");return o?o.focus():void 0}function Cr(e,t){var n;const r=(0,a.IJ)();return(0,o.nT)(()=>{r.value=e()},{...t,flush:null!=(n=void 0)?n:"sync"}),(0,a.tB)(r)}function kr(e){return!!(0,a.o5)()&&((0,a.jr)(e),!0)}function Mr(){const e=new Set,t=t=>{e.delete(t)};return{on:n=>{e.add(n);const o=()=>t(n);return kr(o),{off:o}},off:t,trigger:(...t)=>Promise.all(Array.from(e).map(e=>e(...t)))}}function Sr(e){let t,n=!1;const o=(0,a.uY)(!0);return(...a)=>(n||(t=o.run(()=>e(...a)),n=!0),t)}function Dr(e){let t,n,o=0;const r=()=>{o-=1,n&&o<=0&&(n.stop(),t=void 0,n=void 0)};return(...l)=>(o+=1,t||(n=(0,a.uY)(!0),t=n.run(()=>e(...l))),kr(r),t)}function Ar(e){return"function"==typeof e?e():(0,a.R1)(e)}function Or(e){if(!(0,a.i9)(e))return(0,a.Kh)(e);const t=new Proxy({},{get(t,n,o){return(0,a.R1)(Reflect.get(e.value,n,o))},set(t,n,o){return(0,a.i9)(e.value[n])&&!(0,a.i9)(o)?e.value[n].value=o:e.value[n]=o,!0},deleteProperty(t,n){return Reflect.deleteProperty(e.value,n)},has(t,n){return Reflect.has(e.value,n)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return(0,a.Kh)(t)}function Tr(e){return Or((0,o.EW)(e))}const Pr=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&(globalThis,WorkerGlobalScope);const Ir=e=>typeof e<"u",Wr=e=>null!=e,Fr=Object.prototype.toString,$r=e=>"[object Object]"===Fr.call(e),Nr=()=>{},Vr=Lr();function Lr(){var e,t;return Pr&&(null==(e=null==window?void 0:window.navigator)?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||(null==(t=null==window?void 0:window.navigator)?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(null==window?void 0:window.navigator.userAgent))}function Kr(e,t){function n(...n){return new Promise((o,a)=>{Promise.resolve(e(()=>t.apply(this,n),{fn:t,thisArg:this,args:n})).then(o).catch(a)})}return n}const jr=e=>e();function Gr(e,t={}){let n,o,a=Nr;const r=e=>{clearTimeout(e),a(),a=Nr};return l=>{const i=Ar(e),s=Ar(t.maxWait);return n&&r(n),i<=0||void 0!==s&&s<=0?(o&&(r(o),o=null),Promise.resolve(l())):new Promise((e,u)=>{a=t.rejectOnCancel?u:e,s&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,e(l())},s)),n=setTimeout(()=>{o&&r(o),o=null,e(l())},i)})}}function Xr(e=jr){const t=(0,a.KR)(!0);function n(){t.value=!1}function o(){t.value=!0}const r=(...n)=>{t.value&&e(...n)};return{isActive:(0,a.tB)(t),pause:n,resume:o,eventFilter:r}}function Ur(e){return(0,o.nI)()}function zr(e,t=1e4){return(0,a.rY)((n,o)=>{let a,r=Ar(e);const l=()=>setTimeout(()=>{r=Ar(e),o()},Ar(t));return kr(()=>{clearTimeout(a)}),{get(){return n(),r},set(e){r=e,o(),clearTimeout(a),a=l()}}})}function Hr(e,t=200,n={}){return Kr(Gr(t,n),e)}function Yr(e,t,n={}){const{eventFilter:a=jr,...r}=n;return(0,o.wB)(e,Kr(a,t),r)}function Zr(e,t,n={}){const{eventFilter:o,...a}=n,{eventFilter:r,pause:l,resume:i,isActive:s}=Xr(o);return{stop:Yr(e,t,{...a,eventFilter:r}),pause:l,resume:i,isActive:s}}function qr(e,t,...[n]){const{flush:o="sync",deep:a=!1,immediate:r=!0,direction:l="both",transform:i={}}=n||{},s=[],u="ltr"in i&&i.ltr||(e=>e),d="rtl"in i&&i.rtl||(e=>e);return("both"===l||"ltr"===l)&&s.push(Zr(e,e=>{s.forEach(e=>e.pause()),t.value=u(e),s.forEach(e=>e.resume())},{flush:o,deep:a,immediate:r})),("both"===l||"rtl"===l)&&s.push(Zr(t,t=>{s.forEach(e=>e.pause()),e.value=d(t),s.forEach(e=>e.resume())},{flush:o,deep:a,immediate:r})),()=>{s.forEach(e=>e.stop())}}function Jr(e,t){Ur()&&(0,o.xo)(e,t)}function Qr(e,t,n={}){const{immediate:o=!0}=n,r=(0,a.KR)(!1);let l=null;function i(){l&&(clearTimeout(l),l=null)}function s(){r.value=!1,i()}function u(...n){i(),r.value=!0,l=setTimeout(()=>{r.value=!1,l=null,e(...n)},Ar(t))}return o&&(r.value=!0,Pr&&u()),kr(s),{isPending:(0,a.tB)(r),start:u,stop:s}}function el(e=1e3,t={}){const{controls:n=!1,callback:a}=t,r=Qr(a??Nr,e,t),l=(0,o.EW)(()=>!r.isPending.value);return n?{ready:l,...r}:l}function tl(e,t,n){const a=(0,o.wB)(e,(...e)=>((0,o.dY)(()=>a()),t(...e)),n);return a}function nl(e){var t;const n=Ar(e);return null!=(t=null==n?void 0:n.$el)?t:n}const ol=Pr?window:void 0;function al(...e){let t,n,a,r;if("string"==typeof e[0]||Array.isArray(e[0])?([n,a,r]=e,t=ol):[t,n,a,r]=e,!t)return Nr;Array.isArray(n)||(n=[n]),Array.isArray(a)||(a=[a]);const l=[],i=()=>{l.forEach(e=>e()),l.length=0},s=(e,t,n,o)=>(e.addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)),u=(0,o.wB)(()=>[nl(t),Ar(r)],([e,t])=>{if(i(),!e)return;const o=$r(t)?{...t}:t;l.push(...n.flatMap(t=>a.map(n=>s(e,t,n,o))))},{immediate:!0,flush:"post"}),d=()=>{u(),i()};return kr(d),d}function rl(e){return"function"==typeof e?e:"string"==typeof e?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ll(...e){let t,n,o={};3===e.length?(t=e[0],n=e[1],o=e[2]):2===e.length?"object"==typeof e[1]?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:a=ol,eventName:r="keydown",passive:l=!1,dedupe:i=!1}=o,s=rl(t);return al(a,r,e=>{e.repeat&&Ar(i)||s(e)&&n(e)},l)}function il(){const e=(0,a.KR)(!1),t=(0,o.nI)();return t&&(0,o.sV)(()=>{e.value=!0},t),e}function sl(e){const t=il();return(0,o.EW)(()=>(t.value,!!e()))}function ul(e,t,n={}){const{window:a=ol,...r}=n;let l;const i=sl(()=>a&&"MutationObserver"in a),s=()=>{l&&(l.disconnect(),l=void 0)},u=(0,o.EW)(()=>{const t=Ar(e),n=(Array.isArray(t)?t:[t]).map(nl).filter(Wr);return new Set(n)}),d=(0,o.wB)(()=>u.value,e=>{s(),i.value&&e.size&&(l=new MutationObserver(t),e.forEach(e=>l.observe(e,r)))},{immediate:!0,flush:"post"}),c=()=>null==l?void 0:l.takeRecords(),f=()=>{s(),d()};return kr(f),{isSupported:i,stop:f,takeRecords:c}}function dl(e={}){var t;const{window:n=ol,deep:o=!0,triggerOnRemoval:r=!1}=e,l=null!=(t=e.document)?t:null==n?void 0:n.document,i=()=>{var e;let t=null==l?void 0:l.activeElement;if(o)for(;null!=t&&t.shadowRoot;)t=null==(e=null==t?void 0:t.shadowRoot)?void 0:e.activeElement;return t},s=(0,a.KR)(),u=()=>{s.value=i()};return n&&(al(n,"blur",e=>{null===e.relatedTarget&&u()},!0),al(n,"focus",u,!0)),r&&ul(l,e=>{e.filter(e=>e.removedNodes.length).map(e=>Array.from(e.removedNodes)).flat().forEach(e=>{e===s.value&&u()})},{childList:!0,subtree:!0}),u(),s}function cl(e,t={}){const{immediate:n=!0,fpsLimit:o,window:r=ol}=t,l=(0,a.KR)(!1),i=o?1e3/o:null;let s=0,u=null;function d(t){if(!l.value||!r)return;s||(s=t);const n=t-s;i&&n<i||(s=t,e({delta:n,timestamp:t})),u=r.requestAnimationFrame(d)}function c(){!l.value&&r&&(l.value=!0,s=0,u=r.requestAnimationFrame(d))}function f(){l.value=!1,null!=u&&r&&(r.cancelAnimationFrame(u),u=null)}return n&&c(),kr(f),{isActive:(0,a.tB)(l),pause:f,resume:c}}function fl(e){return JSON.parse(JSON.stringify(e))}function pl(e,t,n={}){const{window:a=ol,...r}=n;let l;const i=sl(()=>a&&"ResizeObserver"in a),s=()=>{l&&(l.disconnect(),l=void 0)},u=(0,o.EW)(()=>Array.isArray(e)?e.map(e=>nl(e)):[nl(e)]),d=(0,o.wB)(u,e=>{if(s(),i.value&&a){l=new ResizeObserver(t);for(const t of e)t&&l.observe(t,r)}},{immediate:!0,flush:"post"}),c=()=>{s(),d()};return kr(c),{isSupported:i,stop:c}}function vl(e,t={}){const n=dl(t),a=(0,o.EW)(()=>nl(e));return{focused:(0,o.EW)(()=>!(!a.value||!n.value)&&a.value.contains(n.value))}}function hl(e,t,n,r={}){var l,i,s;const{clone:u=!1,passive:d=!1,eventName:c,deep:f=!1,defaultValue:p,shouldEmit:v}=r,h=(0,o.nI)(),m=n||(null==h?void 0:h.emit)||(null==(l=null==h?void 0:h.$emit)?void 0:l.bind(h))||(null==(s=null==(i=null==h?void 0:h.proxy)?void 0:i.$emit)?void 0:s.bind(null==h?void 0:h.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const g=e=>u?"function"==typeof u?u(e):fl(e):e,b=()=>Ir(e[t])?g(e[t]):p,w=e=>{v?v(e)&&m(y,e):m(y,e)};if(d){const n=b(),r=(0,a.KR)(n);let l=!1;return(0,o.wB)(()=>e[t],e=>{l||(l=!0,r.value=g(e),(0,o.dY)(()=>l=!1))}),(0,o.wB)(r,n=>{!l&&(n!==e[t]||f)&&w(n)},{deep:f}),r}return(0,o.EW)({get(){return b()},set(e){w(e)}})}function ml(e){return e?e.flatMap(e=>e.type===o.FK?ml(e.children):[e]):[]}function yl(){let e=document.activeElement;if(null==e)return null;for(;null!=e&&null!=e.shadowRoot&&null!=e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}const gl=["INPUT","TEXTAREA"];function bl(e,t,n,o={}){if(!t||o.enableIgnoredElement&&gl.includes(t.nodeName))return null;const{arrowKeyOptions:a="both",attributeName:r="[data-radix-vue-collection-item]",itemsArray:l=[],loop:i=!0,dir:s="ltr",preventScroll:u=!0,focus:d=!1}=o,[c,f,p,v,h,m]=["ArrowRight"===e.key,"ArrowLeft"===e.key,"ArrowUp"===e.key,"ArrowDown"===e.key,"Home"===e.key,"End"===e.key],y=p||v,g=c||f;if(!h&&!m&&(!y&&!g||"vertical"===a&&g||"horizontal"===a&&y))return null;const b=n?Array.from(n.querySelectorAll(r)):l;if(!b.length)return null;u&&e.preventDefault();let w=null;return g||y?w=wl(b,t,{goForward:y?v:"ltr"===s?c:f,loop:i}):h?w=b.at(0)||null:m&&(w=b.at(-1)||null),d&&(null==w||w.focus()),w}function wl(e,t,n,o=e.length){if(0===--o)return null;const a=e.indexOf(t),r=n.goForward?a+1:a-1;if(!n.loop&&(r<0||r>=e.length))return null;const l=(r+e.length)%e.length,i=e[l];return i?i.hasAttribute("disabled")&&"false"!==i.getAttribute("disabled")?wl(e,i,n,o):i:null}function Rl(e){if(null===e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.iterator in e)&&(!(Symbol.toStringTag in e)||"[object Module]"===Object.prototype.toString.call(e))}function xl(e,t,n=".",o){if(!Rl(t))return xl(e,{},n,o);const a=Object.assign({},t);for(const r in e){if("__proto__"===r||"constructor"===r)continue;const t=e[r];null!=t&&(o&&o(a,r,t,n)||(Array.isArray(t)&&Array.isArray(a[r])?a[r]=[...t,...a[r]]:Rl(t)&&Rl(a[r])?a[r]=xl(t,a[r],(n?`${n}.`:"")+r.toString(),o):a[r]=t))}return a}function Bl(e){return(...t)=>t.reduce((t,n)=>xl(t,n,"",e),{})}const _l=Bl(),[El,Cl]=Za("ConfigProvider");Boolean;let kl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",Ml=(e=21)=>{let t="",n=e;for(;n--;)t+=kl[64*Math.random()|0];return t};const Sl=Dr(()=>{const e=(0,a.KR)(new Map),t=(0,a.KR)(),n=(0,o.EW)(()=>{for(const t of e.value.values())if(t)return!0;return!1}),r=El({scrollBody:(0,a.KR)(!0)});let l=null;const i=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.body.style.removeProperty("--scrollbar-width"),document.body.style.overflow=t.value??"",Vr&&(null==l||l()),t.value=void 0};return(0,o.wB)(n,(e,n)=>{var a;if(!Pr)return;if(!e)return void(n&&i());void 0===t.value&&(t.value=document.body.style.overflow);const s=window.innerWidth-document.documentElement.clientWidth,u={padding:s,margin:0},d=null!=(a=r.scrollBody)&&a.value?"object"==typeof r.scrollBody.value?_l({padding:!0===r.scrollBody.value.padding?s:r.scrollBody.value.padding,margin:!0===r.scrollBody.value.margin?s:r.scrollBody.value.margin},u):u:{padding:0,margin:0};s>0&&(document.body.style.paddingRight="number"==typeof d.padding?`${d.padding}px`:String(d.padding),document.body.style.marginRight="number"==typeof d.margin?`${d.margin}px`:String(d.margin),document.body.style.setProperty("--scrollbar-width",`${s}px`),document.body.style.overflow="hidden"),Vr&&(l=al(document,"touchmove",e=>Ol(e),{passive:!1})),(0,o.dY)(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function Dl(e){const t=Ml(6),n=Sl();n.value.set(t,e??!1);const a=(0,o.EW)({get:()=>n.value.get(t)??!1,set:e=>n.value.set(t,e)});return Jr(()=>{n.value.delete(t)}),a}function Al(e){const t=window.getComputedStyle(e);if("scroll"===t.overflowX||"scroll"===t.overflowY||"auto"===t.overflowX&&e.clientWidth<e.scrollWidth||"auto"===t.overflowY&&e.clientHeight<e.scrollHeight)return!0;{const t=e.parentNode;return t instanceof Element&&"BODY"!==t.tagName&&Al(t)}}function Ol(e){const t=e||window.event,n=t.target;return!(n instanceof Element&&Al(n))&&(t.touches.length>1||(t.preventDefault&&t.cancelable&&t.preventDefault(),!1))}const Tl="data-radix-vue-collection-item";function Pl(e,t=Tl){const n=e??Symbol();return{createCollection:e=>{const r=(0,a.KR)([]);function l(){const n=nl(e);return r.value=n?Array.from(n.querySelectorAll(`[${t}]:not([data-disabled])`)):[]}return(0,o.Ic)(()=>{r.value=[]}),(0,o.sV)(l),(0,o.$u)(l),(0,o.wB)(()=>null==e?void 0:e.value,l,{immediate:!0}),(0,o.Gt)(n,r),r},injectCollection:()=>(0,o.WQ)(n,(0,a.KR)([]))}}function Il(e){const t=(0,a.KR)(e);function n(){return t.value}function o(e){t.value=e}function r(e,n){return new s(t.value,n).format(e)}function l(e,t=!0){return ln(e)&&t?r(on(e),{dateStyle:"long",timeStyle:"long"}):r(on(e),{dateStyle:"long"})}function i(e,n={}){return new s(t.value,{month:"long",year:"numeric",...n}).format(e)}function u(e,n={}){return new s(t.value,{month:"long",...n}).format(e)}function d(){const e=P(N());return[1,2,3,4,5,6,7,8,9,10,11,12].map(t=>({label:u(on(e.set({month:t}))),value:t}))}function c(e,n={}){return new s(t.value,{year:"numeric",...n}).format(e)}function f(e,n){return rn(e)?new s(t.value,{...n,timeZone:e.timeZone}).formatToParts(on(e)):new s(t.value,n).formatToParts(on(e))}function p(e,n="narrow"){return new s(t.value,{weekday:n}).format(e)}function v(e){var n;return"PM"===(null==(n=new s(t.value,{hour:"numeric",minute:"numeric"}).formatToParts(e).find(e=>"dayPeriod"===e.type))?void 0:n.value)?"PM":"AM"}const h={year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"};function m(e,t,n={}){const o={...h,...n},a=f(e,o).find(e=>e.type===t);return a?a.value:""}return{setLocale:o,getLocale:n,fullMonth:u,fullYear:c,fullMonthAndYear:i,toParts:f,custom:r,part:m,dayPeriod:v,selectedDate:l,dayOfWeek:p,getMonths:d}}function Wl(e){const t=El({dir:(0,a.KR)("ltr")});return(0,o.EW)(()=>{var n;return(null==e?void 0:e.value)||(null==(n=t.dir)?void 0:n.value)||"ltr"})}function Fl(e){const t=(0,o.nI)(),n=null==t?void 0:t.type.emits,a={};return null!=n&&n.length||console.warn(`No emitted event found. Please check component: ${null==t?void 0:t.type.__name}`),null==n||n.forEach(t=>{a[(0,r.rU)((0,r.PT)(t))]=(...n)=>e(t,...n)}),a}let $l=0;function Nl(){(0,o.nT)(e=>{if(!Pr)return;const t=document.querySelectorAll("[data-radix-focus-guard]");document.body.insertAdjacentElement("afterbegin",t[0]??Vl()),document.body.insertAdjacentElement("beforeend",t[1]??Vl()),$l++,e(()=>{1===$l&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),$l--})})}function Vl(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function Ll(e){return(0,o.EW)(()=>{var t;return!Ar(e)||!(null==(t=nl(e))||!t.closest("form"))})}function Kl(e){const t=(0,o.nI)(),n=Object.keys((null==t?void 0:t.type.props)??{}).reduce((e,n)=>{const o=(null==t?void 0:t.type.props[n]).default;return void 0!==o&&(e[n]=o),e},{}),l=(0,a.lW)(e);return(0,o.EW)(()=>{const e={},o=(null==t?void 0:t.vnode.props)??{};return Object.keys(o).forEach(t=>{e[(0,r.PT)(t)]=o[t]}),Object.keys({...n,...e}).reduce((e,t)=>(void 0!==l.value[t]&&(e[t]=l.value[t]),e),{})})}function jl(e,t){const n=Kl(e),a=t?Fl(t):{};return(0,o.EW)(()=>({...n.value,...a}))}function Gl(){const e=(0,o.nI)(),t=(0,a.KR)(),n=(0,o.EW)(()=>{var e,n;return["#text","#comment"].includes(null==(e=t.value)?void 0:e.$el.nodeName)?null==(n=t.value)?void 0:n.$el.nextElementSibling:nl(t)}),r=Object.assign({},e.exposed),l={};for(const o in e.props)Object.defineProperty(l,o,{enumerable:!0,configurable:!0,get:()=>e.props[o]});if(Object.keys(r).length>0)for(const o in r)Object.defineProperty(l,o,{enumerable:!0,configurable:!0,get:()=>r[o]});function i(n){t.value=n,n&&(Object.defineProperty(l,"$el",{enumerable:!0,configurable:!0,get:()=>n instanceof Element?n:n.$el}),e.exposed=l)}return Object.defineProperty(l,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=l,{forwardRef:i,currentRef:t,currentElement:n}}function Xl(e,t){const n=zr(!1,300),r=(0,a.KR)(null),l=Mr();function i(){r.value=null,n.value=!1}function s(e,t){const o=e.currentTarget,a={x:e.clientX,y:e.clientY},l=Ul(a,o.getBoundingClientRect()),i=zl(a,l),s=Hl(t.getBoundingClientRect()),u=Zl([...i,...s]);r.value=u,n.value=!0}return(0,o.nT)(n=>{if(e.value&&t.value){const o=e=>s(e,t.value),a=t=>s(t,e.value);e.value.addEventListener("pointerleave",o),t.value.addEventListener("pointerleave",a),n(()=>{var n,r;null==(n=e.value)||n.removeEventListener("pointerleave",o),null==(r=t.value)||r.removeEventListener("pointerleave",a)})}}),(0,o.nT)(n=>{var o;if(r.value){const a=n=>{var o,a;if(!r.value)return;const s=n.target,u={x:n.clientX,y:n.clientY},d=(null==(o=e.value)?void 0:o.contains(s))||(null==(a=t.value)?void 0:a.contains(s)),c=!Yl(u,r.value),f=!!s.closest("[data-grace-area-trigger]");d?i():(c||f)&&(i(),l.trigger())};null==(o=e.value)||o.ownerDocument.addEventListener("pointermove",a),n(()=>{var t;return null==(t=e.value)?void 0:t.ownerDocument.removeEventListener("pointermove",a)})}}),{isPointerInTransit:n,onPointerExit:l.on}}function Ul(e,t){const n=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),r=Math.abs(t.left-e.x);switch(Math.min(n,o,a,r)){case r:return"left";case a:return"right";case n:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function zl(e,t,n=5){const o=[];switch(t){case"top":o.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":o.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":o.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":o.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return o}function Hl(e){const{top:t,right:n,bottom:o,left:a}=e;return[{x:a,y:t},{x:n,y:t},{x:n,y:o},{x:a,y:o}]}function Yl(e,t){const{x:n,y:o}=e;let a=!1;for(let r=0,l=t.length-1;r<t.length;l=r++){const e=t[r].x,i=t[r].y,s=t[l].x,u=t[l].y;i>o!=u>o&&n<(s-e)*(o-i)/(u-i)+e&&(a=!a)}return a}function Zl(e){const t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),ql(t)}function ql(e){if(e.length<=1)return e.slice();const t=[];for(let o=0;o<e.length;o++){const n=e[o];for(;t.length>=2;){const e=t[t.length-1],o=t[t.length-2];if(!((e.x-o.x)*(n.y-o.y)>=(e.y-o.y)*(n.x-o.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let o=e.length-1;o>=0;o--){const t=e[o];for(;n.length>=2;){const e=n[n.length-1],o=n[n.length-2];if(!((e.x-o.x)*(t.y-o.y)>=(e.y-o.y)*(t.x-o.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Jl=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Ql=new WeakMap,ei=new WeakMap,ti={},ni=0,oi=function(e){return e&&(e.host||oi(e.parentNode))},ai=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=oi(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(e){return!!e})},ri=function(e,t,n,o){var a=ai(t,Array.isArray(e)?e:[e]);ti[n]||(ti[n]=new WeakMap);var r=ti[n],l=[],i=new Set,s=new Set(a),u=function(e){!e||i.has(e)||(i.add(e),u(e.parentNode))};a.forEach(u);var d=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(i.has(e))d(e);else try{var t=e.getAttribute(o),a=null!==t&&"false"!==t,s=(Ql.get(e)||0)+1,u=(r.get(e)||0)+1;Ql.set(e,s),r.set(e,u),l.push(e),1===s&&a&&ei.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(o,"true")}catch(c){console.error("aria-hidden: cannot operate on ",e,c)}})};return d(t),i.clear(),ni++,function(){l.forEach(function(e){var t=Ql.get(e)-1,a=r.get(e)-1;Ql.set(e,t),r.set(e,a),t||(ei.has(e)||e.removeAttribute(o),ei.delete(e)),a||e.removeAttribute(n)}),ni--,ni||(Ql=new WeakMap,Ql=new WeakMap,ei=new WeakMap,ti={})}},li=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=Jl(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),ri(o,a,n,"aria-hidden")):function(){return null}};function ii(e){let t;(0,o.wB)(()=>nl(e),e=>{e?t=li(e):t&&t()}),(0,o.hi)(()=>{t&&t()})}let si=0;function ui(e,t="radix"){if(e)return e;const n=El({useId:void 0});return o.Bi?`${t}-${o.Bi()}`:n.useId?`${t}-${n.useId()}`:`${t}-${++si}`}function di(e,t){const n=(0,a.KR)(),o=(o,a)=>{if(t.multiple&&Array.isArray(e.value))if("replace"===t.selectionBehavior)e.value=[o],n.value=o;else{const t=e.value.findIndex(e=>a(e));-1!==t?e.value.splice(t,1):e.value.push(o)}else"replace"===t.selectionBehavior?e.value={...o}:!Array.isArray(e.value)&&a(e.value)?e.value=void 0:e.value={...o};return e.value};function r(o,a,r,l){var i;if(null==n||!n.value||!t.multiple||!Array.isArray(e.value))return;const s=null==(i=r().filter(e=>""!==e.ref.dataset.disabled).find(e=>e.ref===a))?void 0:i.value;if(!s)return;let u=null;switch(o){case"prev":case"next":u=rr(l,n.value,s);break;case"first":u=rr(l,n.value,null==l?void 0:l[0]);break;case"last":u=rr(l,n.value,null==l?void 0:l[l.length-1]);break}e.value=u}return{firstValue:n,onSelectItem:o,handleMultipleReplace:r}}function ci(e){const t=(0,a.KR)(),n=(0,o.EW)(()=>{var e;return(null==(e=t.value)?void 0:e.width)??0}),r=(0,o.EW)(()=>{var e;return(null==(e=t.value)?void 0:e.height)??0});return(0,o.sV)(()=>{const n=nl(e);if(n){t.value={width:n.offsetWidth,height:n.offsetHeight};const e=new ResizeObserver(e=>{if(!Array.isArray(e)||!e.length)return;const o=e[0];let a,r;if("borderBoxSize"in o){const e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;a=t.inlineSize,r=t.blockSize}else a=n.offsetWidth,r=n.offsetHeight;t.value={width:a,height:r}});return e.observe(n,{box:"border-box"}),()=>e.unobserve(n)}t.value=void 0}),{width:n,height:r}}function fi(e,t){const n=(0,a.KR)(e);function o(e){return t[n.value][e]??n.value}return{state:n,dispatch:e=>{n.value=o(e)}}}const pi="data-item-text";function vi(e){const t=zr("",1e3);return{search:t,handleTypeaheadSearch:(n,o)=>{if((null==e||!e.value)&&!o)return;t.value=t.value+n;const a=(null==e?void 0:e.value)??o,r=yl(),l=a.map(e=>{var t;return{ref:e,textValue:(null==(t=(e.querySelector(`[${pi}]`)??e).textContent)?void 0:t.trim())??""}}),i=l.find(e=>e.ref===r),s=l.map(e=>e.textValue),u=mi(s,t.value,null==i?void 0:i.textValue),d=l.find(e=>e.textValue===u);return d&&d.ref.focus(),null==d?void 0:d.ref},resetTypeahead:()=>{t.value=""}}}function hi(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function mi(e,t,n){const o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let r=hi(e,Math.max(a,0));1===o.length&&(r=r.filter(e=>e!==n));const l=r.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function yi(){return{ALT:"Alt",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",ARROW_UP:"ArrowUp",BACKSPACE:"Backspace",CAPS_LOCK:"CapsLock",CONTROL:"Control",DELETE:"Delete",END:"End",ENTER:"Enter",ESCAPE:"Escape",F1:"F1",F10:"F10",F11:"F11",F12:"F12",F2:"F2",F3:"F3",F4:"F4",F5:"F5",F6:"F6",F7:"F7",F8:"F8",F9:"F9",HOME:"Home",META:"Meta",PAGE_DOWN:"PageDown",PAGE_UP:"PageUp",SHIFT:"Shift",SPACE:" ",TAB:"Tab",CTRL:"Control",ASTERISK:"*",SPACE_CODE:"Space"}}const gi=(0,o.pM)({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:n}){return()=>{var e,a;if(!n.default)return null;const r=ml(n.default()),l=r.findIndex(e=>e.type!==o.Mw);if(-1===l)return r;const i=r[l];null==(e=i.props)||delete e.ref;const s=i.props?(0,o.v6)(t,i.props):t;t.class&&null!=(a=i.props)&&a.class&&delete i.props.class;const u=(0,o.E3)(i,s);for(const t in s)t.startsWith("on")&&(u.props||(u.props={}),u.props[t]=s[t]);return 1===r.length?u:(r[l]=u,r)}}}),bi=(0,o.pM)({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:n}){const a=e.asChild?"template":e.as;return"string"==typeof a&&["area","img","input"].includes(a)?()=>(0,o.h)(a,t):"template"!==a?()=>(0,o.h)(e.as,t,{default:n.default}):()=>(0,o.h)(gi,t,{default:n.default})}});function wi(){const e=(0,a.KR)(),t=(0,o.EW)(()=>{var t,n;return["#text","#comment"].includes(null==(t=e.value)?void 0:t.$el.nodeName)?null==(n=e.value)?void 0:n.$el.nextElementSibling:nl(e)});return{primitiveElement:e,currentElement:t}}const[Ri,xi]=Za("CollapsibleRoot"),Bi=(0,o.pM)({__name:"CollapsibleRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["update:open"],setup(e,{expose:t,emit:n}){const r=e,l=hl(r,"open",n,{defaultValue:r.defaultOpen,passive:void 0===r.open}),i=hl(r,"disabled");return xi({contentId:"",disabled:i,open:l,onOpenToggle:()=>{l.value=!l.value}}),t({open:l}),Gl(),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":r.asChild,"data-state":(0,a.R1)(l)?"open":"closed","data-disabled":(0,a.R1)(i)?"":void 0},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{open:(0,a.R1)(l)})]),_:3},8,["as","as-child","data-state","data-disabled"]))}}),_i=(0,o.pM)({__name:"CollapsibleTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;Gl();const n=Ri();return(e,r)=>{var l,i;return(0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{type:"button"===e.as?"button":void 0,as:e.as,"as-child":t.asChild,"aria-controls":(0,a.R1)(n).contentId,"aria-expanded":(0,a.R1)(n).open.value,"data-state":(0,a.R1)(n).open.value?"open":"closed","data-disabled":null!=(l=(0,a.R1)(n).disabled)&&l.value?"":void 0,disabled:null==(i=(0,a.R1)(n).disabled)?void 0:i.value,onClick:(0,a.R1)(n).onOpenToggle},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["type","as","as-child","aria-controls","aria-expanded","data-state","data-disabled","disabled","onClick"])}}});function Ei(e,t){var n;const r=(0,a.KR)({}),l=(0,a.KR)("none"),i=(0,a.KR)(e),s=e.value?"mounted":"unmounted";let u;const d=(null==(n=t.value)?void 0:n.ownerDocument.defaultView)??ol,{state:c,dispatch:f}=fi(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),p=e=>{var n;if(Pr){const o=new CustomEvent(e,{bubbles:!1,cancelable:!1});null==(n=t.value)||n.dispatchEvent(o)}};(0,o.wB)(e,async(e,n)=>{var a;const i=n!==e;if(await(0,o.dY)(),i){const o=l.value,i=Ci(t.value);e?(f("MOUNT"),p("enter"),"none"===i&&p("after-enter")):"none"===i||"none"===(null==(a=r.value)?void 0:a.display)?(f("UNMOUNT"),p("leave"),p("after-leave")):n&&o!==i?(f("ANIMATION_OUT"),p("leave")):(f("UNMOUNT"),p("after-leave"))}},{immediate:!0});const v=e=>{const n=Ci(t.value),o=n.includes(e.animationName),a="mounted"===c.value?"enter":"leave";if(e.target===t.value&&o&&(p(`after-${a}`),f("ANIMATION_END"),!i.value)){const e=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",u=null==d?void 0:d.setTimeout(()=>{var n;"forwards"===(null==(n=t.value)?void 0:n.style.animationFillMode)&&(t.value.style.animationFillMode=e)})}e.target===t.value&&"none"===n&&f("ANIMATION_END")},h=e=>{e.target===t.value&&(l.value=Ci(t.value))},m=(0,o.wB)(t,(e,t)=>{e?(r.value=getComputedStyle(e),e.addEventListener("animationstart",h),e.addEventListener("animationcancel",v),e.addEventListener("animationend",v)):(f("ANIMATION_END"),void 0!==u&&(null==d||d.clearTimeout(u)),null==t||t.removeEventListener("animationstart",h),null==t||t.removeEventListener("animationcancel",v),null==t||t.removeEventListener("animationend",v))},{immediate:!0}),y=(0,o.wB)(c,()=>{const e=Ci(t.value);l.value="mounted"===c.value?e:"none"});return(0,o.hi)(()=>{m(),y()}),{isPresent:(0,o.EW)(()=>["mounted","unmountSuspended"].includes(c.value))}}function Ci(e){return e&&getComputedStyle(e).animationName||"none"}const ki=(0,o.pM)({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){var r;const{present:l,forceMount:i}=(0,a.QW)(e),s=(0,a.KR)(),{isPresent:u}=Ei(l,s);n({present:u});let d=t.default({present:u});d=ml(d||[]);const c=(0,o.nI)();if(d&&(null==d?void 0:d.length)>1){const e=null!=(r=null==c?void 0:c.parent)&&r.type.name?`<${c.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${e}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(e=>`  - ${e}`).join("\n")].join("\n"))}return()=>i.value||l.value||u.value?(0,o.h)(t.default({present:u})[0],{ref:e=>{const t=nl(e);return typeof(null==t?void 0:t.hasAttribute)>"u"||(null!=t&&t.hasAttribute("data-radix-popper-content-wrapper")?s.value=t.firstElementChild:s.value=t),t}}):null}}),Mi=(0,o.pM)({inheritAttrs:!1,__name:"CollapsibleContent",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Ri();n.contentId||(n.contentId=ui(void 0,"radix-vue-collapsible-content"));const r=(0,a.KR)(),{forwardRef:l,currentElement:i}=Gl(),s=(0,a.KR)(0),u=(0,a.KR)(0),d=(0,o.EW)(()=>n.open.value),c=(0,a.KR)(d.value),f=(0,a.KR)();return(0,o.wB)(()=>{var e;return[d.value,null==(e=r.value)?void 0:e.present]},async()=>{await(0,o.dY)();const e=i.value;if(!e)return;f.value=f.value||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";const t=e.getBoundingClientRect();u.value=t.height,s.value=t.width,c.value||(e.style.transitionDuration=f.value.transitionDuration,e.style.animationName=f.value.animationName)},{immediate:!0}),(0,o.sV)(()=>{requestAnimationFrame(()=>{c.value=!1})}),(e,i)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{ref_key:"presentRef",ref:r,present:e.forceMount||(0,a.R1)(n).open.value,"force-mount":!0},{default:(0,o.k6)(()=>{var i,d;return[(0,o.bF)((0,a.R1)(bi),(0,o.v6)(e.$attrs,{id:(0,a.R1)(n).contentId,ref:(0,a.R1)(l),"as-child":t.asChild,as:e.as,"data-state":(0,a.R1)(n).open.value?"open":"closed","data-disabled":null!=(i=(0,a.R1)(n).disabled)&&i.value?"":void 0,hidden:!(null!=(d=r.value)&&d.present),style:{"--radix-collapsible-content-height":`${u.value}px`,"--radix-collapsible-content-width":`${s.value}px`}}),{default:(0,o.k6)(()=>{var t;return[null!=(t=r.value)&&t.present?(0,o.RG)(e.$slots,"default",{key:0}):(0,o.Q3)("",!0)]}),_:3},16,["id","as-child","as","data-state","data-disabled","hidden","style"])]}),_:3},8,["present"]))}});function Si({type:e,defaultValue:t,modelValue:n}){const o=n||t;if(ir(e)&&ir(n)&&ir(t))throw new Error("Either the `type` or the `value` or `default-value` prop must be defined.");if(void 0!==n&&void 0!==t&&typeof n!=typeof t)throw new Error(`Invalid prop \`value\` of value \`${n}\` supplied, should be the same type as the \`defaultValue\` prop, which is \`${t}\`. The \`value\` prop must be:\n  ${"single"===e?"- a string":"multiple"===e?"- an array of strings":"- a string\n- an array of strings"}\n  - \`undefined\``);const a=void 0!==n||void 0!==t;if(e&&a){const o=Array.isArray(n)||Array.isArray(t),a=void 0!==n?"modelValue":"defaultValue",r="modelValue"===a?typeof n:typeof t;if("single"===e&&o)return console.error(`Invalid prop \`${a}\` of type ${r} supplied with type \`single\`. The \`modelValue\` prop must be a string or \`undefined\`.\n    You can remove the \`type\` prop to let the component infer the type from the ${a} prop.`),"multiple";if("multiple"===e&&!o)return console.error(`Invalid prop \`${a}\` of type ${r} supplied with type \`multiple\`. The \`modelValue\` prop must be an array of strings or \`undefined\`.\n    You can remove the \`type\` prop to let the component infer the type from the ${a} prop.`),"single"}return a?Array.isArray(o)?"multiple":"single":e}function Di({type:e,defaultValue:t,modelValue:n}){return e||Si({type:e,defaultValue:t,modelValue:n})}function Ai({type:e,defaultValue:t}){return void 0!==t?t:"single"===e?void 0:[]}function Oi(e,t){const n=(0,a.KR)(Di(e)),r=hl(e,"modelValue",t,{defaultValue:Ai(e),passive:void 0===e.modelValue,deep:!0});function l(e){if("single"===n.value)r.value=e===r.value?void 0:e;else{const t=[...r.value||[]];if(t.includes(e)){const n=t.findIndex(t=>t===e);t.splice(n,1)}else t.push(e);r.value=t}}(0,o.wB)(()=>[e.type,e.modelValue,e.defaultValue],()=>{const t=Si(e);n.value!==t&&(n.value=t)},{immediate:!0});const i=(0,o.EW)(()=>"single"===n.value);return{modelValue:r,type:n,changeModelValue:l,isSingle:i}}const[Ti,Pi]=Za("AccordionRoot"),[Ii,Wi]=(Boolean,Boolean,Boolean,Za("AccordionItem")),[Fi,$i]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("DialogRoot")),Ni=(0,o.pM)({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=hl(n,"open",t,{defaultValue:n.defaultOpen,passive:void 0===n.open}),l=(0,a.KR)(),i=(0,a.KR)(),{modal:s}=(0,a.QW)(n);return $i({open:r,modal:s,openModal:()=>{r.value=!0},onOpenChange:e=>{r.value=e},onOpenToggle:()=>{r.value=!r.value},contentId:"",titleId:"",descriptionId:"",triggerElement:l,contentElement:i}),(e,t)=>(0,o.RG)(e.$slots,"default",{open:(0,a.R1)(r)})}}),Vi=(0,o.pM)({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=Fi(),{forwardRef:r,currentElement:l}=Gl();return n.contentId||(n.contentId=ui(void 0,"radix-vue-dialog-content")),(0,o.sV)(()=>{n.triggerElement.value=l.value}),(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{ref:(0,a.R1)(r),type:"button"===e.as?"button":void 0,"aria-haspopup":"dialog","aria-expanded":(0,a.R1)(n).open.value||!1,"aria-controls":(0,a.R1)(n).open.value?(0,a.R1)(n).contentId:void 0,"data-state":(0,a.R1)(n).open.value?"open":"closed",onClick:(0,a.R1)(n).onOpenToggle}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),Li=(0,o.pM)({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=il();return(e,n)=>(0,a.R1)(t)||e.forceMount?((0,o.uX)(),(0,o.Wv)(o.Im,{key:0,to:e.to,disabled:e.disabled},[(0,o.RG)(e.$slots,"default")],8,["to","disabled"])):(0,o.Q3)("",!0)}}),Ki=(Boolean,Boolean,"dismissableLayer.pointerDownOutside"),ji="dismissableLayer.focusOutside";function Gi(e,t){const n=t.closest("[data-dismissable-layer]"),o=""===e.dataset.dismissableLayer?e:e.querySelector("[data-dismissable-layer]"),a=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&o===n||a.indexOf(o)<a.indexOf(n))}function Xi(e,t){var n;const r=(null==(n=null==t?void 0:t.value)?void 0:n.ownerDocument)??(null==globalThis?void 0:globalThis.document),l=(0,a.KR)(!1),i=(0,a.KR)(()=>{});return(0,o.nT)(n=>{if(!Pr)return;const o=async n=>{const o=n.target;if(null!=t&&t.value){if(Gi(t.value,o))return void(l.value=!1);if(n.target&&!l.value){let t=function(){qa(Ki,e,o)};const o={originalEvent:n};"touch"===n.pointerType?(r.removeEventListener("click",i.value),i.value=t,r.addEventListener("click",i.value,{once:!0})):t()}else r.removeEventListener("click",i.value);l.value=!1}},a=window.setTimeout(()=>{r.addEventListener("pointerdown",o)},0);n(()=>{window.clearTimeout(a),r.removeEventListener("pointerdown",o),r.removeEventListener("click",i.value)})}),{onPointerDownCapture:()=>l.value=!0}}function Ui(e,t){var n;const r=(null==(n=null==t?void 0:t.value)?void 0:n.ownerDocument)??(null==globalThis?void 0:globalThis.document),l=(0,a.KR)(!1);return(0,o.nT)(n=>{if(!Pr)return;const a=async n=>{null!=t&&t.value&&(await(0,o.dY)(),t.value&&!Gi(t.value,n.target)&&n.target&&!l.value&&qa(ji,e,{originalEvent:n}))};r.addEventListener("focusin",a),n(()=>r.removeEventListener("focusin",a))}),{onFocusCapture:()=>l.value=!0,onBlurCapture:()=>l.value=!1}}const zi=(0,a.Kh)({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Hi=(0,o.pM)({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:t}){const n=e,l=t,{forwardRef:i,currentElement:s}=Gl(),u=(0,o.EW)(()=>{var e;return(null==(e=s.value)?void 0:e.ownerDocument)??globalThis.document}),d=(0,o.EW)(()=>zi.layersRoot),c=(0,o.EW)(()=>s.value?Array.from(d.value).indexOf(s.value):-1),f=(0,o.EW)(()=>zi.layersWithOutsidePointerEventsDisabled.size>0),p=(0,o.EW)(()=>{const e=Array.from(d.value),[t]=[...zi.layersWithOutsidePointerEventsDisabled].slice(-1),n=e.indexOf(t);return c.value>=n}),v=Xi(async e=>{const t=[...zi.branches].some(t=>null==t?void 0:t.contains(e.target));!p.value||t||(l("pointerDownOutside",e),l("interactOutside",e),await(0,o.dY)(),e.defaultPrevented||l("dismiss"))},s),h=Ui(e=>{[...zi.branches].some(t=>null==t?void 0:t.contains(e.target))||(l("focusOutside",e),l("interactOutside",e),e.defaultPrevented||l("dismiss"))},s);let m;return ll("Escape",e=>{c.value===d.value.size-1&&(l("escapeKeyDown",e),e.defaultPrevented||l("dismiss"))}),(0,o.nT)(e=>{s.value&&(n.disableOutsidePointerEvents&&(0===zi.layersWithOutsidePointerEventsDisabled.size&&(m=u.value.body.style.pointerEvents,u.value.body.style.pointerEvents="none"),zi.layersWithOutsidePointerEventsDisabled.add(s.value)),d.value.add(s.value),e(()=>{n.disableOutsidePointerEvents&&1===zi.layersWithOutsidePointerEventsDisabled.size&&(u.value.body.style.pointerEvents=m)}))}),(0,o.nT)(e=>{e(()=>{s.value&&(d.value.delete(s.value),zi.layersWithOutsidePointerEventsDisabled.delete(s.value))})}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{ref:(0,a.R1)(i),"as-child":e.asChild,as:e.as,"data-dismissable-layer":"",style:(0,r.Tr)({pointerEvents:f.value?p.value?"auto":"none":void 0}),onFocusCapture:(0,a.R1)(h).onFocusCapture,onBlurCapture:(0,a.R1)(h).onBlurCapture,onPointerdownCapture:(0,a.R1)(v).onPointerDownCapture},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),Yi=(0,o.pM)({__name:"DismissableLayerBranch",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e,{forwardRef:n,currentElement:r}=Gl();return(0,o.sV)(()=>{zi.branches.add(r.value)}),(0,o.hi)(()=>{zi.branches.delete(r.value)}),(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(n)},t),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Zi="focusScope.autoFocusOnMount",qi="focusScope.autoFocusOnUnmount",Ji={bubbles:!1,cancelable:!0};function Qi(e,{select:t=!1}={}){const n=yl();for(const o of e)if(rs(o,{select:t}),yl()!==n)return!0}function es(e){const t=ts(e),n=ns(t,e),o=ns(t.reverse(),e);return[n,o]}function ts(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ns(e,t){for(const n of e)if(!os(n,{upTo:t}))return n}function os(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function as(e){return e instanceof HTMLInputElement&&"select"in e}function rs(e,{select:t=!1}={}){if(e&&e.focus){const n=yl();e.focus({preventScroll:!0}),e!==n&&as(e)&&t&&e.select()}}const ls=Sr(()=>(0,a.KR)([]));function is(){const e=ls();return{add(t){const n=e.value[0];t!==n&&(null==n||n.pause()),e.value=ss(e.value,t),e.value.unshift(t)},remove(t){var n;e.value=ss(e.value,t),null==(n=e.value[0])||n.resume()}}}function ss(e,t){const n=[...e],o=n.indexOf(t);return-1!==o&&n.splice(o,1),n}function us(e){return e.filter(e=>"A"!==e.tagName)}const ds=(0,o.pM)({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:t}){const n=e,r=t,{currentRef:l,currentElement:i}=Gl(),s=(0,a.KR)(null),u=is(),d=(0,a.Kh)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});function c(e){if(!n.loop&&!n.trapped||d.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=yl();if(t&&o){const t=e.currentTarget,[a,r]=es(t);a&&r?e.shiftKey||o!==r?e.shiftKey&&o===a&&(e.preventDefault(),n.loop&&rs(r,{select:!0})):(e.preventDefault(),n.loop&&rs(a,{select:!0})):o===t&&e.preventDefault()}}return(0,o.nT)(e=>{if(!Pr)return;const t=i.value;if(!n.trapped)return;function o(e){if(d.paused||!t)return;const n=e.target;t.contains(n)?s.value=n:rs(s.value,{select:!0})}function a(e){if(d.paused||!t)return;const n=e.relatedTarget;null!==n&&(t.contains(n)||rs(s.value,{select:!0}))}function r(e){t.contains(s.value)||rs(t)}document.addEventListener("focusin",o),document.addEventListener("focusout",a);const l=new MutationObserver(r);t&&l.observe(t,{childList:!0,subtree:!0}),e(()=>{document.removeEventListener("focusin",o),document.removeEventListener("focusout",a),l.disconnect()})}),(0,o.nT)(async e=>{const t=i.value;if(await(0,o.dY)(),!t)return;u.add(d);const n=yl();if(!t.contains(n)){const e=new CustomEvent(Zi,Ji);t.addEventListener(Zi,e=>r("mountAutoFocus",e)),t.dispatchEvent(e),e.defaultPrevented||(Qi(us(ts(t)),{select:!0}),yl()===n&&rs(t))}e(()=>{t.removeEventListener(Zi,e=>r("mountAutoFocus",e));const e=new CustomEvent(qi,Ji),o=e=>{r("unmountAutoFocus",e)};t.addEventListener(qi,o),t.dispatchEvent(e),setTimeout(()=>{e.defaultPrevented||rs(n??document.body,{select:!0}),t.removeEventListener(qi,o),u.remove(d)},0)})}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{ref_key:"currentRef",ref:l,tabindex:"-1","as-child":e.asChild,as:e.as,onKeydown:c},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as-child","as"]))}}),cs="menu.itemSelect",fs=["Enter"," "],ps=["ArrowDown","PageUp","Home"],vs=["ArrowUp","PageDown","End"],hs=[...ps,...vs],ms={ltr:[...fs,"ArrowRight"],rtl:[...fs,"ArrowLeft"]},ys={ltr:["ArrowLeft"],rtl:["ArrowRight"]};function gs(e){return e?"open":"closed"}function bs(e){return"indeterminate"===e}function ws(e){return bs(e)?"indeterminate":e?"checked":"unchecked"}function Rs(e){const t=yl();for(const n of e)if(n===t||(n.focus(),yl()!==t))return}function xs(e,t){const{x:n,y:o}=e;let a=!1;for(let r=0,l=t.length-1;r<t.length;l=r++){const e=t[r].x,i=t[r].y,s=t[l].x,u=t[l].y;i>o!=u>o&&n<(s-e)*(o-i)/(u-i)+e&&(a=!a)}return a}function Bs(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return xs(n,t)}function _s(e){return"mouse"===e.pointerType}const Es=(0,o.pM)({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=Fi(),{forwardRef:i,currentElement:s}=Gl();return l.titleId||(l.titleId=ui(void 0,"radix-vue-dialog-title")),l.descriptionId||(l.descriptionId=ui(void 0,"radix-vue-dialog-description")),(0,o.sV)(()=>{l.contentElement=s,yl()!==document.body&&(l.triggerElement.value=yl())}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ds),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:t[5]||(t[5]=e=>r("openAutoFocus",e)),onUnmountAutoFocus:t[6]||(t[6]=e=>r("closeAutoFocus",e))},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Hi),(0,o.v6)({id:(0,a.R1)(l).contentId,ref:(0,a.R1)(i),as:e.as,"as-child":e.asChild,"disable-outside-pointer-events":e.disableOutsidePointerEvents,role:"dialog","aria-describedby":(0,a.R1)(l).descriptionId,"aria-labelledby":(0,a.R1)(l).titleId,"data-state":(0,a.R1)(gs)((0,a.R1)(l).open.value)},e.$attrs,{onDismiss:t[0]||(t[0]=e=>(0,a.R1)(l).onOpenChange(!1)),onEscapeKeyDown:t[1]||(t[1]=e=>r("escapeKeyDown",e)),onFocusOutside:t[2]||(t[2]=e=>r("focusOutside",e)),onInteractOutside:t[3]||(t[3]=e=>r("interactOutside",e)),onPointerDownOutside:t[4]||(t[4]=e=>r("pointerDownOutside",e))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),Cs=(0,o.pM)({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=Fi(),i=Fl(r),{forwardRef:s,currentElement:u}=Gl();return ii(u),(e,t)=>((0,o.uX)(),(0,o.Wv)(Es,(0,o.v6)({...n,...(0,a.R1)(i)},{ref:(0,a.R1)(s),"trap-focus":(0,a.R1)(l).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:t[0]||(t[0]=e=>{var t;e.defaultPrevented||(e.preventDefault(),null==(t=(0,a.R1)(l).triggerElement.value)||t.focus())}),onPointerDownOutside:t[1]||(t[1]=e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:t[2]||(t[2]=e=>{e.preventDefault()})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["trap-focus"]))}}),ks=(0,o.pM)({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=Fl(t);Gl();const l=Fi(),i=(0,a.KR)(!1),s=(0,a.KR)(!1);return(e,t)=>((0,o.uX)(),(0,o.Wv)(Es,(0,o.v6)({...n,...(0,a.R1)(r)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:t[0]||(t[0]=e=>{var t;e.defaultPrevented||(i.value||null==(t=(0,a.R1)(l).triggerElement.value)||t.focus(),e.preventDefault()),i.value=!1,s.value=!1}),onInteractOutside:t[1]||(t[1]=e=>{var t;e.defaultPrevented||(i.value=!0,"pointerdown"===e.detail.originalEvent.type&&(s.value=!0));const n=e.target;(null==(t=(0,a.R1)(l).triggerElement.value)?void 0:t.contains(n))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&s.value&&e.preventDefault()})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Ms=(0,o.pM)({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=Fi(),i=Fl(r),{forwardRef:s}=Gl();return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(l).open.value},{default:(0,o.k6)(()=>[(0,a.R1)(l).modal.value?((0,o.uX)(),(0,o.Wv)(Cs,(0,o.v6)({key:0,ref:(0,a.R1)(s)},{...n,...(0,a.R1)(i),...e.$attrs}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)):((0,o.uX)(),(0,o.Wv)(ks,(0,o.v6)({key:1,ref:(0,a.R1)(s)},{...n,...(0,a.R1)(i),...e.$attrs}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ss=(0,o.pM)({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(e){const t=Fi();return Dl(!0),Gl(),(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,"data-state":(0,a.R1)(t).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Ds=(0,o.pM)({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=Fi(),{forwardRef:n}=Gl();return(e,r)=>{var l;return null!=(l=(0,a.R1)(t))&&l.modal.value?((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{key:0,present:e.forceMount||(0,a.R1)(t).open.value},{default:(0,o.k6)(()=>[(0,o.bF)(Ss,(0,o.v6)(e.$attrs,{ref:(0,a.R1)(n),as:e.as,"as-child":e.asChild}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):(0,o.Q3)("",!0)}}}),As=(0,o.pM)({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;Gl();const n=Fi();return(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{type:"button"===e.as?"button":void 0,onClick:r[0]||(r[0]=e=>(0,a.R1)(n).onOpenChange(!1))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["type"]))}}),Os=(0,o.pM)({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(e){const t=e,n=Fi();return Gl(),(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{id:(0,a.R1)(n).titleId}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id"]))}}),Ts=(0,o.pM)({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(e){const t=e;Gl();const n=Fi();return(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{id:(0,a.R1)(n).descriptionId}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id"]))}}),[Ps,Is]=(Boolean,Boolean,Boolean,Boolean,Boolean,Za("AlertDialogContent")),[Ws,Fs]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("AvatarRoot"));Boolean;function $s(e,t){const n=(0,a.KR)("idle"),r=(0,a.KR)(!1),l=e=>()=>{r.value&&(n.value=e)};return(0,o.sV)(()=>{r.value=!0,(0,o.wB)([()=>e.value,()=>null==t?void 0:t.value],([e,t])=>{if(e){const o=new window.Image;n.value="loading",o.onload=l("loaded"),o.onerror=l("error"),o.src=e,t&&(o.referrerPolicy=t)}else n.value="error"},{immediate:!0})}),(0,o.hi)(()=>{r.value=!1}),n}Boolean,Boolean;function Ns(e){function t(t){return Array.isArray(e.date.value)?e.date.value.some(e=>E(e,t)):!!e.date.value&&E(e.date.value,t)}const n=(0,o.EW)(()=>{var t,n,o,a;if(Array.isArray(e.date.value)){if(!e.date.value.length)return!1;for(const o of e.date.value)if(null!=(t=e.isDateDisabled)&&t.call(e,o)||null!=(n=e.isDateUnavailable)&&n.call(e,o))return!0}else{if(!e.date.value)return!1;if(null!=(o=e.isDateDisabled)&&o.call(e,e.date.value)||null!=(a=e.isDateUnavailable)&&a.call(e,e.date.value))return!0}return!1});return{isDateSelected:t,isInvalid:n}}function Vs(e,t){const n=t(e),o=n.compare(e),a={};return o>=7&&(a.day=1),o>=sn(e)&&(a.month=1),n.set({...a})}function Ls(e,t){const n=t(e),o=e.compare(n),a={};return o>=7&&(a.day=35),o>=sn(e)&&(a.month=13),n.set({...a})}function Ks(e,t){return t(e)}function js(e,t){return t(e)}function Gs(e){const t=Il(e.locale.value),n=(0,o.EW)(()=>{const t={calendar:e.placeholder.value.calendar.identifier};return"gregory"===e.placeholder.value.calendar.identifier&&"BC"===e.placeholder.value.era&&(t.era="short"),t}),r=(0,a.KR)(wn({dateObj:e.placeholder.value,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value})),l=(0,o.EW)(()=>r.value.map(e=>e.value));function i(e){return!l.value.some(t=>M(e,t))}const s=(t="month",n)=>{if(!e.maxValue.value||!r.value.length)return!1;if(e.disabled.value)return!0;const o=r.value[r.value.length-1].value;if(n||e.nextPage.value){const t=Vs(o,n||e.nextPage.value);return dn(t,e.maxValue.value)}if("year"===t){const t=o.add({years:1}).set({day:1,month:1});return dn(t,e.maxValue.value)}const a=o.add({months:1}).set({day:1});return dn(a,e.maxValue.value)},u=(t="month",n)=>{if(!e.minValue.value||!r.value.length)return!1;if(e.disabled.value)return!0;const o=r.value[0].value;if(n||e.prevPage.value){const t=Ls(o,n||e.prevPage.value);return un(t,e.minValue.value)}if("year"===t){const t=o.subtract({years:1}).set({day:35,month:13});return un(t,e.minValue.value)}const a=o.subtract({months:1}).set({day:35});return un(a,e.minValue.value)};function d(t){var n;return!!(null!=(n=e.isDateDisabled)&&n.call(e,t)||e.disabled.value||e.maxValue.value&&dn(t,e.maxValue.value)||e.minValue.value&&un(t,e.minValue.value))}const c=t=>{var n;return!(null==(n=e.isDateUnavailable)||!n.call(e,t))},f=(0,o.EW)(()=>r.value.length?r.value[0].rows[0].map(n=>t.dayOfWeek(on(n),e.weekdayFormat.value)):[]),p=(t="month",n)=>{const o=r.value[0].value;if(n||e.nextPage.value){const t=Ks(o,n||e.nextPage.value),a=wn({dateObj:t,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value});r.value=a;const l={};if(!n){const e=a[0].value.compare(o);e>=sn(o)&&(l.day=1),e>=365&&(l.month=1)}return void(e.placeholder.value=a[0].value.set({...l}))}const a="month"===t?o.add({months:e.pagedNavigation.value?e.numberOfMonths.value:1}):o.add({years:1}),l=wn({dateObj:a,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value});r.value=l,e.placeholder.value=l[0].value.set({day:1})},v=(t="month",n)=>{const o=r.value[0].value;if(n||e.prevPage.value){const t=js(o,n||e.prevPage.value),a=wn({dateObj:t,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value});r.value=a;const l={};if(!n){const e=o.compare(a[0].value);e>=sn(o)&&(l.day=1),e>=365&&(l.month=1)}return void(e.placeholder.value=a[0].value.set({...l}))}const a="month"===t?o.subtract({months:e.pagedNavigation.value?e.numberOfMonths.value:1}):o.subtract({years:1}),l=wn({dateObj:a,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value});r.value=l,e.placeholder.value=l[0].value.set({day:1})};(0,o.wB)(e.placeholder,t=>{l.value.some(e=>M(e,t))||(r.value=wn({dateObj:t,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value}))}),(0,o.wB)([e.locale,e.weekStartsOn,e.fixedWeeks,e.numberOfMonths],()=>{r.value=wn({dateObj:e.placeholder.value,weekStartsOn:e.weekStartsOn.value,locale:e.locale.value,fixedWeeks:e.fixedWeeks.value,numberOfMonths:e.numberOfMonths.value})});const h=(0,o.EW)(()=>{if(!r.value.length)return"";if(e.locale.value!==t.getLocale()&&t.setLocale(e.locale.value),1===r.value.length){const e=r.value[0].value;return`${t.fullMonthAndYear(on(e),n.value)}`}const o=on(r.value[0].value),a=on(r.value[r.value.length-1].value),l=t.fullMonth(o,n.value),i=t.fullMonth(a,n.value),s=t.fullYear(o,n.value),u=t.fullYear(a,n.value);return s===u?`${l} - ${i} ${u}`:`${l} ${s} - ${i} ${u}`}),m=(0,o.EW)(()=>`${e.calendarLabel.value??"Event Date"}, ${h.value}`);return{isDateDisabled:d,isDateUnavailable:c,isNextButtonDisabled:s,isPrevButtonDisabled:u,grid:r,weekdays:f,visibleView:l,isOutsideVisibleView:i,formatter:t,nextPage:p,prevPage:v,headingValue:h,fullCalendarLabel:m}}const Xs={style:{border:"0px",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0px",position:"absolute","white-space":"nowrap",width:"1px"}},Us={role:"heading","aria-level":"2"},[zs,Hs]=Za("CalendarRoot"),Ys=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"CalendarHeader",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}})),Zs=(0,o.pM)({__name:"CalendarHeading",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e,n=zs();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"data-disabled":(0,a.R1)(n).disabled.value?"":void 0}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{headingValue:(0,a.R1)(n).headingValue.value},()=>[(0,o.eW)((0,r.v_)((0,a.R1)(n).headingValue.value),1)])]),_:3},16,["data-disabled"]))}}),qs=(0,o.pM)({__name:"CalendarGrid",props:{asChild:{type:Boolean},as:{default:"table"}},setup(e){const t=e,n=zs(),r=(0,o.EW)(()=>!!n.disabled.value||void 0),l=(0,o.EW)(()=>!!n.readonly.value||void 0);return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{tabindex:"-1",role:"grid","aria-readonly":l.value,"aria-disabled":r.value,"data-readonly":l.value&&"","data-disabled":r.value&&""}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["aria-readonly","aria-disabled","data-readonly","data-disabled"]))}}),Js=(0,o.pM)({__name:"CalendarCell",props:{date:{},asChild:{type:Boolean},as:{default:"td"}},setup(e){const t=zs();return(e,n)=>{var r,l;return(0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,role:"gridcell","aria-selected":!!(0,a.R1)(t).isDateSelected(e.date)||void 0,"aria-disabled":(0,a.R1)(t).isDateDisabled(e.date)||(null==(l=(r=(0,a.R1)(t)).isDateUnavailable)?void 0:l.call(r,e.date)),"data-disabled":(0,a.R1)(t).isDateDisabled(e.date)?"":void 0},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","aria-selected","aria-disabled","data-disabled"])}}}),Qs=(0,o.pM)({__name:"CalendarHeadCell",props:{asChild:{type:Boolean},as:{default:"th"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),eu=(0,o.pM)({__name:"CalendarNext",props:{step:{default:"month"},nextPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=(0,o.EW)(()=>r.disabled.value||r.isNextButtonDisabled(t.step,t.nextPage)),r=zs();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:t.as,"as-child":t.asChild,"aria-label":"Next page",type:"button"===e.as?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:l[0]||(l[0]=e=>(0,a.R1)(r).nextPage(t.step,t.nextPage))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{},()=>[(0,o.eW)("Next page")])]),_:3},8,["as","as-child","type","aria-disabled","data-disabled","disabled"]))}}),tu=(0,o.pM)({__name:"CalendarPrev",props:{step:{default:"month"},prevPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=(0,o.EW)(()=>r.disabled.value||r.isPrevButtonDisabled(t.step,t.prevPage)),r=zs();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{"aria-label":"Previous page",as:t.as,"as-child":t.asChild,type:"button"===e.as?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:l[0]||(l[0]=e=>(0,a.R1)(r).prevPage(t.step,t.prevPage))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{},()=>[(0,o.eW)("Prev page")])]),_:3},8,["as","as-child","type","aria-disabled","data-disabled","disabled"]))}}),nu=(0,o.pM)({__name:"CalendarGridHead",props:{asChild:{type:Boolean},as:{default:"thead"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"aria-hidden":"true"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),ou=(0,o.pM)({__name:"CalendarGridBody",props:{asChild:{type:Boolean},as:{default:"tbody"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),au=(0,o.pM)({__name:"CalendarGridRow",props:{asChild:{type:Boolean},as:{default:"tr"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),ru=(0,o.pM)({__name:"CalendarCellTrigger",props:{day:{},month:{},asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e,n=yi(),i=zs(),{primitiveElement:s,currentElement:u}=wi(),d=(0,o.EW)(()=>t.day.day.toLocaleString(i.locale.value)),c=(0,o.EW)(()=>i.formatter.custom(on(t.day),{weekday:"long",month:"long",day:"numeric",year:"numeric"})),f=(0,o.EW)(()=>i.isDateDisabled(t.day)),p=(0,o.EW)(()=>{var e;return null==(e=i.isDateUnavailable)?void 0:e.call(i,t.day)}),v=(0,o.EW)(()=>D(t.day,N())),h=(0,o.EW)(()=>!C(t.day,t.month)),m=(0,o.EW)(()=>i.isOutsideVisibleView(t.day)),y=(0,o.EW)(()=>!i.disabled.value&&E(t.day,i.placeholder.value)),g=(0,o.EW)(()=>i.isDateSelected(t.day)),b="[data-radix-vue-calendar-cell-trigger]:not([data-disabled]):not([data-outside-view]):not([data-outside-visible-view])";function w(e){var t;i.readonly.value||i.isDateDisabled(e)||null!=(t=i.isDateUnavailable)&&t.call(i,e)||i.onDateChange(e)}function R(){w(t.day)}function x(e){e.preventDefault(),e.stopPropagation();const a=i.parentElement.value,r=a?Array.from(a.querySelectorAll(b)):[];let l=r.indexOf(u.value);const s=7,d="rtl"===i.dir.value?-1:1;switch(e.code){case n.ARROW_RIGHT:l+=d;break;case n.ARROW_LEFT:l-=d;break;case n.ARROW_UP:l-=s;break;case n.ARROW_DOWN:l+=s;break;case n.ENTER:case n.SPACE_CODE:return void w(t.day);default:return}if(l>=0&&l<r.length)r[l].focus();else{if(l<0){if(i.isPrevButtonDisabled("month"))return;return i.prevPage(),void(0,o.dY)(()=>{const e=a?Array.from(a.querySelectorAll(b)):[];if(!i.pagedNavigation.value){const t=sn(i.placeholder.value);return void e[t-Math.abs(l)].focus()}e[e.length-Math.abs(l)].focus()})}if(l>=r.length){if(i.isNextButtonDisabled("month"))return;i.nextPage(),(0,o.dY)(()=>{const e=a?Array.from(a.querySelectorAll(b)):[];if(!i.pagedNavigation.value){const t=sn(i.placeholder.value.add({months:i.numberOfMonths.value-1}));return void e[e.length-t+l-r.length].focus()}e[l-r.length].focus()})}}}return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({ref_key:"primitiveElement",ref:s},t,{role:"button","aria-label":c.value,"data-radix-vue-calendar-cell-trigger":"","aria-disabled":!(!f.value&&!p.value)||void 0,"data-selected":!!g.value||void 0,"data-value":e.day.toString(),"data-disabled":f.value?"":void 0,"data-unavailable":p.value?"":void 0,"data-today":v.value?"":void 0,"data-outside-view":h.value?"":void 0,"data-outside-visible-view":m.value?"":void 0,"data-focused":y.value?"":void 0,tabindex:y.value?0:h.value||f.value?void 0:-1,onClick:R,onKeydown:[(0,l.jR)(x,["up","down","left","right","space","enter"]),n[0]||(n[0]=(0,l.jR)((0,l.D$)(()=>{},["prevent"]),["enter"]))]}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{dayValue:d.value},()=>[(0,o.eW)((0,r.v_)(d.value),1)])]),_:3},16,["aria-label","aria-disabled","data-selected","data-value","data-disabled","data-unavailable","data-today","data-outside-view","data-outside-visible-view","data-focused","tabindex"]))}});function lu(e){return"indeterminate"===e}function iu(e){return lu(e)?"indeterminate":e?"checked":"unchecked"}const su=["value","checked","name","disabled","required"],[uu,du]=Za("CheckboxRoot"),cu=(0,o.pM)({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultChecked:{type:Boolean},checked:{type:[Boolean,String],default:void 0},disabled:{type:Boolean},required:{type:Boolean},name:{},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"}},emits:["update:checked"],setup(e,{emit:t}){const n=e,r=t,{disabled:i}=(0,a.QW)(n),s=hl(n,"checked",r,{defaultValue:n.defaultChecked,passive:void 0===n.checked}),{forwardRef:u,currentElement:d}=Gl(),c=Ll(d),f=(0,o.EW)(()=>{var e;return n.id&&d.value?null==(e=document.querySelector(`[for="${n.id}"]`))?void 0:e.innerText:void 0});return du({disabled:i,state:s}),(e,t)=>((0,o.uX)(),(0,o.CE)(o.FK,null,[(0,o.bF)((0,a.R1)(bi),(0,o.v6)(e.$attrs,{id:e.id,ref:(0,a.R1)(u),role:"checkbox","as-child":n.asChild,as:e.as,type:"button"===e.as?"button":void 0,"aria-checked":(0,a.R1)(lu)((0,a.R1)(s))?"mixed":(0,a.R1)(s),"aria-required":n.required,"aria-label":e.$attrs["aria-label"]||f.value,"data-state":(0,a.R1)(iu)((0,a.R1)(s)),"data-disabled":(0,a.R1)(i)?"":void 0,disabled:(0,a.R1)(i),onKeydown:(0,l.jR)((0,l.D$)(()=>{},["prevent"]),["enter"]),onClick:t[0]||(t[0]=e=>s.value=!!(0,a.R1)(lu)((0,a.R1)(s))||!(0,a.R1)(s))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{checked:(0,a.R1)(s)})]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","onKeydown"]),(0,a.R1)(c)?((0,o.uX)(),(0,o.CE)("input",{key:0,type:"checkbox",tabindex:"-1","aria-hidden":"true",value:e.value,checked:!!(0,a.R1)(s),name:n.name,disabled:n.disabled,required:n.required,style:{transform:"translateX(-100%)",position:"absolute",pointerEvents:"none",opacity:0,margin:0}},null,8,su)):(0,o.Q3)("",!0)],64))}}),fu=(0,o.pM)({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const{forwardRef:t}=Gl(),n=uu();return(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(lu)((0,a.R1)(n).state.value)||!0===(0,a.R1)(n).state.value},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(t),"data-state":(0,a.R1)(iu)((0,a.R1)(n).state.value),"data-disabled":(0,a.R1)(n).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":e.asChild,as:e.as},e.$attrs),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}}),[pu,vu]=Za("PopperRoot"),hu=(0,o.pM)({inheritAttrs:!1,__name:"PopperRoot",setup(e){const t=(0,a.KR)();return vu({anchor:t,onAnchorChange:e=>t.value=e}),(e,t)=>(0,o.RG)(e.$slots,"default")}}),mu=(0,o.pM)({__name:"PopperAnchor",props:{element:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,{forwardRef:n,currentElement:r}=Gl(),l=pu();return(0,o.nT)(()=>{l.onAnchorChange(t.element??r.value)}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{ref:(0,a.R1)(n),as:e.as,"as-child":e.asChild},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child"]))}});function yu(e){return null!==e}function gu(e){return{name:"transformOrigin",options:e,fn(t){var n,o,a;const{placement:r,rects:l,middlewareData:i}=t,s=0!==(null==(n=i.arrow)?void 0:n.centerOffset),u=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[c,f]=bu(r),p={start:"0%",center:"50%",end:"100%"}[f],v=((null==(o=i.arrow)?void 0:o.x)??0)+u/2,h=((null==(a=i.arrow)?void 0:a.y)??0)+d/2;let m="",y="";return"bottom"===c?(m=s?p:`${v}px`,y=-d+"px"):"top"===c?(m=s?p:`${v}px`,y=`${l.floating.height+d}px`):"right"===c?(m=-d+"px",y=s?p:`${h}px`):"left"===c&&(m=`${l.floating.width+d}px`,y=s?p:`${h}px`),{data:{x:m,y}}}}}function bu(e){const[t,n="center"]=e.split("-");return[t,n]}const wu={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,updatePositionStrategy:"optimized",prioritizePosition:!1},[Ru,xu]=Za("PopperContent"),Bu=(0,o.pM)({inheritAttrs:!1,__name:"PopperContent",props:(0,o.HF)({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},{...wu}),emits:["placed"],setup(e,{emit:t}){const n=e,l=t,i=pu(),{forwardRef:s,currentElement:u}=Gl(),d=(0,a.KR)(),c=(0,a.KR)(),{width:f,height:p}=ci(c),v=(0,o.EW)(()=>n.side+("center"!==n.align?`-${n.align}`:"")),h=(0,o.EW)(()=>"number"==typeof n.collisionPadding?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),m=(0,o.EW)(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),y=(0,o.EW)(()=>({padding:h.value,boundary:m.value.filter(yu),altBoundary:m.value.length>0})),g=Cr(()=>[ma({mainAxis:n.sideOffset+p.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&ga({...y.value}),n.avoidCollisions&&ya({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:"partial"===n.sticky?xa():void 0,...y.value}),!n.prioritizePosition&&n.avoidCollisions&&ga({...y.value}),ba({...y.value,apply:({elements:e,rects:t,availableWidth:n,availableHeight:o})=>{const{width:a,height:r}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${o}px`),l.setProperty("--radix-popper-anchor-width",`${a}px`),l.setProperty("--radix-popper-anchor-height",`${r}px`)}}),c.value&&ka({element:c.value,padding:n.arrowPadding}),gu({arrowWidth:f.value,arrowHeight:p.value}),n.hideWhenDetached&&wa({strategy:"referenceHidden",...y.value})]),{floatingStyles:b,placement:w,isPositioned:R,middlewareData:x}=Da(i.anchor,d,{strategy:"fixed",placement:v,whileElementsMounted:(...e)=>ha(...e,{animationFrame:"always"===n.updatePositionStrategy}),middleware:g}),B=(0,o.EW)(()=>bu(w.value)[0]),_=(0,o.EW)(()=>bu(w.value)[1]);(0,o.p9)(()=>{R.value&&l("placed")});const E=(0,o.EW)(()=>{var e;return 0!==(null==(e=x.value.arrow)?void 0:e.centerOffset)}),C=(0,a.KR)("");(0,o.nT)(()=>{u.value&&(C.value=window.getComputedStyle(u.value).zIndex)});const k=(0,o.EW)(()=>{var e;return(null==(e=x.value.arrow)?void 0:e.x)??0}),M=(0,o.EW)(()=>{var e;return(null==(e=x.value.arrow)?void 0:e.y)??0});return xu({placedSide:B,onArrowChange:e=>c.value=e,arrowX:k,arrowY:M,shouldHideArrow:E}),(e,t)=>{var l,i,u;return(0,o.uX)(),(0,o.CE)("div",{ref_key:"floatingRef",ref:d,"data-radix-popper-content-wrapper":"",style:(0,r.Tr)({...(0,a.R1)(b),transform:(0,a.R1)(R)?(0,a.R1)(b).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:C.value,"--radix-popper-transform-origin":[null==(l=(0,a.R1)(x).transformOrigin)?void 0:l.x,null==(i=(0,a.R1)(x).transformOrigin)?void 0:i.y].join(" "),...(null==(u=(0,a.R1)(x).hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(s)},e.$attrs,{"as-child":n.asChild,as:e.as,"data-side":B.value,"data-align":_.value,style:{animation:(0,a.R1)(R)?void 0:"none"}}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}}),_u=(0,o.Lk)("polygon",{points:"0,0 30,0 15,10"},null,-1),Eu=(0,o.pM)({__name:"Arrow",props:{width:{default:10},height:{default:5},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const t=e;return Gl(),(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{width:e.width,height:e.height,viewBox:e.asChild?void 0:"0 0 30 10",preserveAspectRatio:e.asChild?void 0:"none"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{},()=>[_u])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}}),Cu={top:"bottom",right:"left",bottom:"top",left:"right"},ku=(0,o.pM)({inheritAttrs:!1,__name:"PopperArrow",props:{width:{},height:{},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const{forwardRef:t}=Gl(),n=Ru(),l=(0,o.EW)(()=>Cu[n.placedSide.value]);return(e,i)=>{var s,u,d,c;return(0,o.uX)(),(0,o.CE)("span",{ref:e=>{(0,a.R1)(n).onArrowChange(e)},style:(0,r.Tr)({position:"absolute",left:null!=(s=(0,a.R1)(n).arrowX)&&s.value?`${null==(u=(0,a.R1)(n).arrowX)?void 0:u.value}px`:void 0,top:null!=(d=(0,a.R1)(n).arrowY)&&d.value?`${null==(c=(0,a.R1)(n).arrowY)?void 0:c.value}px`:void 0,[l.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[(0,a.R1)(n).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[(0,a.R1)(n).placedSide.value],visibility:(0,a.R1)(n).shouldHideArrow.value?"hidden":void 0})},[(0,o.bF)(Eu,(0,o.v6)(e.$attrs,{ref:(0,a.R1)(t),style:{display:"block"},as:e.as,"as-child":e.asChild,width:e.width,height:e.height}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child","width","height"])],4)}}}),Mu=(0,o.pM)({__name:"VisuallyHidden",props:{asChild:{type:Boolean},as:{default:"span"}},setup(e){return Gl(),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,style:{position:"absolute",border:0,width:"1px",display:"inline-block",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child"]))}}),Su=(0,o.pM)({__name:"VisuallyHiddenInput",props:{name:{},value:{},required:{type:Boolean},disabled:{type:Boolean}},setup(e){const t=e,n=(0,o.EW)(()=>"string"==typeof t.value||"number"==typeof t.value||"boolean"==typeof t.value?[{name:t.name,value:t.value}]:"object"==typeof t.value&&Array.isArray(t.value)?t.value.flatMap((e,n)=>"object"==typeof e?Object.entries(e).map(([e,o])=>({name:`[${n}][${t.name}][${e}]`,value:o})):{name:`[${t.name}][${n}]`,value:e}):null===t.value||"object"!=typeof t.value||Array.isArray(t.value)?[]:Object.entries(t.value).map(([e,n])=>({name:`[${t.name}][${e}]`,value:n})));return(e,t)=>((0,o.uX)(!0),(0,o.CE)(o.FK,null,(0,o.pI)(n.value,t=>((0,o.uX)(),(0,o.Wv)(Mu,{key:t.name,as:"input",type:"hidden",hidden:"",readonly:"",name:t.name,value:t.value,required:e.required,disabled:e.disabled},null,8,["name","value","required","disabled"]))),128))}}),Du="data-radix-vue-collection-item",[Au,Ou]=Za("CollectionProvider");function Tu(e=Du){const t=(0,a.KR)(new Map),n=(0,a.KR)(),r=Ou({collectionRef:n,itemMap:t,attrName:e}),{getItems:l}=Wu(r),i=(0,o.EW)(()=>Array.from(r.itemMap.value.values())),s=(0,o.EW)(()=>r.itemMap.value.size);return{getItems:l,reactiveItems:i,itemMapSize:s}}const Pu=(0,o.pM)({name:"CollectionSlot",setup(e,{slots:t}){const n=Au(),{primitiveElement:a,currentElement:r}=wi();return(0,o.wB)(r,()=>{n.collectionRef.value=r.value}),()=>(0,o.h)(gi,{ref:a},t)}}),Iu=(0,o.pM)({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(e,{slots:t,attrs:n}){const r=Au(),{primitiveElement:l,currentElement:i}=wi();return(0,o.nT)(t=>{if(i.value){const n=(0,a.IG)(i.value);r.itemMap.value.set(n,{ref:i.value,value:e.value}),t(()=>r.itemMap.value.delete(n))}}),()=>(0,o.h)(gi,{...n,[r.attrName]:"",ref:l},t)}});function Wu(e){const t=e??Au();return{getItems:()=>{const e=t.collectionRef.value;if(!e)return[];const n=Array.from(e.querySelectorAll(`[${t.attrName}]`));return Array.from(t.itemMap.value.values()).sort((e,t)=>n.indexOf(e.ref)-n.indexOf(t.ref))}}}const[Fu,$u]=Za("ComboboxRoot"),[Nu,Vu]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("ComboboxGroup")),[Lu,Ku]=(Boolean,Boolean,Za("ComboboxContent")),ju=(0,o.pM)({__name:"ComboboxContentImpl",props:{position:{default:"inline"},bodyLock:{type:Boolean},dismissable:{type:Boolean,default:!0},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside"],setup(e,{emit:t}){const n=e,r=t,{position:l}=(0,a.QW)(n),i=Fu();Dl(n.bodyLock);const{forwardRef:s,currentElement:u}=Gl();ii(i.parentElement);const d=(0,o.EW)(()=>"popper"===n.position?n:{}),c=Kl(d.value);function f(e){i.onSelectedValueChange("")}(0,o.sV)(()=>{i.onContentElementChange(u.value)});const p={boxSizing:"border-box","--radix-combobox-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-combobox-content-available-width":"var(--radix-popper-available-width)","--radix-combobox-content-available-height":"var(--radix-popper-available-height)","--radix-combobox-trigger-width":"var(--radix-popper-anchor-width)","--radix-combobox-trigger-height":"var(--radix-popper-anchor-height)"};return Ku({position:l}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Pu),null,{default:(0,o.k6)(()=>[e.dismissable?((0,o.uX)(),(0,o.Wv)((0,a.R1)(Hi),{key:0,"as-child":"","disable-outside-pointer-events":e.disableOutsidePointerEvents,onDismiss:t[0]||(t[0]=e=>(0,a.R1)(i).onOpenChange(!1)),onFocusOutside:t[1]||(t[1]=e=>{var t;null!=(t=(0,a.R1)(i).parentElement.value)&&t.contains(e.target)&&e.preventDefault(),r("focusOutside",e)}),onInteractOutside:t[2]||(t[2]=e=>r("interactOutside",e)),onEscapeKeyDown:t[3]||(t[3]=e=>r("escapeKeyDown",e)),onPointerDownOutside:t[4]||(t[4]=e=>{var t;null!=(t=(0,a.R1)(i).parentElement.value)&&t.contains(e.target)&&e.preventDefault(),r("pointerDownOutside",e)})},{default:(0,o.k6)(()=>[((0,o.uX)(),(0,o.Wv)((0,o.$y)("popper"===(0,a.R1)(l)?(0,a.R1)(Bu):(0,a.R1)(bi)),(0,o.v6)({...e.$attrs,...(0,a.R1)(c)},{id:(0,a.R1)(i).contentId,ref:(0,a.R1)(s),role:"listbox","data-state":(0,a.R1)(i).open.value?"open":"closed",style:{display:"flex",flexDirection:"column",outline:"none",..."popper"===(0,a.R1)(l)?p:{}},onPointerleave:f}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","data-state","style"]))]),_:3},8,["disable-outside-pointer-events"])):((0,o.uX)(),(0,o.Wv)((0,o.$y)("popper"===(0,a.R1)(l)?(0,a.R1)(Bu):(0,a.R1)(bi)),(0,o.v6)({key:1},{...e.$attrs,...d.value},{id:(0,a.R1)(i).contentId,ref:(0,a.R1)(s),role:"listbox","data-state":(0,a.R1)(i).open.value?"open":"closed",style:{display:"flex",flexDirection:"column",outline:"none",..."popper"===(0,a.R1)(l)?p:{}},onPointerleave:f}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","data-state","style"]))]),_:3}))}});Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;function Gu(e){const t=El({nonce:(0,a.KR)()});return(0,o.EW)(()=>{var n;return(null==e?void 0:e.value)||(null==(n=t.nonce)?void 0:n.value)})}Boolean;const[Xu,Uu]=Za("ComboboxItem"),zu="combobox.select",Hu=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"MenuAnchor",props:{element:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(mu),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}})),Yu=(0,o.pM)({__name:"MenuArrow",props:{width:{},height:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ku),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}});function Zu(){const e=(0,a.KR)(!1);return(0,o.sV)(()=>{al("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),al(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const qu=Dr(Zu),[Ju,Qu]=Za(["MenuRoot","MenuSub"],"MenuContext"),[ed,td]=Za("MenuRoot"),nd=(0,o.pM)({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,{modal:l,dir:i}=(0,a.QW)(n),s=Wl(i),u=hl(n,"open",r),d=(0,a.KR)(),c=qu();return Qu({open:u,onOpenChange:e=>{u.value=e},content:d,onContentChange:e=>{d.value=e}}),td({onClose:()=>{u.value=!1},isUsingKeyboardRef:c,dir:s,modal:l}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(hu),null,{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3}))}}),od="rovingFocusGroup.onEntryFocus",ad={bubbles:!1,cancelable:!0},rd={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ld(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}function id(e,t,n){const o=ld(e.key,n);if(("vertical"!==t||!["ArrowLeft","ArrowRight"].includes(o))&&("horizontal"!==t||!["ArrowUp","ArrowDown"].includes(o)))return rd[o]}function sd(e,t=!1){const n=yl();for(const o of e)if(o===n||(o.focus({preventScroll:t}),yl()!==n))return}function ud(e,t){return e.map((n,o)=>e[(t+o)%e.length])}const[dd,cd]=Za("RovingFocusGroup"),fd=(0,o.pM)({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(e,{expose:t,emit:n}){const r=e,l=n,{loop:i,orientation:s,dir:u}=(0,a.QW)(r),d=Wl(u),c=hl(r,"currentTabStopId",l,{defaultValue:r.defaultCurrentTabStopId,passive:void 0===r.currentTabStopId}),f=(0,a.KR)(!1),p=(0,a.KR)(!1),v=(0,a.KR)(0),{getItems:h}=Tu();function m(e){const t=!p.value;if(e.currentTarget&&e.target===e.currentTarget&&t&&!f.value){const t=new CustomEvent(od,ad);if(e.currentTarget.dispatchEvent(t),l("entryFocus",t),!t.defaultPrevented){const e=h().map(e=>e.ref).filter(e=>""!==e.dataset.disabled),t=e.find(e=>"true"===e.getAttribute("data-active")),n=e.find(e=>e.id===c.value),o=[t,n,...e].filter(Boolean);sd(o,r.preventScrollOnEntryFocus)}}p.value=!1}function y(){setTimeout(()=>{p.value=!1},1)}return t({getItems:h}),cd({loop:i,dir:d,orientation:s,currentTabStopId:c,onItemFocus:e=>{c.value=e},onItemShiftTab:()=>{f.value=!0},onFocusableItemAdd:()=>{v.value++},onFocusableItemRemove:()=>{v.value--}}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Pu),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),{tabindex:f.value||0===v.value?-1:0,"data-orientation":(0,a.R1)(s),as:e.as,"as-child":e.asChild,dir:(0,a.R1)(d),style:{outline:"none"},onMousedown:t[0]||(t[0]=e=>p.value=!0),onMouseup:y,onFocus:m,onBlur:t[1]||(t[1]=e=>f.value=!1)},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}}),pd=(0,o.pM)({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!0},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const t=e,n=dd(),r=(0,o.EW)(()=>t.tabStopId||ui()),l=(0,o.EW)(()=>n.currentTabStopId.value===r.value),{getItems:i}=Wu();function s(e){if("Tab"===e.key&&e.shiftKey)return void n.onItemShiftTab();if(e.target!==e.currentTarget)return;const a=id(e,n.orientation.value,n.dir.value);if(void 0!==a){if(e.metaKey||e.ctrlKey||e.altKey||!t.allowShiftKey&&e.shiftKey)return;e.preventDefault();let r=[...i().map(e=>e.ref).filter(e=>""!==e.dataset.disabled)];if("last"===a)r.reverse();else if("prev"===a||"next"===a){"prev"===a&&r.reverse();const t=r.indexOf(e.currentTarget);r=n.loop.value?ud(r,t+1):r.slice(t+1)}(0,o.dY)(()=>sd(r))}}return(0,o.sV)(()=>{t.focusable&&n.onFocusableItemAdd()}),(0,o.hi)(()=>{t.focusable&&n.onFocusableItemRemove()}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Iu),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),{tabindex:l.value?0:-1,"data-orientation":(0,a.R1)(n).orientation.value,"data-active":e.active,"data-disabled":e.focusable?void 0:"",as:e.as,"as-child":e.asChild,onMousedown:t[0]||(t[0]=t=>{e.focusable?(0,a.R1)(n).onItemFocus(r.value):t.preventDefault()}),onFocus:t[1]||(t[1]=e=>(0,a.R1)(n).onItemFocus(r.value)),onKeydown:s},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])]),_:3}))}}),[vd,hd]=Za("MenuContent"),md=(0,o.pM)({__name:"MenuContentImpl",props:(0,o.HF)({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},{...wu}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:t}){const n=e,r=t,l=Ju(),i=ed(),{trapFocus:s,disableOutsidePointerEvents:u,loop:d}=(0,a.QW)(n);Nl(),Dl(u.value);const c=(0,a.KR)(""),f=(0,a.KR)(0),p=(0,a.KR)(0),v=(0,a.KR)(null),h=(0,a.KR)("right"),m=(0,a.KR)(0),y=(0,a.KR)(null),{createCollection:g}=Pl(),{forwardRef:b,currentElement:w}=Gl(),R=g(w);(0,o.wB)(w,e=>{l.onContentChange(e)});const{handleTypeaheadSearch:x}=vi(R);function B(e){var t,n;return h.value===(null==(t=v.value)?void 0:t.side)&&Bs(e,null==(n=v.value)?void 0:n.area)}async function _(e){var t;r("openAutoFocus",e),!e.defaultPrevented&&(e.preventDefault(),null==(t=w.value)||t.focus({preventScroll:!0}))}function E(e){if(e.defaultPrevented)return;const t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,o=1===e.key.length,a=bl(e,yl(),w.value,{loop:d.value,arrowKeyOptions:"vertical",dir:null==i?void 0:i.dir.value,focus:!0,attributeName:"[data-radix-vue-collection-item]:not([data-disabled])"});if(a)return null==a?void 0:a.focus();if("Space"===e.code||(t&&("Tab"===e.key&&e.preventDefault(),!n&&o&&x(e.key)),e.target!==w.value)||!hs.includes(e.key))return;e.preventDefault();const r=R.value;vs.includes(e.key)&&r.reverse(),Rs(r)}function C(e){var t,n;null!=(n=null==(t=null==e?void 0:e.currentTarget)?void 0:t.contains)&&n.call(t,e.target)||(window.clearTimeout(f.value),c.value="")}function k(e){var t;if(!_s(e))return;const n=e.target,o=m.value!==e.clientX;if(null!=(t=null==e?void 0:e.currentTarget)&&t.contains(n)&&o){const t=e.clientX>m.value?"right":"left";h.value=t,m.value=e.clientX}}return(0,o.hi)(()=>{window.clearTimeout(f.value)}),hd({onItemEnter:e=>!!B(e),onItemLeave:e=>{var t;B(e)||(null==(t=w.value)||t.focus(),y.value=null)},onTriggerLeave:e=>!!B(e),searchRef:c,pointerGraceTimerRef:p,onPointerGraceIntentChange:e=>{v.value=e}}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ds),{"as-child":"",trapped:(0,a.R1)(s),onMountAutoFocus:_,onUnmountAutoFocus:t[7]||(t[7]=e=>r("closeAutoFocus",e))},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Hi),{"as-child":"","disable-outside-pointer-events":(0,a.R1)(u),onEscapeKeyDown:t[2]||(t[2]=e=>r("escapeKeyDown",e)),onPointerDownOutside:t[3]||(t[3]=e=>r("pointerDownOutside",e)),onFocusOutside:t[4]||(t[4]=e=>r("focusOutside",e)),onInteractOutside:t[5]||(t[5]=e=>r("interactOutside",e)),onDismiss:t[6]||(t[6]=e=>r("dismiss"))},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(fd),{"current-tab-stop-id":y.value,"onUpdate:currentTabStopId":t[0]||(t[0]=e=>y.value=e),"as-child":"",orientation:"vertical",dir:(0,a.R1)(i).dir.value,loop:(0,a.R1)(d),onEntryFocus:t[1]||(t[1]=e=>{r("entryFocus",e),(0,a.R1)(i).isUsingKeyboardRef.value||e.preventDefault()})},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Bu),{ref:(0,a.R1)(b),role:"menu",as:e.as,"as-child":e.asChild,"aria-orientation":"vertical","data-radix-menu-content":"","data-state":(0,a.R1)(gs)((0,a.R1)(l).open.value),dir:(0,a.R1)(i).dir.value,side:e.side,"side-offset":e.sideOffset,align:e.align,"align-offset":e.alignOffset,"avoid-collisions":e.avoidCollisions,"collision-boundary":e.collisionBoundary,"collision-padding":e.collisionPadding,"arrow-padding":e.arrowPadding,"prioritize-position":e.prioritizePosition,sticky:e.sticky,"hide-when-detached":e.hideWhenDetached,onKeydown:E,onBlur:C,onPointermove:k},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","sticky","hide-when-detached"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),yd=(0,o.pM)({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=vd(),{forwardRef:r}=Gl(),l=(0,a.KR)(!1);async function i(e){if(!e.defaultPrevented&&_s(e))if(t.disabled)n.onItemLeave(e);else if(!n.onItemEnter(e)){const t=e.currentTarget;null==t||t.focus({preventScroll:!0})}}async function s(e){await(0,o.dY)(),!e.defaultPrevented&&_s(e)&&n.onItemLeave(e)}return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Iu),{value:{textValue:e.textValue}},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(r),role:"menuitem",tabindex:"-1"},e.$attrs,{as:e.as,"as-child":e.asChild,"data-radix-vue-collection-item":"","aria-disabled":e.disabled||void 0,"data-disabled":e.disabled?"":void 0,"data-highlighted":l.value?"":void 0,onPointermove:i,onPointerleave:s,onFocus:t[0]||(t[0]=async t=>{await(0,o.dY)(),!t.defaultPrevented&&!e.disabled&&(l.value=!0)}),onBlur:t[1]||(t[1]=async e=>{await(0,o.dY)(),!e.defaultPrevented&&(l.value=!1)})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),gd=(0,o.pM)({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:t}){const n=e,r=t,{forwardRef:l,currentElement:i}=Gl(),s=ed(),u=vd(),d=(0,a.KR)(!1);async function c(){const e=i.value;if(!n.disabled&&e){const e=new CustomEvent(cs,{bubbles:!0,cancelable:!0});r("select",e),await(0,o.dY)(),e.defaultPrevented?d.value=!1:s.onClose()}}return(e,t)=>((0,o.uX)(),(0,o.Wv)(yd,(0,o.v6)(n,{ref:(0,a.R1)(l),onClick:c,onPointerdown:t[0]||(t[0]=()=>{d.value=!0}),onPointerup:t[1]||(t[1]=async e=>{var t;await(0,o.dY)(),!e.defaultPrevented&&(d.value||null==(t=e.currentTarget)||t.click())}),onKeydown:t[2]||(t[2]=async t=>{const n=""!==(0,a.R1)(u).searchRef.value;e.disabled||n&&" "===t.key||(0,a.R1)(fs).includes(t.key)&&(t.currentTarget.click(),t.preventDefault())})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),[bd,wd]=Za(["MenuCheckboxItem","MenuRadioItem"],"MenuItemIndicatorContext"),Rd=(0,o.pM)({__name:"MenuItemIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const t=bd({checked:(0,a.KR)(!1)});return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(bs)((0,a.R1)(t).checked.value)||!0===(0,a.R1)(t).checked.value},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,"data-state":(0,a.R1)(ws)((0,a.R1)(t).checked.value)},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","data-state"])]),_:3},8,["present"]))}}),xd=(0,o.pM)({__name:"MenuCheckboxItem",props:{checked:{type:[Boolean,String],default:!1},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select","update:checked"],setup(e,{emit:t}){const n=e,r=t,l=hl(n,"checked",r);return wd({checked:l}),(e,t)=>((0,o.uX)(),(0,o.Wv)(gd,(0,o.v6)({role:"menuitemcheckbox"},n,{"aria-checked":(0,a.R1)(bs)((0,a.R1)(l))?"mixed":(0,a.R1)(l),"data-state":(0,a.R1)(ws)((0,a.R1)(l)),onSelect:t[0]||(t[0]=async e=>{r("select",e),(0,a.R1)(bs)((0,a.R1)(l))?l.value=!0:l.value=!(0,a.R1)(l)})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{checked:(0,a.R1)(l)})]),_:3},16,["aria-checked","data-state"]))}}),Bd=(0,o.pM)({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,i=jl(n,r),s=Ju(),{forwardRef:u,currentElement:d}=Gl();return ii(d),(e,t)=>((0,o.uX)(),(0,o.Wv)(md,(0,o.v6)((0,a.R1)(i),{ref:(0,a.R1)(u),"trap-focus":(0,a.R1)(s).open.value,"disable-outside-pointer-events":(0,a.R1)(s).open.value,"disable-outside-scroll":!0,onDismiss:t[0]||(t[0]=e=>(0,a.R1)(s).onOpenChange(!1)),onFocusOutside:t[1]||(t[1]=(0,l.D$)(e=>r("focusOutside",e),["prevent"]))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),_d=(0,o.pM)({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=jl(e,t),r=Ju();return(e,t)=>((0,o.uX)(),(0,o.Wv)(md,(0,o.v6)((0,a.R1)(n),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:t[0]||(t[0]=e=>(0,a.R1)(r).onOpenChange(!1))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Ed=(0,o.pM)({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=jl(e,t),l=Ju(),i=ed();return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(l).open.value},{default:(0,o.k6)(()=>[(0,a.R1)(i).modal.value?((0,o.uX)(),(0,o.Wv)(Bd,(0,r._B)((0,o.v6)({key:0},{...e.$attrs,...(0,a.R1)(n)})),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)):((0,o.uX)(),(0,o.Wv)(_d,(0,r._B)((0,o.v6)({key:1},{...e.$attrs,...(0,a.R1)(n)})),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Cd=(0,o.pM)({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({role:"group"},t),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),kd=(0,o.pM)({__name:"MenuLabel",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Md=(0,o.pM)({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Li),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),[Sd,Dd]=Za("MenuRadioGroup"),Ad=(0,o.pM)({__name:"MenuRadioGroup",props:{modelValue:{default:""},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,l=hl(n,"modelValue",t);return Dd({modelValue:l,onValueChange:e=>{l.value=e}}),(e,t)=>((0,o.uX)(),(0,o.Wv)(Cd,(0,r._B)((0,o.Ng)(n)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{modelValue:(0,a.R1)(l)})]),_:3},16))}}),Od=(0,o.pM)({__name:"MenuRadioItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(e,{emit:t}){const n=e,r=t,{value:l}=(0,a.QW)(n),i=Sd(),s=(0,o.EW)(()=>i.modelValue.value===(null==l?void 0:l.value));return wd({checked:s}),(e,t)=>((0,o.uX)(),(0,o.Wv)(gd,(0,o.v6)({role:"menuitemradio"},n,{"aria-checked":s.value,"data-state":(0,a.R1)(ws)(s.value),onSelect:t[0]||(t[0]=async e=>{r("select",e),(0,a.R1)(i).onValueChange((0,a.R1)(l))})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["aria-checked","data-state"]))}}),Td=(0,o.pM)({__name:"MenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{role:"separator","aria-orientation":"horizontal"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),[Pd,Id]=Za("MenuSub"),Wd=(0,o.pM)({__name:"MenuSub",props:{open:{type:Boolean,default:void 0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=hl(n,"open",t,{defaultValue:!1,passive:void 0===n.open}),l=Ju(),i=(0,a.KR)(),s=(0,a.KR)();return(0,o.nT)(e=>{!1===(null==l?void 0:l.open.value)&&(r.value=!1),e(()=>r.value=!1)}),Qu({open:r,onOpenChange:e=>{r.value=e},content:s,onContentChange:e=>{s.value=e}}),Id({triggerId:"",contentId:"",trigger:i,onTriggerChange:e=>{i.value=e}}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(hu),null,{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3}))}}),Fd=(0,o.pM)({__name:"MenuSubContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},sideOffset:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=jl(e,t),r=Ju(),i=ed(),s=Pd(),{forwardRef:u,currentElement:d}=Gl();return s.contentId||(s.contentId=ui(void 0,"radix-vue-menu-sub-content")),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(r).open.value},{default:(0,o.k6)(()=>[(0,o.bF)(md,(0,o.v6)((0,a.R1)(n),{id:(0,a.R1)(s).contentId,ref:(0,a.R1)(u),"aria-labelledby":(0,a.R1)(s).triggerId,align:"start",side:"rtl"===(0,a.R1)(i).dir.value?"left":"right","disable-outside-pointer-events":!1,"disable-outside-scroll":!1,"trap-focus":!1,onOpenAutoFocus:t[0]||(t[0]=(0,l.D$)(e=>{var t;(0,a.R1)(i).isUsingKeyboardRef.value&&(null==(t=(0,a.R1)(d))||t.focus())},["prevent"])),onCloseAutoFocus:t[1]||(t[1]=(0,l.D$)(()=>{},["prevent"])),onFocusOutside:t[2]||(t[2]=e=>{e.defaultPrevented||e.target!==(0,a.R1)(s).trigger.value&&(0,a.R1)(r).onOpenChange(!1)}),onEscapeKeyDown:t[3]||(t[3]=e=>{(0,a.R1)(i).onClose(),e.preventDefault()}),onKeydown:t[4]||(t[4]=e=>{var t,n;const o=null==(t=e.currentTarget)?void 0:t.contains(e.target),l=(0,a.R1)(ys)[(0,a.R1)(i).dir.value].includes(e.key);o&&l&&((0,a.R1)(r).onOpenChange(!1),null==(n=(0,a.R1)(s).trigger.value)||n.focus(),e.preventDefault())})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","aria-labelledby","side"])]),_:3},8,["present"]))}}),$d=(0,o.pM)({__name:"MenuSubTrigger",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Ju(),r=ed(),l=Pd(),i=vd(),s=(0,a.KR)(null);function u(){s.value&&window.clearTimeout(s.value),s.value=null}function d(e){!_s(e)||i.onItemEnter(e)||!t.disabled&&!n.open.value&&!s.value&&(i.onPointerGraceIntentChange(null),s.value=window.setTimeout(()=>{n.onOpenChange(!0),u()},100))}async function c(e){var t,o;if(!_s(e))return;u();const a=null==(t=n.content.value)?void 0:t.getBoundingClientRect();if(null!=a&&a.width){const t=null==(o=n.content.value)?void 0:o.dataset.side,r="right"===t,l=r?-5:5,s=a[r?"left":"right"],u=a[r?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+l,y:e.clientY},{x:s,y:a.top},{x:u,y:a.top},{x:u,y:a.bottom},{x:s,y:a.bottom}],side:t}),window.clearTimeout(i.pointerGraceTimerRef.value),i.pointerGraceTimerRef.value=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e))return;i.onPointerGraceIntentChange(null)}}async function f(e){var a;const l=""!==i.searchRef.value;t.disabled||l&&" "===e.key||ms[r.dir.value].includes(e.key)&&(n.onOpenChange(!0),await(0,o.dY)(),null==(a=n.content.value)||a.focus(),e.preventDefault())}return l.triggerId||(l.triggerId=ui(void 0,"radix-vue-menu-sub-trigger")),(0,o.hi)(()=>{u()}),(e,r)=>((0,o.uX)(),(0,o.Wv)(Hu,{"as-child":""},{default:(0,o.k6)(()=>[(0,o.bF)(yd,(0,o.v6)(t,{id:(0,a.R1)(l).triggerId,ref:e=>{var t;null==(t=(0,a.R1)(l))||t.onTriggerChange(null==e?void 0:e.$el)},"aria-haspopup":"menu","aria-expanded":(0,a.R1)(n).open.value,"aria-controls":(0,a.R1)(l).contentId,"data-state":(0,a.R1)(gs)((0,a.R1)(n).open.value),onClick:r[0]||(r[0]=async e=>{t.disabled||e.defaultPrevented||(e.currentTarget.focus(),(0,a.R1)(n).open.value||(0,a.R1)(n).onOpenChange(!0))}),onPointermove:d,onPointerleave:c,onKeydown:f}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","aria-expanded","aria-controls","data-state"])]),_:3}))}}),[Nd,Vd]=Za("ContextMenuRoot");Boolean;function Ld(e){return"mouse"!==e.pointerType}Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;const Kd=["hour","minute","second"];function jd(e){const{formatter:t}=e,n=br.map(t=>[t,e.value[t]]);if("hour"in e.value){const o=wr.map(n=>"dayPeriod"===n?[n,t.dayPeriod(on(e.value))]:[n,e.value[n]]),a=[...n,...o];return Object.fromEntries(a)}return Object.fromEntries(n)}function Gd(e){const t=Rr.map(e=>"dayPeriod"===e?[e,"AM"]:[e,null]).filter(([t])=>"literal"!==t&&null!==t&&("minute"!==e||"second"!==t)&&("hour"!==e||"second"!==t&&"minute"!==t)&&("day"!==e||!Kd.includes(t)&&"dayPeriod"!==t));return Object.fromEntries(t)}function Xd(e){const{segmentValues:t,formatter:n,locale:o}=e;function a(a){if("hour"in t){const r=t[a];return null!==r?"day"===a&&null!==t.month?n.part(e.dateRef.set({[a]:r,month:t.month}),a,{hourCycle:24===e.hourCycle?"h24":void 0}):n.part(e.dateRef.set({[a]:r}),a,{hourCycle:24===e.hourCycle?"h24":void 0}):pr(a,"",o.value)}if(xr(a)){const r=t[a];return null!==r?"day"===a&&null!==t.month?n.part(e.dateRef.set({[a]:r,month:t.month}),a):n.part(e.dateRef.set({[a]:r}),a):pr(a,"",o.value)}return""}return Object.keys(t).reduce((e,n)=>{if(!Br(n))return e;if("hour"in t&&"dayPeriod"===n){const a=t[n];e[n]=null!==a?a:pr(n,"AM",o.value)}else e[n]=a(n);return e},{})}function Ud(e){const{granularity:t,formatter:n,contentObj:o,hideTimeZone:a,hourCycle:r}=e;return n.toParts(e.dateRef,_r(t,r)).map(e=>["literal","timeZoneName",null].includes(e.type)||!Br(e.type)?{part:e.type,value:e.value}:{part:e.type,value:o[e.type]}).filter(t=>!(null===t.part||null===t.value||"timeZoneName"===t.part&&(!rn(e.dateRef)||a)))}function zd(e){const t=Xd(e),n=Ud({contentObj:t,...e});return{obj:t,arr:n}}function Hd(e){const t=yi();return e===t.ARROW_RIGHT||e===t.ARROW_LEFT}function Yd(e){return!Number.isNaN(Number.parseInt(e))}function Zd(e){const t=yi();return!(![t.ENTER,t.ARROW_UP,t.ARROW_DOWN,t.ARROW_LEFT,t.ARROW_RIGHT,t.BACKSPACE,t.SPACE,"a","A","p","P"].includes(e)&&!Yd(e))}function qd(e){return Array.from(e.querySelectorAll("[data-radix-vue-date-field-segment]")).filter(e=>"literal"!==e.getAttribute("data-radix-vue-date-field-segment"))}const Jd=["id","value","name","disabled","required"],[Qd,ec]=Za("DateFieldRoot");Boolean,Boolean,Boolean,Boolean,Boolean;function tc(e){return{role:"spinbutton",contenteditable:!0,tabindex:e.disabled?void 0:0,spellcheck:!1,inputmode:"numeric",autocorrect:"off",enterkeyhint:"next",style:"caret-color: transparent;"}}function nc(e){const{segmentValues:t,placeholder:n}=e,o=null===t.day,a=t.day?n.set({day:t.day}):n,r=a.day,l=1,i=sn(a),s=o?"Empty":`${r}`;return{...tc(e),"aria-label":"day,","aria-valuemin":l,"aria-valuemax":i,"aria-valuenow":r,"aria-valuetext":s,"data-placeholder":o?"":void 0}}function oc(e){const{segmentValues:t,placeholder:n,formatter:o}=e,a=null===t.month,r=t.month?n.set({month:t.month}):n,l=r.month,i=1,s=12,u=a?"Empty":`${l} - ${o.fullMonth(on(r))}`;return{...tc(e),"aria-label":"month, ",contenteditable:!0,"aria-valuemin":i,"aria-valuemax":s,"aria-valuenow":l,"aria-valuetext":u,"data-placeholder":a?"":void 0}}function ac(e){const{segmentValues:t,placeholder:n}=e,o=null===t.year,a=t.year?n.set({year:t.year}):n,r=1,l=9999,i=a.year,s=o?"Empty":`${i}`;return{...tc(e),"aria-label":"year, ","aria-valuemin":r,"aria-valuemax":l,"aria-valuenow":i,"aria-valuetext":s,"data-placeholder":o?"":void 0}}function rc(e){const{segmentValues:t,hourCycle:n,placeholder:o}=e;if(!("hour"in t)||!("hour"in o))return{};const a=null===t.hour,r=t.hour?o.set({hour:t.hour}):o,l=12===n?1:0,i=12===n?12:23,s=r.hour,u=a?"Empty":`${s} ${t.dayPeriod??""}`;return{...tc(e),"aria-label":"hour, ","aria-valuemin":l,"aria-valuemax":i,"aria-valuenow":s,"aria-valuetext":u,"data-placeholder":a?"":void 0}}function lc(e){const{segmentValues:t,placeholder:n}=e;if(!("minute"in t)||!("minute"in n))return{};const o=null===t.minute,a=(t.minute?n.set({minute:t.minute}):n).minute,r=0,l=59,i=o?"Empty":`${a}`;return{...tc(e),"aria-label":"minute, ","aria-valuemin":r,"aria-valuemax":l,"aria-valuenow":a,"aria-valuetext":i,"data-placeholder":o?"":void 0}}function ic(e){const{segmentValues:t,placeholder:n}=e;if(!("second"in t)||!("second"in n))return{};const o=null===t.second,a=(t.second?n.set({second:t.second}):n).second,r=0,l=59,i=o?"Empty":`${a}`;return{...tc(e),"aria-label":"second, ","aria-valuemin":r,"aria-valuemax":l,"aria-valuenow":a,"aria-valuetext":i,"data-placeholder":o?"":void 0}}function sc(e){const{segmentValues:t}=e;if(!("dayPeriod"in t))return{};const n=0,o=12,a=t.hour?t.hour>12?t.hour-12:t.hour:0,r=t.dayPeriod??"AM";return{...tc(e),inputmode:"text","aria-label":"AM/PM","aria-valuemin":n,"aria-valuemax":o,"aria-valuenow":a,"aria-valuetext":r}}function uc(e){return{"aria-hidden":!0,"data-segment":"literal"}}function dc(e){return{role:"textbox","aria-label":"timezone, ","data-readonly":!0,"data-segment":"timeZoneName",tabindex:e.disabled?void 0:0,style:"caret-color: transparent;"}}function cc(e){const{segmentValues:t,placeholder:n}=e,o=0,a=0,r=0,l="era"in t?t.era:n.era;return{...tc(e),"aria-label":"era","aria-valuemin":o,"aria-valuemax":a,"aria-valuenow":r,"aria-valuetext":l}}const fc={day:{attrs:nc},month:{attrs:oc},year:{attrs:ac},hour:{attrs:rc},minute:{attrs:lc},second:{attrs:ic},dayPeriod:{attrs:sc},literal:{attrs:uc},timeZoneName:{attrs:dc},era:{attrs:cc}};function pc(e){const t=yi();function n({e,part:n,dateRef:o,prevValue:a}){const r=e.key===t.ARROW_UP?1:-1,l=0,i=59;if(null===a)return r>0?l:i;const s=[n,r];return o.set({[n]:a}).cycle(...s)[n]}function a(t){if(e.hasLeftFocus.value=!1,null===t)return t;const n=t.toString();return 1===n.length?(e.modelValue.value=void 0,null):Number.parseInt(n.slice(0,-1))}function r({e:n,part:o,dateRef:a,prevValue:r,hourCycle:l}){const i=n.key===t.ARROW_UP?1:-1;if(null===r)return a[o];if("hour"===o&&"hour"in a){const e=[o,i,{hourCycle:l}];return a.set({[o]:r}).cycle(...e)[o]}const s=[o,i];return"day"===o&&null!==e.segmentValues.value.month?a.set({[o]:r,month:e.segmentValues.value.month}).cycle(...s)[o]:a.set({[o]:r}).cycle(...s)[o]}function l(t,n,o){let a=!1;const r=Math.floor(t/10);if(e.hasLeftFocus.value&&(e.hasLeftFocus.value=!1,o=null),null===o)return 0===n?(e.lastKeyZero.value=!0,{value:null,moveToNext:a}):((e.lastKeyZero.value||n>r)&&(a=!0),e.lastKeyZero.value=!1,{value:n,moveToNext:a});const l=o.toString().length,i=Number.parseInt(o.toString()+n.toString());return 2===l||i>t?((n>r||i>t)&&(a=!0),{value:n,moveToNext:a}):(a=!0,{value:i,moveToNext:a})}function i(t,n){let o=!1;const a=Math.floor(5.9);if(e.hasLeftFocus.value&&(e.hasLeftFocus.value=!1,n=null),null===n)return 0===t?(e.lastKeyZero.value=!0,{value:0,moveToNext:o}):((e.lastKeyZero.value||t>a)&&(o=!0),e.lastKeyZero.value=!1,{value:t,moveToNext:o});const r=n.toString().length,l=Number.parseInt(n.toString()+t.toString());return 2===r||l>59?(t>a&&(o=!0),{value:t,moveToNext:o}):(o=!0,{value:l,moveToNext:o})}function s(t,n){let o=!1;const a=Math.floor(2.4);if(e.hasLeftFocus.value&&(e.hasLeftFocus.value=!1,n=null),null===n)return 0===t?(e.lastKeyZero.value=!0,{value:0,moveToNext:o}):((e.lastKeyZero.value||t>a)&&(o=!0),e.lastKeyZero.value=!1,{value:t,moveToNext:o});const r=n.toString().length,l=Number.parseInt(n.toString()+t.toString());return 2===r||l>24?(t>a&&(o=!0),{value:t,moveToNext:o}):(o=!0,{value:l,moveToNext:o})}function u(t,n){let o=!1;if(e.hasLeftFocus.value&&(e.hasLeftFocus.value=!1,n=null),null===n)return{value:0===t?1:t,moveToNext:o};const a=n.toString()+t.toString();return a.length>4?{value:0===t?1:t,moveToNext:o}:(4===a.length&&(o=!0),{value:Number.parseInt(a),moveToNext:o})}const d=(0,o.EW)(()=>{var t;return(null==(t=fc[e.part])?void 0:t.attrs({disabled:e.disabled.value,placeholder:e.placeholder.value,hourCycle:e.hourCycle,segmentValues:e.segmentValues.value,formatter:e.formatter}))??{}});function c(n){if(!Zd(n.key)||Hd(n.key))return;const o=e.segmentValues.value.day;if(n.key!==t.ARROW_DOWN&&n.key!==t.ARROW_UP){if(Yd(n.key)){const t=Number.parseInt(n.key),a=e.segmentValues.value.month,r=sn(a?e.placeholder.value.set({month:a}):e.placeholder.value),{value:i,moveToNext:s}=l(r,t,o);e.segmentValues.value.day=i,s&&e.focusNext()}n.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.day=a(o))}else e.segmentValues.value.day=r({e:n,part:"day",dateRef:e.placeholder.value,prevValue:o})}function f(n){if(!Zd(n.key)||Hd(n.key))return;const o=e.segmentValues.value.month;if(n.key!==t.ARROW_DOWN&&n.key!==t.ARROW_UP){if(Yd(n.key)){const t=Number.parseInt(n.key),{value:a,moveToNext:r}=l(12,t,o);e.segmentValues.value.month=a,r&&e.focusNext()}n.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.month=a(o))}else e.segmentValues.value.month=r({e:n,part:"month",dateRef:e.placeholder.value,prevValue:o})}function p(n){if(!Zd(n.key)||Hd(n.key))return;const o=e.segmentValues.value.year;if(n.key!==t.ARROW_DOWN&&n.key!==t.ARROW_UP){if(Yd(n.key)){const t=Number.parseInt(n.key),{value:a,moveToNext:r}=u(t,o);e.segmentValues.value.year=a,r&&e.focusNext()}n.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.year=a(o))}else e.segmentValues.value.year=r({e:n,part:"year",dateRef:e.placeholder.value,prevValue:o})}function v(n){const o=e.placeholder.value;if(!Zd(n.key)||Hd(n.key)||!("hour"in o)||!("hour"in e.segmentValues.value))return;const l=e.segmentValues.value.hour,i=e.hourCycle;if(n.key===t.ARROW_UP||n.key===t.ARROW_DOWN)return e.segmentValues.value.hour=r({e:n,part:"hour",dateRef:e.placeholder.value,prevValue:l,hourCycle:i}),void("dayPeriod"in e.segmentValues.value&&(e.segmentValues.value.hour<12?e.segmentValues.value.dayPeriod="AM":e.segmentValues.value.hour&&(e.segmentValues.value.dayPeriod="PM")));if(Yd(n.key)){const t=Number.parseInt(n.key),{value:o,moveToNext:a}=s(t,l);"dayPeriod"in e.segmentValues.value&&o&&o>12?e.segmentValues.value.dayPeriod="PM":"dayPeriod"in e.segmentValues.value&&o&&(e.segmentValues.value.dayPeriod="AM"),e.segmentValues.value.hour=o,a&&e.focusNext()}n.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.hour=a(l))}function h(o){const r=e.placeholder.value;if(!Zd(o.key)||Hd(o.key)||!("minute"in r)||!("minute"in e.segmentValues.value))return;const l=e.segmentValues.value.minute;if(e.segmentValues.value.minute=n({e:o,part:"minute",dateRef:e.placeholder.value,prevValue:l}),Yd(o.key)){const t=Number.parseInt(o.key),{value:n,moveToNext:a}=i(t,l);e.segmentValues.value.minute=n,a&&e.focusNext()}o.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.minute=a(l))}function m(o){const r=e.placeholder.value;if(!Zd(o.key)||Hd(o.key)||!("second"in r)||!("second"in e.segmentValues.value))return;const l=e.segmentValues.value.second;if(e.segmentValues.value.second=n({e:o,part:"second",dateRef:e.placeholder.value,prevValue:l}),Yd(o.key)){const t=Number.parseInt(o.key),{value:n,moveToNext:a}=i(t,l);e.segmentValues.value.second=n,a&&e.focusNext()}o.key===t.BACKSPACE&&(e.hasLeftFocus.value=!1,e.segmentValues.value.second=a(l))}function y(n){if((Zd(n.key)&&!Hd(n.key)||"a"===n.key||"p"===n.key)&&"hour"in e.placeholder.value&&"dayPeriod"in e.segmentValues.value){if(n.key===t.ARROW_UP||n.key===t.ARROW_DOWN)return"AM"===e.segmentValues.value.dayPeriod?(e.segmentValues.value.dayPeriod="PM",void(e.segmentValues.value.hour=e.segmentValues.value.hour+12)):(e.segmentValues.value.dayPeriod="AM",void(e.segmentValues.value.hour=e.segmentValues.value.hour-12));if(["a","A"].includes(n.key)&&"AM"!==e.segmentValues.value.dayPeriod)return e.segmentValues.value.dayPeriod="AM",void(e.segmentValues.value.hour=e.segmentValues.value.hour-12);["p","P"].includes(n.key)&&"PM"!==e.segmentValues.value.dayPeriod&&(e.segmentValues.value.dayPeriod="PM",e.segmentValues.value.hour=e.segmentValues.value.hour+12)}}function g(t){e.disabled.value&&t.preventDefault()}function b(n){const o=e.disabled.value,a=e.readonly.value;if(n.key!==t.TAB&&n.preventDefault(),!o&&!a&&({day:c,month:f,year:p,hour:v,minute:h,second:m,dayPeriod:y,timeZoneName:()=>{}}[e.part](n),![t.ARROW_LEFT,t.ARROW_RIGHT].includes(n.key)&&n.key!==t.TAB&&n.key!==t.SHIFT&&Zd(n.key)&&Object.values(e.segmentValues.value).every(e=>null!==e))){const t={...e.segmentValues.value};let n=e.placeholder.value.copy();Object.keys(t).forEach(e=>{const o=t[e];n=n.set({[e]:o})}),e.modelValue.value=n.copy()}}return{handleSegmentClick:g,handleSegmentKeydown:b,attributes:d}}const vc=(0,o.pM)({__name:"DateFieldInput",props:{part:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Qd(),r=(0,a.KR)(!0),l=(0,a.KR)(!1),{handleSegmentClick:i,handleSegmentKeydown:s,attributes:u}=pc({hasLeftFocus:r,lastKeyZero:l,placeholder:n.placeholder,hourCycle:n.hourCycle,segmentValues:n.segmentValues,formatter:n.formatter,part:t.part,disabled:n.disabled,readonly:n.readonly,focusNext:n.focusNext,modelValue:n.modelValue}),d=(0,o.EW)(()=>n.disabled.value),c=(0,o.EW)(()=>n.readonly.value),f=(0,o.EW)(()=>n.isInvalid.value);return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({as:e.as,"as-child":e.asChild},(0,a.R1)(u),{contenteditable:!d.value&&!c.value&&"literal"!==e.part,"data-radix-vue-date-field-segment":e.part,"aria-disabled":!!d.value||void 0,"aria-readonly":!!c.value||void 0,"data-disabled":d.value?"":void 0,"data-invalid":f.value?"":void 0,"aria-invalid":!!f.value||void 0},(0,o.Tb)("literal"!==e.part?{mousedown:(0,a.R1)(i),keydown:(0,a.R1)(s),focusout:()=>{r.value=!0},focusin:e=>{(0,a.R1)(n).setFocusedElement(e.target)}}:{})),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child","contenteditable","data-radix-vue-date-field-segment","aria-disabled","aria-readonly","data-disabled","data-invalid","aria-invalid"]))}}),[hc,mc]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("DatePickerRoot")),[yc,gc]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("DateRangePickerRoot")),bc=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,["id","value","name","disabled","required"]),[wc,Rc]=Za("DateRangeFieldRoot"),xc=(Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"DateRangeFieldInput",props:{part:{},type:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=wc(),r=(0,a.KR)(!0),l=(0,a.KR)(!1),{handleSegmentClick:i,handleSegmentKeydown:s,attributes:u}=pc({hasLeftFocus:r,lastKeyZero:l,placeholder:n.placeholder,hourCycle:n.hourCycle,segmentValues:n.segmentValues[t.type],formatter:n.formatter,part:t.part,disabled:n.disabled,readonly:n.readonly,focusNext:n.focusNext,modelValue:"start"===t.type?n.startValue:n.endValue}),d=(0,o.EW)(()=>n.disabled.value),c=(0,o.EW)(()=>n.readonly.value),f=(0,o.EW)(()=>n.isInvalid.value);return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({as:e.as,"as-child":e.asChild},(0,a.R1)(u),{contenteditable:!d.value&&!c.value&&"literal"!==e.part,"data-radix-vue-date-field-segment":e.part,"aria-disabled":!!d.value||void 0,"aria-readonly":!!c.value||void 0,"data-disabled":d.value?"":void 0,"data-radix-vue-date-range-field-segment-type":e.type,"data-invalid":f.value?"":void 0,"aria-invalid":!!f.value||void 0},(0,o.Tb)("literal"!==e.part?{mousedown:(0,a.R1)(i),keydown:(0,a.R1)(s),focusout:()=>{r.value=!0},focusin:e=>{(0,a.R1)(n).setFocusedElement(e.target)}}:{})),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child","contenteditable","data-radix-vue-date-field-segment","aria-disabled","aria-readonly","data-disabled","data-radix-vue-date-range-field-segment-type","data-invalid","aria-invalid"]))}})),[Bc,_c]=Za("DropdownMenuRoot"),Ec=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,["value","name","disabled","required"]),[Cc,kc]=Za("EditableRoot"),[Mc,Sc]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("HoverCardRoot"));Boolean,Boolean;function Dc(e){return t=>"touch"===t.pointerType?void 0:e()}function Ac(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;n.nextNode();)t.push(n.currentNode);return t}Boolean,Boolean,Boolean;const Oc=(0,o.pM)({__name:"HoverCardContentImpl",props:{side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside"],setup(e,{emit:t}){const n=e,r=t,i=Kl(n),{forwardRef:s,currentElement:u}=Gl(),d=Mc(),{isPointerInTransit:c,onPointerExit:f}=Xl(d.triggerElement,u);qr(d.isPointerInTransitRef,c,{direction:"rtl"}),f(()=>{d.onClose()});const p=(0,a.KR)(!1);let v;function h(){p.value=!1,d.isPointerDownOnContentRef.value=!1,(0,o.dY)(()=>{var e;""!==(null==(e=document.getSelection())?void 0:e.toString())&&(d.hasSelectionRef.value=!0)})}return(0,o.nT)(e=>{if(p.value){const t=document.body;v=t.style.userSelect||t.style.webkitUserSelect,t.style.userSelect="none",t.style.webkitUserSelect="none",e(()=>{t.style.userSelect=v,t.style.webkitUserSelect=v})}}),(0,o.sV)(()=>{u.value&&(document.addEventListener("pointerup",h),Ac(u.value).forEach(e=>e.setAttribute("tabindex","-1")))}),(0,o.hi)(()=>{document.removeEventListener("pointerup",h),d.hasSelectionRef.value=!1,d.isPointerDownOnContentRef.value=!1}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Hi),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:t[1]||(t[1]=e=>r("escapeKeyDown",e)),onPointerDownOutside:t[2]||(t[2]=e=>r("pointerDownOutside",e)),onFocusOutside:t[3]||(t[3]=(0,l.D$)(e=>r("focusOutside",e),["prevent"])),onDismiss:(0,a.R1)(d).onDismiss},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Bu),(0,o.v6)({...(0,a.R1)(i),...e.$attrs},{ref:(0,a.R1)(s),"data-state":(0,a.R1)(d).open.value?"open":"closed",style:{userSelect:p.value?"text":void 0,WebkitUserSelect:p.value?"text":void 0,"--radix-hover-card-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-hover-card-content-available-width":"var(--radix-popper-available-width)","--radix-hover-card-content-available-height":"var(--radix-popper-available-height)","--radix-hover-card-trigger-width":"var(--radix-popper-anchor-width)","--radix-hover-card-trigger-height":"var(--radix-popper-anchor-height)"},onPointerdown:t[0]||(t[0]=e=>{e.currentTarget.contains(e.target)&&(p.value=!0),(0,a.R1)(d).hasSelectionRef.value=!1,(0,a.R1)(d).isPointerDownOnContentRef.value=!0})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["data-state","style"])]),_:3},8,["onDismiss"]))}});Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;function Tc(e,t,n){return void 0!==e&&(Array.isArray(e)?e.some(e=>Pc(e,t,n)):Pc(e,t,n))}function Pc(e,t,n){return void 0!==e&&void 0!==t&&("string"==typeof e?e===t:"function"==typeof n?n(e,t):"string"==typeof n?(null==e?void 0:e[n])===(null==t?void 0:t[n]):or(e,t))}const[Ic,Wc]=Za("ListboxRoot"),Fc=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,"listbox.select"),[$c,Nc]=Za("ListboxItem");Boolean,Boolean,Boolean;typeof window>"u"||window;const[Vc,Lc]=Za("ListboxGroup"),[Kc,jc]=(Boolean,Boolean,Za("MenubarRoot")),[Gc,Xc]=(Boolean,Za("MenubarMenu")),[Uc,zc]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za(["NavigationMenuRoot","NavigationMenuSub"],"NavigationMenuContext"));Boolean,Boolean,Boolean;function Hc(e){return e?"open":"closed"}function Yc(e,t){return`${e}-trigger-${t}`}function Zc(e,t){return`${e}-content-${t}`}const qc="navigationMenu.linkSelect",Jc="navigationMenu.rootContentDismiss";function Qc(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ef(e){const t=yl();return e.some(e=>e===t||(e.focus(),yl()!==t))}function tf(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{const t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}function nf(e){return t=>"mouse"===t.pointerType?e(t):void 0}const[of,af]=Za("NavigationMenuItem"),rf=(Boolean,(0,o.pM)({__name:"NavigationMenuContentImpl",props:{disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside"],setup(e,{emit:t}){const n=e,r=t,{injectCollection:l}=Pl("nav"),i=l(),{forwardRef:s,currentElement:u}=Gl(),d=Uc(),c=of(),f=Yc(d.baseId,c.value),p=Zc(d.baseId,c.value),v=(0,a.KR)(null),h=(0,o.EW)(()=>{const e=i.value.map(e=>e.id.split("trigger-")[1]);"rtl"===d.dir.value&&e.reverse();const t=e.indexOf(d.modelValue.value),n=e.indexOf(d.previousValue.value),o=c.value===d.modelValue.value,a=n===e.indexOf(c.value);if(!o&&!a)return v.value;const r=(()=>{if(t!==n){if(o&&-1!==n)return t>n?"from-end":"from-start";if(a&&-1!==t)return t>n?"to-start":"to-end"}return null})();return v.value=r,r});function m(e){var t,n;if(r("focusOutside",e),r("interactOutside",e),!e.defaultPrevented){c.onContentFocusOutside();const o=e.target;null!=(n=null==(t=d.rootNavigationMenu)?void 0:t.value)&&n.contains(o)&&e.preventDefault()}}function y(e){var t;if(r("pointerDownOutside",e),!e.defaultPrevented){const n=e.target,o=i.value.some(e=>e.contains(n)),a=d.isRootMenu&&(null==(t=d.viewport.value)?void 0:t.contains(n));(o||a||!d.isRootMenu)&&e.preventDefault()}}function g(e){var t,n;r("escapeKeyDown",e),e.defaultPrevented||(d.onItemDismiss(),null==(n=null==(t=c.triggerRef)?void 0:t.value)||n.focus(),c.wasEscapeCloseRef.value=!0)}function b(e){var t;if(e.target.closest("[data-radix-navigation-menu]")!==d.rootNavigationMenu.value)return;const n=e.altKey||e.ctrlKey||e.metaKey,o="Tab"===e.key&&!n,a=Qc(e.currentTarget);if(o){const n=yl(),o=a.findIndex(e=>e===n),r=e.shiftKey?a.slice(0,o).reverse():a.slice(o+1,a.length);if(!ef(r))return void(null==(t=c.focusProxyRef.value)||t.focus());e.preventDefault()}const r=bl(e,yl(),void 0,{itemsArray:a,loop:!1,enableIgnoredElement:!0});null==r||r.focus()}function w(){var e;const t=new Event(Jc,{bubbles:!0,cancelable:!0});null==(e=u.value)||e.dispatchEvent(t)}return(0,o.nT)(e=>{const t=u.value;if(d.isRootMenu&&t){const n=()=>{var e;d.onItemDismiss(),c.onRootContentClose(),t.contains(yl())&&(null==(e=c.triggerRef.value)||e.focus())};t.addEventListener(Jc,n),e(()=>t.removeEventListener(Jc,n))}}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Hi),(0,o.v6)({id:(0,a.R1)(p),ref:(0,a.R1)(s),"aria-labelledby":(0,a.R1)(f),"data-motion":h.value,"data-state":(0,a.R1)(Hc)((0,a.R1)(d).modelValue.value===(0,a.R1)(c).value),"data-orientation":(0,a.R1)(d).orientation},n,{onKeydown:b,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:m,onDismiss:w}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","aria-labelledby","data-motion","data-state","data-orientation"]))}})),lf=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,["aria-owns"]);Boolean,Boolean,Boolean,Boolean;function sf(e){const{disabled:t}=e,n=(0,a.KR)(),r=Mr(),l=()=>window.clearTimeout(n.value),i=e=>{l(),!t.value&&(r.trigger(),n.value=window.setTimeout(()=>{i(60)},e))},s=()=>{i(400)},u=()=>{l()},d=(0,a.KR)(!1),c=(0,o.EW)(()=>nl(e.target)),f=e=>{0!==e.button||d.value||(e.preventDefault(),d.value=!0,s())},p=()=>{d.value=!1,u()};return Pr&&(al(c||window,"pointerdown",f),al(window,"pointerup",p),al(window,"pointercancel",p)),{isPressed:d,onTrigger:r.on}}function uf(e,t=(0,a.KR)({})){return Tr(()=>new Ia(e.value,t.value))}function df(e,t=(0,a.KR)({})){return Tr(()=>new Va(e.value,t.value))}function cf(e,t,n){let o="+"===e?t+n:t-n;if(t%1!==0||n%1!==0){const a=t.toString().split("."),r=n.toString().split("."),l=a[1]&&a[1].length||0,i=r[1]&&r[1].length||0,s=10**Math.max(l,i);t=Math.round(t*s),n=Math.round(n*s),o="+"===e?t+n:t-n,o/=s}return o}const ff=["value","name","disabled","required"],[pf,vf]=Za("NumberFieldRoot"),[hf,mf]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("PaginationRoot"));Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;function yf(e,t){const n=t-e+1;return Array.from({length:n},(t,n)=>n+e)}function gf(e){return e.map(e=>"number"==typeof e?{type:"page",value:e}:{type:"ellipsis"})}const bf="ellipsis";function wf(e,t,n,o){const a=t,r=Math.max(e-n,1),l=Math.min(e+n,a);if(o){const e=Math.min(2*n+5,t)-2,o=r>3&&Math.abs(a-e-1+1)>2&&Math.abs(r-1)>2,i=l<a-2&&Math.abs(a-e)>2&&Math.abs(a-l)>2;if(!o&&i)return[...yf(1,e),bf,a];if(o&&!i){const t=yf(a-e+1,a);return[1,bf,...t]}if(o&&i){const e=yf(r,l);return[1,bf,...e,bf,a]}return yf(1,a)}{const o=2*n+1;return t<o?yf(1,a):e<=n+1?yf(1,o):t-e<=n?yf(t-o+1,a):yf(r,l)}}Boolean,Boolean,Boolean,Boolean;const Rf=["id","value","name","disabled","required"],[xf,Bf]=Za("PinInputRoot"),[_f,Ef]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("PopoverRoot")),Cf=(0,o.pM)({__name:"PopoverRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,{modal:l}=(0,a.QW)(n),i=hl(n,"open",r,{defaultValue:n.defaultOpen,passive:void 0===n.open}),s=(0,a.KR)(),u=(0,a.KR)(!1);return Ef({contentId:"",modal:l,open:i,onOpenChange:e=>{i.value=e},onOpenToggle:()=>{i.value=!i.value},triggerElement:s,hasCustomAnchor:u}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(hu),null,{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{open:(0,a.R1)(i)})]),_:3}))}}),kf=(0,o.pM)({__name:"PopoverTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=_f(),{forwardRef:r,currentElement:l}=Gl();return(0,o.sV)(()=>{n.triggerElement.value=l.value}),(e,l)=>((0,o.uX)(),(0,o.Wv)((0,o.$y)((0,a.R1)(n).hasCustomAnchor.value?(0,a.R1)(bi):(0,a.R1)(mu)),{"as-child":""},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),{ref:(0,a.R1)(r),type:"button"===e.as?"button":void 0,"aria-haspopup":"dialog","aria-expanded":(0,a.R1)(n).open.value,"aria-controls":(0,a.R1)(n).contentId,"data-state":(0,a.R1)(n).open.value?"open":"closed",as:e.as,"as-child":t.asChild,onClick:(0,a.R1)(n).onOpenToggle},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["type","aria-expanded","aria-controls","data-state","as","as-child","onClick"])]),_:3}))}}),Mf=(0,o.pM)({__name:"PopoverPortal",props:{to:{},disabled:{type:Boolean},forceMount:{type:Boolean}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Li),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Sf=(0,o.pM)({__name:"PopoverContentImpl",props:{trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=Kl(n),{forwardRef:i}=Gl(),s=_f();return Nl(),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ds),{"as-child":"",loop:"",trapped:e.trapFocus,onMountAutoFocus:t[5]||(t[5]=e=>r("openAutoFocus",e)),onUnmountAutoFocus:t[6]||(t[6]=e=>r("closeAutoFocus",e))},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Hi),{"as-child":"","disable-outside-pointer-events":e.disableOutsidePointerEvents,onPointerDownOutside:t[0]||(t[0]=e=>r("pointerDownOutside",e)),onInteractOutside:t[1]||(t[1]=e=>r("interactOutside",e)),onEscapeKeyDown:t[2]||(t[2]=e=>r("escapeKeyDown",e)),onFocusOutside:t[3]||(t[3]=e=>r("focusOutside",e)),onDismiss:t[4]||(t[4]=e=>(0,a.R1)(s).onOpenChange(!1))},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Bu),(0,o.v6)((0,a.R1)(l),{id:(0,a.R1)(s).contentId,ref:(0,a.R1)(i),"data-state":(0,a.R1)(s).open.value?"open":"closed",role:"dialog",style:{"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","data-state"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Df=(0,o.pM)({__name:"PopoverContentModal",props:{trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,i=_f(),s=(0,a.KR)(!1);Dl(!0);const u=jl(n,r),{forwardRef:d,currentElement:c}=Gl();return ii(c),(e,t)=>((0,o.uX)(),(0,o.Wv)(Sf,(0,o.v6)((0,a.R1)(u),{ref:(0,a.R1)(d),"trap-focus":(0,a.R1)(i).open.value,"disable-outside-pointer-events":"",onCloseAutoFocus:t[0]||(t[0]=(0,l.D$)(e=>{var t;r("closeAutoFocus",e),s.value||null==(t=(0,a.R1)(i).triggerElement.value)||t.focus()},["prevent"])),onPointerDownOutside:t[1]||(t[1]=e=>{r("pointerDownOutside",e);const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,o=2===t.button||n;s.value=o}),onFocusOutside:t[2]||(t[2]=(0,l.D$)(()=>{},["prevent"]))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["trap-focus"]))}}),Af=(0,o.pM)({__name:"PopoverContentNonModal",props:{trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=_f(),i=(0,a.KR)(!1),s=(0,a.KR)(!1),u=jl(n,r);return(e,t)=>((0,o.uX)(),(0,o.Wv)(Sf,(0,o.v6)((0,a.R1)(u),{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:t[0]||(t[0]=e=>{var t;r("closeAutoFocus",e),e.defaultPrevented||(i.value||null==(t=(0,a.R1)(l).triggerElement.value)||t.focus(),e.preventDefault()),i.value=!1,s.value=!1}),onInteractOutside:t[1]||(t[1]=async e=>{var t;r("interactOutside",e),e.defaultPrevented||(i.value=!0,"pointerdown"===e.detail.originalEvent.type&&(s.value=!0));const n=e.target;(null==(t=(0,a.R1)(l).triggerElement.value)?void 0:t.contains(n))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&s.value&&e.preventDefault()})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Of=(0,o.pM)({__name:"PopoverContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,l=_f(),i=jl(n,r),{forwardRef:s}=Gl();return l.contentId||(l.contentId=ui(void 0,"radix-vue-popover-content")),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||(0,a.R1)(l).open.value},{default:(0,o.k6)(()=>[(0,a.R1)(l).modal.value?((0,o.uX)(),(0,o.Wv)(Df,(0,o.v6)({key:0},(0,a.R1)(i),{ref:(0,a.R1)(s)}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)):((0,o.uX)(),(0,o.Wv)(Af,(0,o.v6)({key:1},(0,a.R1)(i),{ref:(0,a.R1)(s)}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Tf=(0,o.pM)({__name:"PopoverArrow",props:{width:{default:10},height:{default:5},asChild:{type:Boolean},as:{default:"svg"}},setup(e){const t=e;return Gl(),(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ku),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Pf=(0,o.pM)({__name:"PopoverClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;Gl();const n=_f();return(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{type:"button"===e.as?"button":void 0,as:e.as,"as-child":t.asChild,onClick:r[0]||(r[0]=e=>(0,a.R1)(n).onOpenChange(!1))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["type","as","as-child"]))}}),If=(0,o.pM)({__name:"PopoverAnchor",props:{element:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;Gl();const n=_f();return(0,o.KC)(()=>{n.hasCustomAnchor.value=!0}),(0,o.hi)(()=>{n.hasCustomAnchor.value=!1}),(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(mu),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Wf=100,[Ff,$f]=Za("ProgressRoot"),Nf=e=>"number"==typeof e;function Vf(e,t){return ir(e)||Nf(e)&&!Number.isNaN(e)&&e<=t&&e>=0?e:(console.error(`Invalid prop \`value\` of value \`${e}\` supplied to \`ProgressRoot\`. The \`value\` prop must be:\n  - a positive number\n  - less than the value passed to \`max\` (or ${Wf} if no \`max\` prop is set)\n  - \`null\`  or \`undefined\` if the progress is indeterminate.\n\nDefaulting to \`null\`.`),null)}function Lf(e){return Nf(e)&&!Number.isNaN(e)&&e>0?e:(console.error(`Invalid prop \`max\` of value \`${e}\` supplied to \`ProgressRoot\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Wf}\`.`),Wf)}Boolean,Boolean;const[Kf,jf]=Za("RadioGroupRoot"),Gf=(Boolean,Boolean,Boolean,Boolean,["value","checked","name","disabled","required"]),Xf=(0,o.pM)({__name:"Radio",props:{id:{},value:{},disabled:{type:Boolean,default:!1},required:{type:Boolean},checked:{type:Boolean,default:void 0},name:{},asChild:{type:Boolean},as:{default:"button"}},emits:["update:checked"],setup(e,{emit:t}){const n=e,r=hl(n,"checked",t,{passive:void 0===n.checked}),{value:i}=(0,a.QW)(n),{forwardRef:s,currentElement:u}=Gl(),d=Ll(u),c=(0,o.EW)(()=>{var e;return n.id&&u.value?(null==(e=document.querySelector(`[for="${n.id}"]`))?void 0:e.innerText)??n.value:void 0});function f(e){r.value=!0,d.value&&e.stopPropagation()}return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(e.$attrs,{id:e.id,ref:(0,a.R1)(s),role:"radio",type:"button"===e.as?"button":void 0,as:e.as,"aria-checked":(0,a.R1)(r),"aria-label":c.value,"as-child":e.asChild,disabled:e.disabled?"":void 0,"data-state":(0,a.R1)(r)?"checked":"unchecked","data-disabled":e.disabled?"":void 0,value:(0,a.R1)(i),required:e.required,name:e.name,onClick:(0,l.D$)(f,["stop"])}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{checked:(0,a.R1)(r)}),(0,a.R1)(d)?((0,o.uX)(),(0,o.CE)("input",{key:0,type:"radio",tabindex:"-1","aria-hidden":"true",value:(0,a.R1)(i),checked:!!(0,a.R1)(r),name:e.name,disabled:e.disabled,required:e.required,style:{transform:"translateX(-100%)",position:"absolute",pointerEvents:"none",opacity:0,margin:0}},null,8,Gf)):(0,o.Q3)("",!0)]),_:3},16,["id","type","as","aria-checked","aria-label","as-child","disabled","data-state","data-disabled","value","required","name"]))}}),[Uf,zf]=Za("RadioGroupItem");Boolean,Boolean,Boolean,Boolean,Boolean;function Hf(e){const t=(0,o.EW)(()=>!!e.start.value&&!!e.isDateDisabled(e.start.value)),n=(0,o.EW)(()=>!!e.end.value&&!!e.isDateDisabled(e.end.value)),a=(0,o.EW)(()=>!t.value&&!n.value&&!!(e.start.value&&e.end.value&&un(e.end.value,e.start.value))),r=t=>!!e.start.value&&E(e.start.value,t),l=t=>!!e.end.value&&E(e.end.value,t),i=t=>!!(e.start.value&&E(e.start.value,t)||e.end.value&&E(e.end.value,t))||!(!e.end.value||!e.start.value)&&vn(t,e.start.value,e.end.value),s=(0,o.EW)(()=>{if(e.start.value&&e.end.value||!e.start.value||!e.focusedValue.value)return null;const t=un(e.start.value,e.focusedValue.value),n=t?e.start.value:e.focusedValue.value,o=t?e.focusedValue.value:e.start.value;return E(n,o)||yn(n,o,e.isDateUnavailable,e.isDateDisabled)?{start:n,end:o}:null});return{isInvalid:a,isSelected:i,highlightedRange:s,isSelectionStart:r,isSelectionEnd:l,isHighlightedStart:e=>!(!s.value||!s.value.start)&&E(s.value.start,e),isHighlightedEnd:e=>!(!s.value||!s.value.end)&&E(s.value.end,e)}}const Yf={style:{border:"0px",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0px",position:"absolute","white-space":"nowrap",width:"1px"}},Zf={role:"heading","aria-level":"2"},[qf,Jf]=Za("RangeCalendarRoot"),Qf=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"RangeCalendarHeader",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}})),ep=(0,o.pM)({__name:"RangeCalendarHeading",props:{asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e,n=qf();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"data-disabled":(0,a.R1)(n).disabled.value?"":void 0}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{headingValue:(0,a.R1)(n).headingValue.value},()=>[(0,o.eW)((0,r.v_)((0,a.R1)(n).headingValue.value),1)])]),_:3},16,["data-disabled"]))}}),tp=(0,o.pM)({__name:"RangeCalendarGrid",props:{asChild:{type:Boolean},as:{default:"table"}},setup(e){const t=e,n=qf(),r=(0,o.EW)(()=>!!n.disabled.value||void 0),l=(0,o.EW)(()=>!!n.readonly.value||void 0);return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{tabindex:"-1",role:"grid","aria-readonly":l.value,"aria-disabled":r.value,"data-readonly":l.value&&"","data-disabled":r.value&&""}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["aria-readonly","aria-disabled","data-readonly","data-disabled"]))}}),np=(0,o.pM)({__name:"RangeCalendarCell",props:{date:{},asChild:{type:Boolean},as:{default:"td"}},setup(e){const t=qf();return(e,n)=>{var r,l;return(0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,role:"gridcell","aria-selected":!!(0,a.R1)(t).isSelected(e.date)||void 0,"aria-disabled":(0,a.R1)(t).isDateDisabled(e.date)||(null==(l=(r=(0,a.R1)(t)).isDateUnavailable)?void 0:l.call(r,e.date)),"data-disabled":(0,a.R1)(t).isDateDisabled(e.date)?"":void 0},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","aria-selected","aria-disabled","data-disabled"])}}}),op=(0,o.pM)({__name:"RangeCalendarHeadCell",props:{asChild:{type:Boolean},as:{default:"th"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),ap=(0,o.pM)({__name:"RangeCalendarNext",props:{step:{},nextPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=(0,o.EW)(()=>r.disabled.value||r.isNextButtonDisabled(t.step,t.nextPage)),r=qf();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"aria-label":"Next page",type:"button"===e.as?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:l[0]||(l[0]=e=>(0,a.R1)(r).nextPage(t.step,t.nextPage))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{},()=>[(0,o.eW)("Next page")])]),_:3},16,["type","aria-disabled","data-disabled","disabled"]))}}),rp=(0,o.pM)({__name:"RangeCalendarPrev",props:{step:{},prevPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=(0,o.EW)(()=>r.disabled.value||r.isPrevButtonDisabled(t.step,t.prevPage)),r=qf();return(e,l)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"aria-label":"Previous page",type:"button"===e.as?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:l[0]||(l[0]=e=>(0,a.R1)(r).prevPage(t.step,t.prevPage))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{},()=>[(0,o.eW)("Prev page")])]),_:3},16,["type","aria-disabled","data-disabled","disabled"]))}}),lp=(0,o.pM)({__name:"RangeCalendarGridHead",props:{asChild:{type:Boolean},as:{default:"thead"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)(t,{"aria-hidden":"true"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),ip=(0,o.pM)({__name:"RangeCalendarGridBody",props:{asChild:{type:Boolean},as:{default:"tbody"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),sp=(0,o.pM)({__name:"RangeCalendarGridRow",props:{asChild:{type:Boolean},as:{default:"tr"}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),up=(0,o.pM)({__name:"RangeCalendarCellTrigger",props:{day:{},month:{},asChild:{type:Boolean},as:{default:"div"}},setup(e){const t=e,n=qf(),i=yi(),{primitiveElement:s,currentElement:u}=wi(),d=(0,o.EW)(()=>n.formatter.custom(on(t.day),{weekday:"long",month:"long",day:"numeric",year:"numeric"})),c=(0,o.EW)(()=>n.isDateDisabled(t.day)),f=(0,o.EW)(()=>{var e;return null==(e=n.isDateUnavailable)?void 0:e.call(n,t.day)}),p=(0,o.EW)(()=>n.isSelected(t.day)),v=(0,o.EW)(()=>n.isSelectionStart(t.day)),h=(0,o.EW)(()=>n.isSelectionEnd(t.day)),m=(0,o.EW)(()=>n.isHighlightedStart(t.day)),y=(0,o.EW)(()=>n.isHighlightedEnd(t.day)),g=(0,o.EW)(()=>!!n.highlightedRange.value&&pn(t.day,n.highlightedRange.value.start,n.highlightedRange.value.end)),b="[data-radix-vue-calendar-cell-trigger]:not([data-disabled]):not([data-outside-view]):not([data-outside-visible-view])",w=(0,o.EW)(()=>D(t.day,N())),R=(0,o.EW)(()=>!C(t.day,t.month)),x=(0,o.EW)(()=>n.isOutsideVisibleView(t.day)),B=(0,o.EW)(()=>t.day.day.toLocaleString(n.locale.value)),_=(0,o.EW)(()=>!n.disabled.value&&E(t.day,n.placeholder.value));function k(e,t){var o;if(!n.readonly.value&&!(n.isDateDisabled(t)||null!=(o=n.isDateUnavailable)&&o.call(n,t))){if(n.lastPressedDateValue.value=t.copy(),n.startValue.value&&null===n.highlightedRange.value){if(E(t,n.startValue.value)&&!n.preventDeselect.value&&!n.endValue.value)return n.startValue.value=void 0,void n.onPlaceholderChange(t);if(!n.endValue.value)return e.preventDefault(),void(n.lastPressedDateValue.value&&E(n.lastPressedDateValue.value,t)&&(n.startValue.value=t.copy()))}if(n.startValue.value&&n.endValue.value&&E(n.endValue.value,t)&&!n.preventDeselect.value)return n.startValue.value=void 0,n.endValue.value=void 0,void n.onPlaceholderChange(t);n.startValue.value?n.endValue.value?n.endValue.value&&n.startValue.value&&(n.endValue.value=void 0,n.startValue.value=t.copy()):n.endValue.value=t.copy():n.startValue.value=t.copy()}}function M(e){k(e,t.day)}function S(){var e;n.isDateDisabled(t.day)||null!=(e=n.isDateUnavailable)&&e.call(n,t.day)||(n.focusedValue.value=t.day.copy())}function A(e){e.preventDefault(),e.stopPropagation();const a=n.parentElement.value,r=a?Array.from(a.querySelectorAll(b)):[];let l=r.indexOf(u.value);const s=7,d="rtl"===n.dir.value?-1:1;switch(e.code){case i.ARROW_RIGHT:l+=d;break;case i.ARROW_LEFT:l-=d;break;case i.ARROW_UP:l-=s;break;case i.ARROW_DOWN:l+=s;break;case i.ENTER:case i.SPACE_CODE:return void k(e,t.day);default:return}if(l>=0&&l<r.length)r[l].focus();else{if(l<0){if(n.isPrevButtonDisabled("month"))return;return n.prevPage(),void(0,o.dY)(()=>{const e=a?Array.from(a.querySelectorAll(b)):[];if(!n.pagedNavigation.value){const t=sn(n.placeholder.value);return void e[t-Math.abs(l)].focus()}e[e.length-Math.abs(l)].focus()})}if(l>=r.length){if(n.isNextButtonDisabled("month"))return;n.nextPage(),(0,o.dY)(()=>{const e=a?Array.from(a.querySelectorAll(b)):[];if(!n.pagedNavigation.value){const t=sn(n.placeholder.value.add({months:n.numberOfMonths.value-1}));return void e[e.length-t+l-r.length].focus()}e[l-r.length].focus()})}}}return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({ref_key:"primitiveElement",ref:s},t,{role:"button","aria-label":d.value,"data-radix-vue-calendar-cell-trigger":"","aria-selected":!!p.value||void 0,"aria-disabled":!(!c.value&&!f.value)||void 0,"data-highlighted":g.value?"":void 0,"data-selection-start":!!v.value||void 0,"data-selection-end":!!h.value||void 0,"data-highlighted-start":!!m.value||void 0,"data-highlighted-end":!!y.value||void 0,"data-selected":!!p.value||void 0,"data-outside-visible-view":x.value?"":void 0,"data-value":e.day.toString(),"data-disabled":c.value?"":void 0,"data-unavailable":f.value?"":void 0,"data-today":w.value?"":void 0,"data-outside-view":R.value?"":void 0,"data-focused":_.value?"":void 0,tabindex:_.value?0:R.value||c.value?void 0:-1,onClick:M,onFocusin:S,onMouseenter:S,onKeydown:(0,l.jR)(A,["up","down","left","right","enter","space"])}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{dayValue:B.value},()=>[(0,o.eW)((0,r.v_)(B.value),1)])]),_:3},16,["aria-label","aria-selected","aria-disabled","data-highlighted","data-selection-start","data-selection-end","data-highlighted-start","data-highlighted-end","data-selected","data-outside-visible-view","data-value","data-disabled","data-unavailable","data-today","data-outside-view","data-focused","tabindex"]))}}),[dp,cp]=Za("ScrollAreaRoot");Boolean,Boolean;function fp(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(n-e[0])}}function pp(e){const t=vp(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,o=(e.scrollbar.size-n)*t;return Math.max(o,18)}function vp(e,t){const n=e/t;return Number.isNaN(n)?0:n}function hp(e,t=()=>{}){let n={left:e.scrollLeft,top:e.scrollTop},o=0;return function a(){const r={left:e.scrollLeft,top:e.scrollTop},l=n.left!==r.left,i=n.top!==r.top;(l||i)&&t(),n=r,o=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(o)}function mp(e,t,n="ltr"){const o=pp(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,r=t.scrollbar.size-a,l=t.content-t.viewport,i=r-o,s="ltr"===n?[0,l]:[-1*l,0],u=Ja(e,s[0],s[1]);return fp([0,l],[0,i])(u)}function yp(e){return e?Number.parseInt(e,10):0}function gp(e,t,n,o="ltr"){const a=pp(n),r=a/2,l=t||r,i=a-l,s=n.scrollbar.paddingStart+l,u=n.scrollbar.size-n.scrollbar.paddingEnd-i,d=n.content-n.viewport,c="ltr"===o?[0,d]:[-1*d,0];return fp([s,u],c)(e)}function bp(e,t){return e>0&&e<t}const wp=(0,o.pM)({__name:"ScrollAreaScrollbarImpl",props:{isHorizontal:{type:Boolean}},emits:["onDragScroll","onWheelScroll","onThumbPointerDown"],setup(e,{emit:t}){const n=e,r=t,l=dp(),i=Bp(),s=Sp(),{forwardRef:u,currentElement:d}=Gl(),c=(0,a.KR)(""),f=(0,a.KR)();function p(e){var t,n;if(f.value){const o=e.clientX-(null==(t=f.value)?void 0:t.left),a=e.clientY-(null==(n=f.value)?void 0:n.top);r("onDragScroll",{x:o,y:a})}}function v(e){0===e.button&&(e.target.setPointerCapture(e.pointerId),f.value=d.value.getBoundingClientRect(),c.value=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",l.viewport&&(l.viewport.value.style.scrollBehavior="auto"),p(e))}function h(e){p(e)}function m(e){const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=c.value,l.viewport&&(l.viewport.value.style.scrollBehavior=""),f.value=void 0}function y(e){var t;const n=e.target,o=null==(t=d.value)?void 0:t.contains(n),a=i.sizes.value.content-i.sizes.value.viewport;o&&i.handleWheelScroll(e,a)}function g(){var e,t,o,a,r;d.value&&(n.isHorizontal?i.handleSizeChange({content:(null==(e=l.viewport.value)?void 0:e.scrollWidth)??0,viewport:(null==(t=l.viewport.value)?void 0:t.offsetWidth)??0,scrollbar:{size:d.value.clientWidth??0,paddingStart:yp(getComputedStyle(d.value).paddingLeft),paddingEnd:yp(getComputedStyle(d.value).paddingRight)}}):i.handleSizeChange({content:(null==(o=l.viewport.value)?void 0:o.scrollHeight)??0,viewport:(null==(a=l.viewport.value)?void 0:a.offsetHeight)??0,scrollbar:{size:(null==(r=d.value)?void 0:r.clientHeight)??0,paddingStart:yp(getComputedStyle(d.value).paddingLeft),paddingEnd:yp(getComputedStyle(d.value).paddingRight)}}))}return(0,o.sV)(()=>{document.addEventListener("wheel",y,{passive:!1})}),(0,o.hi)(()=>{document.removeEventListener("wheel",y)}),pl(d,g),pl(l.content,g),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{ref:(0,a.R1)(u),style:{position:"absolute"},"data-scrollbarimpl":"",as:(0,a.R1)(s).as.value,"as-child":(0,a.R1)(s).asChild.value,onPointerdown:v,onPointermove:h,onPointerup:m},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child"]))}}),Rp=(0,o.pM)({__name:"ScrollAreaScrollbarX",setup(e){const t=dp(),n=Bp(),{forwardRef:l,currentElement:i}=Gl();(0,o.sV)(()=>{i.value&&t.onScrollbarXChange(i.value)});const s=(0,o.EW)(()=>n.sizes.value);return(e,i)=>((0,o.uX)(),(0,o.Wv)(wp,{ref:(0,a.R1)(l),"is-horizontal":!0,"data-orientation":"horizontal",style:(0,r.Tr)({bottom:0,left:"rtl"===(0,a.R1)(t).dir.value?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===(0,a.R1)(t).dir.value?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":s.value?`${(0,a.R1)(pp)(s.value)}px`:void 0}),onOnDragScroll:i[0]||(i[0]=e=>(0,a.R1)(n).onDragScroll(e.x))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["style"]))}}),xp=(0,o.pM)({__name:"ScrollAreaScrollbarY",setup(e){const t=dp(),n=Bp(),{forwardRef:l,currentElement:i}=Gl();(0,o.sV)(()=>{i.value&&t.onScrollbarYChange(i.value)});const s=(0,o.EW)(()=>n.sizes.value);return(e,i)=>((0,o.uX)(),(0,o.Wv)(wp,{ref:(0,a.R1)(l),"is-horizontal":!1,"data-orientation":"vertical",style:(0,r.Tr)({top:0,right:"ltr"===(0,a.R1)(t).dir.value?0:void 0,left:"rtl"===(0,a.R1)(t).dir.value?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":s.value?`${(0,a.R1)(pp)(s.value)}px`:void 0}),onOnDragScroll:i[0]||(i[0]=e=>(0,a.R1)(n).onDragScroll(e.y))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["style"]))}}),[Bp,_p]=Za("ScrollAreaScrollbarVisible"),Ep=(0,o.pM)({__name:"ScrollAreaScrollbarVisible",setup(e){const t=dp(),n=Sp(),{forwardRef:r}=Gl(),l=(0,a.KR)({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),i=(0,o.EW)(()=>{const e=vp(l.value.viewport,l.value.content);return e>0&&e<1}),s=(0,a.KR)(),u=(0,a.KR)(0);function d(e,n){if(h.value){const o=t.viewport.value.scrollLeft+e.deltaY;t.viewport.value.scrollLeft=o,bp(o,n)&&e.preventDefault()}else{const o=t.viewport.value.scrollTop+e.deltaY;t.viewport.value.scrollTop=o,bp(o,n)&&e.preventDefault()}}function c(e,t){h.value?u.value=t.x:u.value=t.y}function f(e){u.value=0}function p(e){l.value=e}function v(e,t){return gp(e,u.value,l.value,t)}const h=(0,o.EW)(()=>n.isHorizontal.value);function m(e){h.value?t.viewport.value.scrollLeft=v(e,t.dir.value):t.viewport.value.scrollTop=v(e)}function y(){if(h.value){if(t.viewport.value&&s.value){const e=t.viewport.value.scrollLeft,n=mp(e,l.value,t.dir.value);s.value.style.transform=`translate3d(${n}px, 0, 0)`}}else if(t.viewport.value&&s.value){const e=t.viewport.value.scrollTop,n=mp(e,l.value);s.value.style.transform=`translate3d(0, ${n}px, 0)`}}function g(e){s.value=e}return _p({sizes:l,hasThumb:i,handleWheelScroll:d,handleThumbDown:c,handleThumbUp:f,handleSizeChange:p,onThumbPositionChange:y,onThumbChange:g,onDragScroll:m}),(e,t)=>h.value?((0,o.uX)(),(0,o.Wv)(Rp,(0,o.v6)({key:0},e.$attrs,{ref:(0,a.R1)(r)}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)):((0,o.uX)(),(0,o.Wv)(xp,(0,o.v6)({key:1},e.$attrs,{ref:(0,a.R1)(r)}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),Cp=(0,o.pM)({__name:"ScrollAreaScrollbarAuto",props:{forceMount:{type:Boolean}},setup(e){const t=dp(),n=Sp(),{forwardRef:r}=Gl(),l=(0,a.KR)(!1),i=Hr(()=>{if(t.viewport.value){const e=t.viewport.value.offsetWidth<t.viewport.value.scrollWidth,o=t.viewport.value.offsetHeight<t.viewport.value.scrollHeight;l.value=n.isHorizontal.value?e:o}},10);return(0,o.sV)(()=>i()),pl(t.viewport,i),pl(t.content,i),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||l.value},{default:(0,o.k6)(()=>[(0,o.bF)(Ep,(0,o.v6)(e.$attrs,{ref:(0,a.R1)(r),"data-state":l.value?"visible":"hidden"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["data-state"])]),_:3},8,["present"]))}}),kp=(0,o.pM)({inheritAttrs:!1,__name:"ScrollAreaScrollbarHover",props:{forceMount:{type:Boolean}},setup(e){const t=dp(),{forwardRef:n}=Gl();let r;const l=(0,a.KR)(!1);function i(){window.clearTimeout(r),l.value=!0}function s(){r=window.setTimeout(()=>{l.value=!1},t.scrollHideDelay.value)}return(0,o.sV)(()=>{const e=t.scrollArea.value;e&&(e.addEventListener("pointerenter",i),e.addEventListener("pointerleave",s))}),(0,o.hi)(()=>{const e=t.scrollArea.value;e&&(window.clearTimeout(r),e.removeEventListener("pointerenter",i),e.removeEventListener("pointerleave",s))}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||l.value},{default:(0,o.k6)(()=>[(0,o.bF)(Cp,(0,o.v6)(e.$attrs,{ref:(0,a.R1)(n),"data-state":l.value?"visible":"hidden"}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["data-state"])]),_:3},8,["present"]))}}),Mp=(0,o.pM)({__name:"ScrollAreaScrollbarScroll",props:{forceMount:{type:Boolean}},setup(e){const t=dp(),n=Sp(),{forwardRef:r}=Gl(),{state:l,dispatch:i}=fi("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});(0,o.nT)(e=>{if("idle"===l.value){const n=window.setTimeout(()=>i("HIDE"),t.scrollHideDelay.value);e(()=>{window.clearTimeout(n)})}});const s=Hr(()=>i("SCROLL_END"),100);return(0,o.nT)(e=>{const o=t.viewport.value,a=n.isHorizontal.value?"scrollLeft":"scrollTop";if(o){let t=o[a];const n=()=>{const e=o[a];t!==e&&(i("SCROLL"),s()),t=e};o.addEventListener("scroll",n),e(()=>{o.removeEventListener("scroll",n)})}}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ki),{present:e.forceMount||"hidden"!==(0,a.R1)(l)},{default:(0,o.k6)(()=>[(0,o.bF)(Ep,(0,o.v6)(e.$attrs,{ref:(0,a.R1)(r)}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)]),_:3},8,["present"]))}}),[Sp,Dp]=Za("ScrollAreaScrollbar"),Ap=(Boolean,Boolean,Boolean,(0,o.pM)({__name:"ScrollAreaCornerImpl",setup(e){const t=dp(),n=(0,a.KR)(0),r=(0,a.KR)(0),l=(0,o.EW)(()=>!!n.value&&!!r.value);function i(){var e;const n=(null==(e=t.scrollbarX.value)?void 0:e.offsetHeight)||0;t.onCornerHeightChange(n),r.value=n}function s(){var e;const o=(null==(e=t.scrollbarY.value)?void 0:e.offsetWidth)||0;t.onCornerWidthChange(o),n.value=o}return pl(t.scrollbarX.value,i),pl(t.scrollbarY.value,s),(0,o.wB)(()=>t.scrollbarX.value,i),(0,o.wB)(()=>t.scrollbarY.value,s),(e,i)=>{var s;return l.value?((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({key:0,style:{width:`${n.value}px`,height:`${r.value}px`,position:"absolute",right:"ltr"===(0,a.R1)(t).dir.value?0:void 0,left:"rtl"===(0,a.R1)(t).dir.value?0:void 0,bottom:0}},null==(s=e.$parent)?void 0:s.$props),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["style"])):(0,o.Q3)("",!0)}}})),Op=(Boolean,["default-value"]),Tp=(0,o.pM)({__name:"BubbleSelect",props:{autocomplete:{},autofocus:{type:Boolean},disabled:{type:Boolean},form:{},multiple:{type:Boolean},name:{},required:{type:Boolean},size:{},value:{}},setup(e){const t=e,{value:n}=(0,a.QW)(t),r=(0,a.KR)();return(e,i)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Mu),{"as-child":""},{default:(0,o.k6)(()=>[(0,o.bo)((0,o.Lk)("select",(0,o.v6)({ref_key:"selectElement",ref:r},t,{"onUpdate:modelValue":i[0]||(i[0]=e=>(0,a.i9)(n)?n.value=e:null),"default-value":(0,a.R1)(n)}),[(0,o.RG)(e.$slots,"default")],16,Op),[[l.u1,(0,a.R1)(n)]])]),_:3}))}}),Pp={key:0,value:""},[Ip,Wp]=Za("SelectRoot"),[Fp,$p]=Za("SelectRoot"),Np=(Boolean,Boolean,Boolean,Boolean,[" ","Enter","ArrowUp","ArrowDown"]),Vp=[" ","Enter"],Lp=10;function Kp(e){return""===e||ir(e)}Boolean,Boolean,Boolean,Boolean;const[jp,Gp]=Za("SelectItemAlignedPosition"),Xp=(0,o.pM)({inheritAttrs:!1,__name:"SelectItemAlignedPosition",props:{asChild:{type:Boolean},as:{}},emits:["placed"],setup(e,{emit:t}){const n=e,l=t,{injectCollection:i}=Pl(),s=Ip(),u=Hp(),d=i(),c=(0,a.KR)(!1),f=(0,a.KR)(!0),p=(0,a.KR)(),{forwardRef:v,currentElement:h}=Gl(),{viewport:m,selectedItem:y,selectedItemText:g,focusSelectedItem:b}=u;function w(){if(s.triggerElement.value&&s.valueElement.value&&p.value&&h.value&&null!=m&&m.value&&null!=y&&y.value&&null!=g&&g.value){const e=s.triggerElement.value.getBoundingClientRect(),t=h.value.getBoundingClientRect(),n=s.valueElement.value.getBoundingClientRect(),o=g.value.getBoundingClientRect();if("rtl"!==s.dir.value){const a=o.left-t.left,r=n.left-a,l=e.left-r,i=e.width+l,s=Math.max(i,t.width),u=window.innerWidth-Lp,d=Ja(r,Lp,Math.max(Lp,u-s));p.value.style.minWidth=`${i}px`,p.value.style.left=`${d}px`}else{const a=t.right-o.right,r=window.innerWidth-n.right-a,l=window.innerWidth-e.right-r,i=e.width+l,s=Math.max(i,t.width),u=window.innerWidth-Lp,d=Ja(r,Lp,Math.max(Lp,u-s));p.value.style.minWidth=`${i}px`,p.value.style.right=`${d}px`}const a=d.value,r=window.innerHeight-2*Lp,i=m.value.scrollHeight,u=window.getComputedStyle(h.value),f=Number.parseInt(u.borderTopWidth,10),v=Number.parseInt(u.paddingTop,10),b=Number.parseInt(u.borderBottomWidth,10),w=Number.parseInt(u.paddingBottom,10),R=f+v+i+w+b,x=Math.min(5*y.value.offsetHeight,R),B=window.getComputedStyle(m.value),_=Number.parseInt(B.paddingTop,10),E=Number.parseInt(B.paddingBottom,10),C=e.top+e.height/2-Lp,k=r-C,M=y.value.offsetHeight/2,S=y.value.offsetTop+M,D=f+v+S,A=R-D;if(D<=C){const e=y.value===a[a.length-1];p.value.style.bottom="0px";const t=h.value.clientHeight-m.value.offsetTop-m.value.offsetHeight,n=Math.max(k,M+(e?E:0)+t+b),o=D+n;p.value.style.height=`${o}px`}else{const e=y.value===a[0];p.value.style.top="0px";const t=Math.max(C,f+m.value.offsetTop+(e?_:0)+M)+A;p.value.style.height=`${t}px`,m.value.scrollTop=D-C+m.value.offsetTop}p.value.style.margin=`${Lp}px 0`,p.value.style.minHeight=`${x}px`,p.value.style.maxHeight=`${r}px`,l("placed"),requestAnimationFrame(()=>c.value=!0)}}const R=(0,a.KR)("");function x(e){e&&!0===f.value&&(w(),null==b||b(),f.value=!1)}return(0,o.sV)(async()=>{await(0,o.dY)(),w(),h.value&&(R.value=window.getComputedStyle(h.value).zIndex)}),Gp({contentWrapper:p,shouldExpandOnScrollRef:c,onScrollButtonChange:x}),(e,t)=>((0,o.uX)(),(0,o.CE)("div",{ref_key:"contentWrapperElement",ref:p,style:(0,r.Tr)({display:"flex",flexDirection:"column",position:"fixed",zIndex:R.value})},[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(v),style:{boxSizing:"border-box",maxHeight:"100%"}},{...e.$attrs,...n}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)],4))}}),Up=(0,o.pM)({__name:"SelectPopperPosition",props:{side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{default:Lp},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=Kl(e);return(e,n)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Bu),(0,o.v6)((0,a.R1)(t),{style:{boxSizing:"border-box","--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),zp={onViewportChange:()=>{},itemTextRefCallback:()=>{},itemRefCallback:()=>{}},[Hp,Yp]=Za("SelectContent"),Zp=(0,o.pM)({__name:"SelectContentImpl",props:{position:{default:"item-aligned"},bodyLock:{type:Boolean,default:!0},side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,i=Ip();Nl(),Dl(n.bodyLock);const{createCollection:s}=Pl(),u=(0,a.KR)();ii(u);const d=s(u),{search:c,handleTypeaheadSearch:f}=vi(d),p=(0,a.KR)(),v=(0,a.KR)(),h=(0,a.KR)(),m=(0,a.KR)(!1),y=(0,a.KR)(!1);function g(){v.value&&u.value&&Rs([v.value,u.value])}(0,o.wB)(m,()=>{g()});const{onOpenChange:b,triggerPointerDownPosRef:w}=i;function R(e){const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),!t&&1===e.key.length&&f(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=d.value;if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,o=t.indexOf(n);t=t.slice(o+1)}setTimeout(()=>Rs(t)),e.preventDefault()}}(0,o.nT)(e=>{if(!u.value)return;let t={x:0,y:0};const n=e=>{var n,o;t={x:Math.abs(Math.round(e.pageX)-((null==(n=w.value)?void 0:n.x)??0)),y:Math.abs(Math.round(e.pageY)-((null==(o=w.value)?void 0:o.y)??0))}},o=e=>{var o;"touch"!==e.pointerType&&(t.x<=10&&t.y<=10?e.preventDefault():null!=(o=u.value)&&o.contains(e.target)||b(!1),document.removeEventListener("pointermove",n),w.value=null)};null!==w.value&&(document.addEventListener("pointermove",n),document.addEventListener("pointerup",o,{capture:!0,once:!0})),e(()=>{document.removeEventListener("pointermove",n),document.removeEventListener("pointerup",o,{capture:!0})})});const x=(0,o.EW)(()=>"popper"===n.position?n:{}),B=Kl(x.value);return Yp({content:u,viewport:p,onViewportChange:e=>{p.value=e},itemRefCallback:(e,t,n)=>{var o,a;const r=!y.value&&!n;(void 0!==(null==(o=i.modelValue)?void 0:o.value)&&(null==(a=i.modelValue)?void 0:a.value)===t||r)&&(v.value=e,r&&(y.value=!0))},selectedItem:v,selectedItemText:h,onItemLeave:()=>{var e;null==(e=u.value)||e.focus()},itemTextRefCallback:(e,t,n)=>{var o,a;const r=!y.value&&!n;(void 0!==(null==(o=i.modelValue)?void 0:o.value)&&(null==(a=i.modelValue)?void 0:a.value)===t||r)&&(h.value=e)},focusSelectedItem:g,position:n.position,isPositioned:m,searchRef:c}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(ds),{"as-child":"",onMountAutoFocus:t[6]||(t[6]=(0,l.D$)(()=>{},["prevent"])),onUnmountAutoFocus:t[7]||(t[7]=e=>{var t;r("closeAutoFocus",e),!e.defaultPrevented&&(null==(t=(0,a.R1)(i).triggerElement.value)||t.focus({preventScroll:!0}),e.preventDefault())})},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Hi),{"as-child":"","disable-outside-pointer-events":"",onFocusOutside:t[2]||(t[2]=(0,l.D$)(()=>{},["prevent"])),onDismiss:t[3]||(t[3]=e=>(0,a.R1)(i).onOpenChange(!1)),onEscapeKeyDown:t[4]||(t[4]=e=>r("escapeKeyDown",e)),onPointerDownOutside:t[5]||(t[5]=e=>r("pointerDownOutside",e))},{default:(0,o.k6)(()=>[((0,o.uX)(),(0,o.Wv)((0,o.$y)("popper"===e.position?Up:Xp),(0,o.v6)({...e.$attrs,...(0,a.R1)(B)},{id:(0,a.R1)(i).contentId,ref:e=>{u.value=(0,a.R1)(nl)(e)},role:"listbox","data-state":(0,a.R1)(i).open.value?"open":"closed",dir:(0,a.R1)(i).dir.value,style:{display:"flex",flexDirection:"column",outline:"none"},onContextmenu:t[0]||(t[0]=(0,l.D$)(()=>{},["prevent"])),onPlaced:t[1]||(t[1]=e=>m.value=!0),onKeydown:R}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["id","data-state","dir","onKeydown"]))]),_:3})]),_:3}))}}),qp=(0,o.pM)({inheritAttrs:!1,__name:"SelectProvider",props:{context:{}},setup(e){return Wp(e.context),(e,t)=>(0,o.RG)(e.$slots,"default")}}),Jp={key:1},[Qp,ev]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("SelectItem")),[tv,nv]=(Boolean,Boolean,Boolean,Za("SelectGroup")),ov=(Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"SelectScrollButtonImpl",emits:["autoScroll"],setup(e,{emit:t}){const n=t,{injectCollection:r}=Pl(),l=r(),i=Hp(zp),s=(0,a.KR)(null);function u(){null!==s.value&&(window.clearInterval(s.value),s.value=null)}function d(){null===s.value&&(s.value=window.setInterval(()=>{n("autoScroll")},50))}function c(){var e;null==(e=i.onItemLeave)||e.call(i),null===s.value&&(s.value=window.setInterval(()=>{n("autoScroll")},50))}return(0,o.nT)(()=>{const e=l.value.find(e=>e===yl());null==e||e.scrollIntoView({block:"nearest"})}),(0,o.xo)(()=>u()),(e,t)=>{var n;return(0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({"aria-hidden":"true",style:{flexShrink:0}},null==(n=e.$parent)?void 0:n.$props,{onPointerdown:d,onPointermove:c,onPointerleave:t[0]||(t[0]=()=>{u()})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16)}}})),av=(Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=["horizontal","vertical"];function r(e){return n.includes(e)}const l=(0,o.EW)(()=>r(t.orientation)?t.orientation:"horizontal"),i=(0,o.EW)(()=>"vertical"===l.value?t.orientation:void 0),s=(0,o.EW)(()=>t.decorative?{role:"none"}:{"aria-orientation":i.value,role:"separator"});return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({as:e.as,"as-child":e.asChild,"data-orientation":l.value},s.value),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}})),rv=(0,o.pM)({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.Wv)(av,(0,r._B)((0,o.Ng)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}});function lv(e=[],t,n){const o=[...e];return o[n]=t,o.sort((e,t)=>e-t)}function iv(e,t,n){const o=100/(n-t)*(e-t);return Ja(o,0,100)}function sv(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}function uv(e,t){if(1===e.length)return 0;const n=e.map(e=>Math.abs(e-t)),o=Math.min(...n);return n.indexOf(o)}function dv(e,t,n){const o=e/2,a=pv([0,50],[0,o]);return(o-a(t)*n)*n}function cv(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function fv(e,t){if(t>0){const n=cv(e);return Math.min(...n)>=t}return!0}function pv(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(n-e[0])}}function vv(e){return(String(e).split(".")[1]||"").length}function hv(e,t){const n=10**t;return Math.round(e*n)/n}const mv=["PageUp","PageDown"],yv=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],gv={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},[bv,wv]=Za(["SliderVertical","SliderHorizontal"]),Rv=(0,o.pM)({__name:"SliderImpl",props:{asChild:{type:Boolean},as:{default:"span"}},emits:["slideStart","slideMove","slideEnd","homeKeyDown","endKeyDown","stepKeyDown"],setup(e,{emit:t}){const n=e,r=t,l=Ev();return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),(0,o.v6)({"data-slider-impl":""},n,{onKeydown:t[0]||(t[0]=e=>{"Home"===e.key?(r("homeKeyDown",e),e.preventDefault()):"End"===e.key?(r("endKeyDown",e),e.preventDefault()):(0,a.R1)(mv).concat((0,a.R1)(yv)).includes(e.key)&&(r("stepKeyDown",e),e.preventDefault())}),onPointerdown:t[1]||(t[1]=e=>{const t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),(0,a.R1)(l).thumbElements.value.includes(t)?t.focus():r("slideStart",e)}),onPointermove:t[2]||(t[2]=e=>{e.target.hasPointerCapture(e.pointerId)&&r("slideMove",e)}),onPointerup:t[3]||(t[3]=e=>{const t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),r("slideEnd",e))})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}}),xv=(0,o.pM)({__name:"SliderHorizontal",props:{dir:{},min:{},max:{},inverted:{type:Boolean}},emits:["slideEnd","slideStart","slideMove","homeKeyDown","endKeyDown","stepKeyDown"],setup(e,{emit:t}){const n=e,r=t,{max:l,min:i,dir:s,inverted:u}=(0,a.QW)(n),{forwardRef:d,currentElement:c}=Gl(),f=(0,a.KR)(),p=(0,o.EW)(()=>"ltr"===(null==s?void 0:s.value)&&!u.value||"ltr"!==(null==s?void 0:s.value)&&u.value);function v(e){const t=f.value||c.value.getBoundingClientRect(),n=[0,t.width],o=p.value?[i.value,l.value]:[l.value,i.value],a=pv(n,o);return f.value=t,a(e-t.left)}return wv({startEdge:p.value?"left":"right",endEdge:p.value?"right":"left",direction:p.value?1:-1,size:"width"}),(e,t)=>((0,o.uX)(),(0,o.Wv)(Rv,{ref:(0,a.R1)(d),dir:(0,a.R1)(s),"data-orientation":"horizontal",style:{"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:t[0]||(t[0]=e=>{const t=v(e.clientX);r("slideStart",t)}),onSlideMove:t[1]||(t[1]=e=>{const t=v(e.clientX);r("slideMove",t)}),onSlideEnd:t[2]||(t[2]=()=>{f.value=void 0,r("slideEnd")}),onStepKeyDown:t[3]||(t[3]=e=>{const t=p.value?"from-left":"from-right",n=(0,a.R1)(gv)[t].includes(e.key);r("stepKeyDown",e,n?-1:1)}),onEndKeyDown:t[4]||(t[4]=e=>r("endKeyDown",e)),onHomeKeyDown:t[5]||(t[5]=e=>r("homeKeyDown",e))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["dir"]))}}),Bv=(0,o.pM)({__name:"SliderVertical",props:{min:{},max:{},inverted:{type:Boolean}},emits:["slideEnd","slideStart","slideMove","homeKeyDown","endKeyDown","stepKeyDown"],setup(e,{emit:t}){const n=e,r=t,{max:l,min:i,inverted:s}=(0,a.QW)(n),{forwardRef:u,currentElement:d}=Gl(),c=(0,a.KR)(),f=(0,o.EW)(()=>!s.value);function p(e){const t=c.value||d.value.getBoundingClientRect(),n=[0,t.height],o=f.value?[l.value,i.value]:[i.value,l.value],a=pv(n,o);return c.value=t,a(e-t.top)}return wv({startEdge:f.value?"bottom":"top",endEdge:f.value?"top":"bottom",size:"height",direction:f.value?1:-1}),(e,t)=>((0,o.uX)(),(0,o.Wv)(Rv,{ref:(0,a.R1)(u),"data-orientation":"vertical",style:{"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:t[0]||(t[0]=e=>{const t=p(e.clientY);r("slideStart",t)}),onSlideMove:t[1]||(t[1]=e=>{const t=p(e.clientY);r("slideMove",t)}),onSlideEnd:t[2]||(t[2]=()=>{c.value=void 0,r("slideEnd")}),onStepKeyDown:t[3]||(t[3]=e=>{const t=f.value?"from-bottom":"from-top",n=(0,a.R1)(gv)[t].includes(e.key);r("stepKeyDown",e,n?-1:1)}),onEndKeyDown:t[4]||(t[4]=e=>r("endKeyDown",e)),onHomeKeyDown:t[5]||(t[5]=e=>r("homeKeyDown",e))},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},512))}}),_v=["value","name","disabled","step"],[Ev,Cv]=Za("SliderRoot"),kv=(Boolean,Boolean,Boolean,(0,o.pM)({inheritAttrs:!1,__name:"SliderThumbImpl",props:{index:{},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Ev(),r=bv(),{forwardRef:l,currentElement:i}=Gl(),s=(0,o.EW)(()=>{var e,o;return null==(o=null==(e=n.modelValue)?void 0:e.value)?void 0:o[t.index]}),u=(0,o.EW)(()=>void 0===s.value?0:iv(s.value,n.min.value??0,n.max.value??100)),d=(0,o.EW)(()=>{var e,o;return sv(t.index,(null==(o=null==(e=n.modelValue)?void 0:e.value)?void 0:o.length)??0)}),c=ci(i),f=(0,o.EW)(()=>c[r.size].value),p=(0,o.EW)(()=>f.value?dv(f.value,u.value,r.direction):0),v=il();return(0,o.sV)(()=>{n.thumbElements.value.push(i.value)}),(0,o.hi)(()=>{const e=n.thumbElements.value.findIndex(e=>e===i.value)??-1;n.thumbElements.value.splice(e,1)}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Iu),null,{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),(0,o.v6)(e.$attrs,{ref:(0,a.R1)(l),role:"slider","data-radix-vue-collection-item":"",tabindex:(0,a.R1)(n).disabled.value?void 0:0,"aria-label":e.$attrs["aria-label"]||d.value,"data-disabled":(0,a.R1)(n).disabled.value?"":void 0,"data-orientation":(0,a.R1)(n).orientation.value,"aria-valuenow":s.value,"aria-valuemin":(0,a.R1)(n).min.value,"aria-valuemax":(0,a.R1)(n).max.value,"aria-orientation":(0,a.R1)(n).orientation.value,"as-child":e.asChild,as:e.as,style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[(0,a.R1)(r).startEdge]:`calc(${u.value}% + ${p.value}px)`,display:(0,a.R1)(v)||void 0!==s.value?void 0:"none"},onFocus:t[0]||(t[0]=()=>{(0,a.R1)(n).valueIndexToChangeRef.value=e.index})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["tabindex","aria-label","data-disabled","data-orientation","aria-valuenow","aria-valuemin","aria-valuemax","aria-orientation","as-child","as","style"])]),_:3}))}}));Boolean,Boolean,Boolean;let Mv=null,Sv=null;function Dv(e,t){if(t){const e=0!==(t&Yv),n=0!==(t&Zv),o=0!==(t&qv),a=0!==(t&Jv);if(e)return o?"se-resize":a?"ne-resize":"e-resize";if(n)return o?"sw-resize":a?"nw-resize":"w-resize";if(o)return"s-resize";if(a)return"n-resize"}switch(e){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}function Av(){null!==Sv&&(document.head.removeChild(Sv),Mv=null,Sv=null)}function Ov(e,t){const n=Dv(e,t);Mv!==n&&(Mv=n,null===Sv&&(Sv=document.createElement("style"),document.head.appendChild(Sv)),Sv.innerHTML=`*{cursor: ${n}!important;}`)}function Tv({defaultSize:e,dragState:t,layout:n,panelData:o,panelIndex:a,precision:r=3}){const l=n[a];let i;return i=null==l?void 0!==e?e.toPrecision(r):"1":1===o.length?"1":l.toPrecision(r),{flexBasis:0,flexGrow:i,flexShrink:1,overflow:"hidden",pointerEvents:null!==t?"none":void 0}}function Pv(e){return"keydown"===e.type}function Iv(e){return e.type.startsWith("mouse")}function Wv(e){return e.type.startsWith("touch")}function Fv(e){if(Iv(e))return{x:e.clientX,y:e.clientY};if(Wv(e)){const t=e.touches[0];if(t&&t.clientX&&t.clientY)return{x:t.clientX,y:t.clientY}}return{x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY}}function $v(e,t){const n="horizontal"===e,{x:o,y:a}=Fv(t);return n?o:a}function Nv(e,t,n){return e.x<t.x+t.width&&e.x+e.width>t.x&&e.y<t.y+t.height&&e.y+e.height>t.y}function Vv(e,t="Assertion failed!"){if(!e)throw console.error(t),new Error(t)}function Lv(e,t){if(e===t)throw new Error("Cannot compare node with itself");const n={a:zv(e),b:zv(t)};let o;for(;n.a.at(-1)===n.b.at(-1);)e=n.a.pop(),t=n.b.pop(),o=e;Vv(o);const a={a:Uv(Xv(n.a)),b:Uv(Xv(n.b))};if(a.a===a.b){const e=o.childNodes,t={a:n.a.at(-1),b:n.b.at(-1)};let a=e.length;for(;a--;){const n=e[a];if(n===t.a)return 1;if(n===t.b)return-1}}return Math.sign(a.a-a.b)}const Kv=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function jv(e){const t=getComputedStyle(Hv(e)).display;return"flex"===t||"inline-flex"===t}function Gv(e){const t=getComputedStyle(e);return!!("fixed"===t.position||"auto"!==t.zIndex&&("static"!==t.position||jv(e))||+t.opacity<1||"transform"in t&&"none"!==t.transform||"webkitTransform"in t&&"none"!==t.webkitTransform||"mixBlendMode"in t&&"normal"!==t.mixBlendMode||"filter"in t&&"none"!==t.filter||"webkitFilter"in t&&"none"!==t.webkitFilter||"isolation"in t&&"isolate"===t.isolation||Kv.test(t.willChange)||"touch"===t.webkitOverflowScrolling)}function Xv(e){let t=e.length;for(;t--;){const n=e[t];if(Vv(n),Gv(n))return n}return null}function Uv(e){return e&&Number(getComputedStyle(e).zIndex)||0}function zv(e){const t=[];for(;e;)t.push(e),e=Hv(e);return t}function Hv(e){var t;return e.parentNode instanceof DocumentFragment&&(null==(t=e.parentNode)?void 0:t.host)||e.parentNode}const Yv=1,Zv=2,qv=4,Jv=8;function Qv(){if("function"==typeof matchMedia)return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}const eh="coarse"===Qv(),th=[];let nh=!1;const oh=new Map,ah=new Map,rh=new Set;function lh(e,t,n,o,a){const{ownerDocument:r}=t,l={direction:n,element:t,hitAreaMargins:o,setResizeHandlerState:a},i=oh.get(r)??0;return oh.set(r,i+1),rh.add(l),ph(),function(){ah.delete(e),rh.delete(l);const t=oh.get(r)??1;oh.set(r,t-1),ph(),Av(),1===t&&oh.delete(r)}}function ih(e){const{target:t}=e,{x:n,y:o}=Fv(e);nh=!0,dh({target:t,x:n,y:o}),ph(),th.length>0&&(vh("down",e),e.preventDefault())}function sh(e){const{x:t,y:n}=Fv(e);if(!nh){const{target:o}=e;dh({target:o,x:t,y:n})}vh("move",e),fh(),th.length>0&&e.preventDefault()}function uh(e){const{target:t}=e,{x:n,y:o}=Fv(e);ah.clear(),nh=!1,th.length>0&&e.preventDefault(),vh("up",e),dh({target:t,x:n,y:o}),fh(),ph()}function dh({target:e,x:t,y:n}){th.splice(0);let o=null;e instanceof HTMLElement&&(o=e),rh.forEach(e=>{const{element:a,hitAreaMargins:r}=e,l=a.getBoundingClientRect(),{bottom:i,left:s,right:u,top:d}=l,c=eh?r.coarse:r.fine;if(t>=s-c&&t<=u+c&&n>=d-c&&n<=i+c){if(null!==o&&a!==o&&!a.contains(o)&&!o.contains(a)&&Lv(o,a)>0){let e=o,t=!1;for(;e&&!e.contains(a);){if(Nv(e.getBoundingClientRect(),l)){t=!0;break}e=e.parentElement}if(t)return}th.push(e)}})}function ch(e,t){ah.set(e,t)}function fh(){let e=!1,t=!1;th.forEach(n=>{const{direction:o}=n;"horizontal"===o.value?e=!0:t=!0});let n=0;ah.forEach(e=>{n|=e}),e&&t?Ov("intersection",n):e?Ov("horizontal",n):t?Ov("vertical",n):Av()}function ph(){oh.forEach((e,t)=>{const{body:n}=t;n.removeEventListener("contextmenu",uh),n.removeEventListener("mousedown",ih),n.removeEventListener("mouseleave",sh),n.removeEventListener("mousemove",sh),n.removeEventListener("touchmove",sh),n.removeEventListener("touchstart",ih)}),window.removeEventListener("mouseup",uh),window.removeEventListener("touchcancel",uh),window.removeEventListener("touchend",uh),rh.size>0&&(nh?(th.length>0&&oh.forEach((e,t)=>{const{body:n}=t;e>0&&(n.addEventListener("contextmenu",uh),n.addEventListener("mouseleave",sh),n.addEventListener("mousemove",sh),n.addEventListener("touchmove",sh,{passive:!1}))}),window.addEventListener("mouseup",uh),window.addEventListener("touchcancel",uh),window.addEventListener("touchend",uh)):oh.forEach((e,t)=>{const{body:n}=t;e>0&&(n.addEventListener("mousedown",ih),n.addEventListener("mousemove",sh),n.addEventListener("touchmove",sh,{passive:!1}),n.addEventListener("touchstart",ih))}))}function vh(e,t){rh.forEach(n=>{const{setResizeHandlerState:o}=n,a=th.includes(n);o(e,a,t)})}const hh=10;function mh(e,t,n=hh){e=Number.parseFloat(e.toFixed(n)),t=Number.parseFloat(t.toFixed(n));const o=e-t;return 0===o?0:o>0?1:-1}function yh(e,t,n){return 0===mh(e,t,n)}function gh({panelConstraints:e,panelIndex:t,size:n}){const o=e[t];Vv(null!=o);const{collapsedSize:a=0,collapsible:r,maxSize:l=100,minSize:i=0}=o;if(mh(n,i)<0)if(r){const e=(a+i)/2;n=mh(n,e)<0?a:i}else n=i;return n=Math.min(l,n),n=Number.parseFloat(n.toFixed(hh)),n}function bh(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function wh({delta:e,layout:t,panelConstraints:n,pivotIndices:o,trigger:a}){if(yh(e,0))return t;const r=[...t],[l,i]=o;Vv(null!=l),Vv(null!=i);let s=0;if("keyboard"===a){{const o=e<0?i:l,a=n[o];if(Vv(a),a.collapsible){const a=t[o];Vv(null!=a);const r=n[o];Vv(r);const{collapsedSize:l=0,minSize:i=0}=r;if(yh(a,l)){const t=i-a;mh(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}{const o=e<0?l:i,a=n[o];Vv(a);const{collapsible:r}=a;if(r){const a=t[o];Vv(null!=a);const r=n[o];Vv(r);const{collapsedSize:l=0,minSize:i=0}=r;if(yh(a,i)){const t=a-l;mh(t,Math.abs(e))>0&&(e=e<0?0-t:t)}}}}{const o=e<0?1:-1;let a=e<0?i:l,r=0;for(;;){const e=t[a];Vv(null!=e);const l=gh({panelConstraints:n,panelIndex:a,size:100})-e;if(r+=l,a+=o,a<0||a>=n.length)break}const s=Math.min(Math.abs(e),Math.abs(r));e=e<0?0-s:s}{let o=e<0?l:i;for(;o>=0&&o<n.length;){const a=Math.abs(e)-Math.abs(s),l=t[o];Vv(null!=l);const i=l-a,u=gh({panelConstraints:n,panelIndex:o,size:i});if(!yh(l,u)&&(s+=l-u,r[o]=u,s.toPrecision(3).localeCompare(Math.abs(e).toPrecision(3),void 0,{numeric:!0})>=0))break;e<0?o--:o++}}if(yh(s,0))return t;{const o=e<0?i:l,a=t[o];Vv(null!=a);const u=a+s,d=gh({panelConstraints:n,panelIndex:o,size:u});if(r[o]=d,!yh(d,u)){let t=u-d,o=e<0?i:l;for(;o>=0&&o<n.length;){const a=r[o];Vv(null!=a);const l=a+t,i=gh({panelConstraints:n,panelIndex:o,size:l});if(yh(a,i)||(t-=i-a,r[o]=i),yh(t,0))break;e>0?o--:o++}}}const u=r.reduce((e,t)=>t+e,0);return yh(u,100)?r:t}function Rh(e,t=document){var n;if(!lr)return null;if(t instanceof HTMLElement&&(null==(n=null==t?void 0:t.dataset)?void 0:n.panelGroupId)===e)return t;const o=t.querySelector(`[data-panel-group][data-panel-group-id="${e}"]`);return o||null}function xh(e,t=document){if(!lr)return null;const n=t.querySelector(`[data-panel-resize-handle-id="${e}"]`);return n||null}function Bh(e,t,n=document){return lr?_h(e,n).findIndex(e=>e.getAttribute("data-panel-resize-handle-id")===t)??null:null}function _h(e,t=document){return lr?Array.from(t.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id="${e}"]`)):[]}function Eh(e,t,n,o=document){var a,r;const l=xh(t,o),i=_h(e,o),s=l?i.indexOf(l):-1,u=(null==(a=n[s])?void 0:a.id)??null,d=(null==(r=n[s+1])?void 0:r.id)??null;return[u,d]}function Ch(e,t,n,o,a){const r="horizontal"===n,l=xh(t,a);Vv(l);const i=l.getAttribute("data-panel-group-id");Vv(i);const{initialCursorPosition:s}=o,u=$v(n,e),d=Rh(i,a);Vv(d);const c=d.getBoundingClientRect(),f=r?c.width:c.height;return(u-s)/f*100}function kh(e,t,n,o,a,r){if(Pv(e)){const t="horizontal"===n;let o=0;o=e.shiftKey?100:a??10;let r=0;switch(e.key){case"ArrowDown":r=t?0:o;break;case"ArrowLeft":r=t?-o:0;break;case"ArrowRight":r=t?o:0;break;case"ArrowUp":r=t?0:-o;break;case"End":r=100;break;case"Home":r=-100;break}return r}return null==o?0:Ch(e,t,n,o,r)}function Mh({layout:e,panelsArray:t,pivotIndices:n}){let o=0,a=100,r=0,l=0;const i=n[0];Vv(null!=i),t.forEach((e,t)=>{const{constraints:n}=e,{maxSize:s=100,minSize:u=0}=n;t===i?(o=u,a=s):(r+=u,l+=s)});const s=Math.min(a,100-r),u=Math.max(o,100-l),d=e[i];return{valueMax:s,valueMin:u,valueNow:d}}function Sh({panelDataArray:e}){const t=Array(e.length),n=e.map(e=>e.constraints);let o=0,a=100;for(let r=0;r<e.length;r++){const e=n[r];Vv(e);const{defaultSize:l}=e;null!=l&&(o++,t[r]=l,a-=l)}for(let r=0;r<e.length;r++){const l=n[r];Vv(l);const{defaultSize:i}=l;if(null!=i)continue;const s=e.length-o,u=a/s;o++,t[r]=u,a-=u}return t}function Dh(e,t,n){t.forEach((t,o)=>{const a=e[o];Vv(a);const{callbacks:r,constraints:l,id:i}=a,{collapsedSize:s=0,collapsible:u}=l,d=n[i];if(null==d||t!==d){n[i]=t;const{onCollapse:e,onExpand:o,onResize:a}=r;a&&a(t,d),u&&(e||o)&&(o&&(null==d||d===s)&&t!==s&&o(),e&&(null==d||d!==s)&&t===s&&e())}})}function Ah(e,t=10){let n=null;return(...o)=>{null!==n&&clearTimeout(n),n=setTimeout(()=>{e(...o)},t)}}function Oh(e,t,n){const o=Bh(e,t,n);return null!=o?[o,o+1]:[-1,-1]}function Th({layout:e,panelConstraints:t}){const n=[...e],o=n.reduce((e,t)=>e+t,0);if(n.length!==t.length)throw new Error(`Invalid ${t.length} panel layout: ${n.map(e=>`${e}%`).join(", ")}`);if(!yh(o,100)){console.warn(`WARNING: Invalid layout total size: ${n.map(e=>`${e}%`).join(", ")}. Layout normalization will be applied.`);for(let e=0;e<t.length;e++){const t=n[e];Vv(null!=t);const a=100/o*t;n[e]=a}}let a=0;for(let r=0;r<t.length;r++){const e=n[r];Vv(null!=e);const o=gh({panelConstraints:t,panelIndex:r,size:e});e!==o&&(a+=e-o,n[r]=o)}if(!yh(a,0))for(let r=0;r<t.length;r++){const e=n[r];Vv(null!=e);const o=e+a,l=gh({panelConstraints:t,panelIndex:r,size:o});if(e!==l&&(a-=l-e,n[r]=l,yh(a,0)))break}return n}function Ph(e){try{if(!(typeof localStorage<"u"))throw new TypeError("localStorage not supported in this environment");e.getItem=e=>localStorage.getItem(e),e.setItem=(e,t)=>{localStorage.setItem(e,t)}}catch(t){console.error(t),e.getItem=()=>null,e.setItem=()=>{}}}function Ih(e){return`radix-vue:${e}`}function Wh(e){return e.map(e=>{const{constraints:t,id:n,idIsFromProps:o,order:a}=e;return o?n:a?`${a}:${JSON.stringify(t)}`:JSON.stringify(t)}).sort((e,t)=>e.localeCompare(t)).join(",")}function Fh(e,t){try{const n=Ih(e),o=t.getItem(n);if(o){const e=JSON.parse(o);if("object"==typeof e&&null!=e)return e}}catch{}return null}function $h(e,t,n){const o=Fh(e,n)??{},a=Wh(t);return o[a]??null}function Nh(e,t,n,o,a){const r=Ih(e),l=Wh(t),i=Fh(e,a)??{};i[l]={expandToSizes:Object.fromEntries(n.entries()),layout:o};try{a.setItem(r,JSON.stringify(i))}catch(s){console.error(s)}}function Vh({eagerValuesRef:e,groupId:t,layout:n,panelDataArray:a,panelGroupElement:r,setLayout:l}){(0,o.nT)(e=>{const o=r.value;if(!o)return;const l=_h(t,o);for(let t=0;t<a.length-1;t++){const{valueMax:e,valueMin:o,valueNow:r}=Mh({layout:n.value,panelsArray:a,pivotIndices:[t,t+1]}),i=l[t];if(null!=i){const n=a[t];Vv(n),i.setAttribute("aria-controls",n.id),i.setAttribute("aria-valuemax",`${Math.round(e)}`),i.setAttribute("aria-valuemin",`${Math.round(o)}`),i.setAttribute("aria-valuenow",null!=r?`${Math.round(r)}`:"")}}e(()=>{l.forEach(e=>{e.removeAttribute("aria-controls"),e.removeAttribute("aria-valuemax"),e.removeAttribute("aria-valuemin"),e.removeAttribute("aria-valuenow")})})}),(0,o.nT)(o=>{const a=r.value;if(!a)return;const i=e.value;Vv(i);const{panelDataArray:s}=i,u=Rh(t,a);Vv(null!=u,`No group found for id "${t}"`);const d=_h(t,a);Vv(d);const c=d.map(e=>{const o=e.getAttribute("data-panel-resize-handle-id");Vv(o);const[r,i]=Eh(t,o,s,a);if(null==r||null==i)return()=>{};const u=e=>{if(!e.defaultPrevented)switch(e.key){case"Enter":{e.preventDefault();const i=s.findIndex(e=>e.id===r);if(i>=0){const e=s[i];Vv(e);const r=n.value[i],{collapsedSize:u=0,collapsible:d,minSize:c=0}=e.constraints;if(null!=r&&d){const e=wh({delta:yh(r,u)?c-u:u-r,layout:n.value,panelConstraints:s.map(e=>e.constraints),pivotIndices:Oh(t,o,a),trigger:"keyboard"});n.value!==e&&l(e)}}break}}};return e.addEventListener("keydown",u),()=>{e.removeEventListener("keydown",u)}});o(()=>{c.forEach(e=>e())})})}const Lh=100,Kh={getItem:e=>(Ph(Kh),Kh.getItem(e)),setItem:(e,t)=>{Ph(Kh),Kh.setItem(e,t)}},[jh,Gh]=Za("PanelGroup");Boolean,Boolean,Boolean;function Xh({disabled:e,handleId:t,resizeHandler:n,panelGroupElement:a}){(0,o.nT)(o=>{const r=a.value;if(e.value||null===n.value||null===r)return;const l=xh(t,r);if(null==l)return;const i=e=>{var o;if(!e.defaultPrevented)switch(e.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":e.preventDefault(),null==(o=n.value)||o.call(n,e);break;case"F6":{e.preventDefault();const n=l.getAttribute("data-panel-group-id");Vv(n);const o=_h(n,r),a=Bh(n,t,r);Vv(null!==a);const i=e.shiftKey?a>0?a-1:o.length-1:a+1<o.length?a+1:0;o[i].focus();break}}};l.addEventListener("keydown",i),o(()=>{l.removeEventListener("keydown",i)})})}Boolean,Boolean;const Uh={"aria-live":"polite","aria-atomic":"true",role:"status",style:{transform:"translateX(-100%)",position:"absolute",pointerEvents:"none",opacity:0,margin:0}},[zh,Hh]=Za("StepperRoot"),[Yh,Zh]=(Boolean,Boolean,Za("StepperItem")),qh=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,["name","disabled","required","value","checked","data-state","data-disabled"]),[Jh,Qh]=Za("SwitchRoot"),[em,tm]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("TabsRoot"));Boolean,Boolean,Boolean;function nm(e,t){return`${e}-trigger-${t}`}function om(e,t){return`${e}-content-${t}`}Boolean,Boolean,Boolean,Boolean,Boolean;const[am,rm]=Za("TagsInputRoot"),[lm,im]=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Za("TagsInputItem")),[sm,um]=(Boolean,Boolean,Boolean,Boolean,Boolean,Za("ToastProvider")),dm="toast.swipeStart",cm="toast.swipeMove",fm="toast.swipeCancel",pm="toast.swipeEnd",vm="toast.viewportPause",hm="toast.viewportResume";function mm(e,t,n){const o=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),o.dispatchEvent(a)}function ym(e,t,n=0){const o=Math.abs(e.x),a=Math.abs(e.y),r=o>a;return"left"===t||"right"===t?r&&o>n:!r&&a>n}function gm(e){return e.nodeType===e.ELEMENT_NODE}function bm(e){const t=[];return Array.from(e.childNodes).forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),gm(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,o=""===e.dataset.radixToastAnnounceExclude;if(!n)if(o){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...bm(e))}}),t}const wm=(0,o.pM)({__name:"ToastAnnounce",setup(e){const t=sm(),n=el(1e3),l=(0,a.KR)(!1);return cl(()=>{l.value=!0}),(e,i)=>(0,a.R1)(n)||l.value?((0,o.uX)(),(0,o.Wv)((0,a.R1)(Mu),{key:0},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)((0,a.R1)(t).label.value)+" ",1),(0,o.RG)(e.$slots,"default")]),_:3})):(0,o.Q3)("",!0)}}),[Rm,xm]=Za("ToastRoot"),Bm=(0,o.pM)({inheritAttrs:!1,__name:"ToastRootImpl",props:{type:{},open:{type:Boolean,default:!1},duration:{},asChild:{type:Boolean},as:{default:"li"}},emits:["close","escapeKeyDown","pause","resume","swipeStart","swipeMove","swipeCancel","swipeEnd"],setup(e,{emit:t}){const n=e,i=t,{forwardRef:s,currentElement:u}=Gl(),d=sm(),c=(0,a.KR)(null),f=(0,a.KR)(null),p=(0,o.EW)(()=>"number"==typeof n.duration?n.duration:d.duration.value),v=(0,a.KR)(0),h=(0,a.KR)(p.value),m=(0,a.KR)(0),y=(0,a.KR)(p.value),g=cl(()=>{const e=(new Date).getTime()-v.value;y.value=Math.max(h.value-e,0)},{fpsLimit:60});function b(e){e<=0||e===Number.POSITIVE_INFINITY||Pr&&(window.clearTimeout(m.value),v.value=(new Date).getTime(),m.value=window.setTimeout(w,e))}function w(){var e,t;(null==(e=u.value)?void 0:e.contains(yl()))&&(null==(t=d.viewport.value)||t.focus()),d.isClosePausedRef.value=!1,i("close")}const R=(0,o.EW)(()=>u.value?bm(u.value):null);if(n.type&&!["foreground","background"].includes(n.type)){const e="Invalid prop `type` supplied to `Toast`. Expected `foreground | background`.";throw new Error(e)}return(0,o.nT)(e=>{const t=d.viewport.value;if(t){const e=()=>{b(h.value),g.resume(),i("resume")},n=()=>{const e=(new Date).getTime()-v.value;h.value=h.value-e,window.clearTimeout(m.value),g.pause(),i("pause")};return t.addEventListener(vm,n),t.addEventListener(hm,e),()=>{t.removeEventListener(vm,n),t.removeEventListener(hm,e)}}}),(0,o.wB)(()=>[n.open,p.value],()=>{h.value=p.value,n.open&&!d.isClosePausedRef.value&&b(p.value)},{immediate:!0}),ll("Escape",e=>{i("escapeKeyDown",e),e.defaultPrevented||(d.isFocusedToastEscapeKeyDownRef.value=!0,w())}),(0,o.sV)(()=>{d.onToastAdd()}),(0,o.hi)(()=>{d.onToastRemove()}),xm({onClose:w}),(e,t)=>((0,o.uX)(),(0,o.CE)(o.FK,null,[R.value?((0,o.uX)(),(0,o.Wv)(wm,{key:0,role:"alert","aria-live":"foreground"===e.type?"assertive":"polite","aria-atomic":"true"},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(R.value),1)]),_:1},8,["aria-live"])):(0,o.Q3)("",!0),(0,a.R1)(d).viewport.value?((0,o.uX)(),(0,o.Wv)(o.Im,{key:1,to:(0,a.R1)(d).viewport.value},[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(s),role:"alert","aria-live":"off","aria-atomic":"true",tabindex:"0","data-radix-vue-collection-item":""},e.$attrs,{as:e.as,"as-child":e.asChild,"data-state":e.open?"open":"closed","data-swipe-direction":(0,a.R1)(d).swipeDirection.value,style:{userSelect:"none",touchAction:"none"},onPointerdown:t[0]||(t[0]=(0,l.D$)(e=>{c.value={x:e.clientX,y:e.clientY}},["left"])),onPointermove:t[1]||(t[1]=e=>{if(!c.value)return;const t=e.clientX-c.value.x,n=e.clientY-c.value.y,o=!!f.value,r=["left","right"].includes((0,a.R1)(d).swipeDirection.value),l=["left","up"].includes((0,a.R1)(d).swipeDirection.value)?Math.min:Math.max,s=r?l(0,t):0,u=r?0:l(0,n),p="touch"===e.pointerType?10:2,v={x:s,y:u},h={originalEvent:e,delta:v};o?(f.value=v,(0,a.R1)(mm)((0,a.R1)(cm),e=>i("swipeMove",e),h)):(0,a.R1)(ym)(v,(0,a.R1)(d).swipeDirection.value,p)?(f.value=v,(0,a.R1)(mm)((0,a.R1)(dm),e=>i("swipeStart",e),h),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>p||Math.abs(n)>p)&&(c.value=null)}),onPointerup:t[2]||(t[2]=e=>{const t=f.value,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),f.value=null,c.value=null,t){const n=e.currentTarget,o={originalEvent:e,delta:t};(0,a.R1)(ym)(t,(0,a.R1)(d).swipeDirection.value,(0,a.R1)(d).swipeThreshold.value)?(0,a.R1)(mm)((0,a.R1)(pm),e=>i("swipeEnd",e),o):(0,a.R1)(mm)((0,a.R1)(fm),e=>i("swipeCancel",e),o),null==n||n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{remaining:y.value,duration:p.value})]),_:3},16,["as","as-child","data-state","data-swipe-direction"])],8,["to"])):(0,o.Q3)("",!0)],64))}}),_m=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"ToastAnnounceExclude",props:{altText:{},asChild:{type:Boolean},as:{}},setup(e){return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{as:e.as,"as-child":e.asChild,"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":e.altText||void 0},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},8,["as","as-child","data-radix-toast-announce-alt"]))}})),Em=(0,o.pM)({__name:"ToastClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=Rm(),{forwardRef:r}=Gl();return(e,l)=>((0,o.uX)(),(0,o.Wv)(_m,{"as-child":""},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),(0,o.v6)(t,{ref:(0,a.R1)(r),type:"button"===e.as?"button":void 0,onClick:l[0]||(l[0]=e=>(0,a.R1)(n).onClose())}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["type"])]),_:3}))}}),Cm=(Boolean,(0,o.pM)({__name:"FocusProxy",emits:["focusFromOutsideViewport"],setup(e,{emit:t}){const n=t,r=sm();return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Mu),{"aria-hidden":"true",tabindex:"0",style:{position:"fixed"},onFocus:t[0]||(t[0]=e=>{var t;const o=e.relatedTarget;(null==(t=(0,a.R1)(r).viewport.value)||!t.contains(o))&&n("focusFromOutsideViewport")})},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3}))}})),km=(Boolean,Boolean,Boolean,(0,o.pM)({__name:"Toggle",props:{defaultValue:{type:Boolean},pressed:{type:Boolean,default:void 0},disabled:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"button"}},emits:["update:pressed"],setup(e,{emit:t}){const n=e,r=t;Gl();const l=hl(n,"pressed",r,{defaultValue:n.defaultValue,passive:void 0===n.pressed});function i(){l.value=!l.value}const s=(0,o.EW)(()=>l.value?"on":"off");return(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(bi),{type:"button"===e.as?"button":void 0,"as-child":n.asChild,as:e.as,"aria-pressed":(0,a.R1)(l),"data-state":s.value,"data-disabled":e.disabled?"":void 0,disabled:e.disabled,onClick:i},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{pressed:(0,a.R1)(l)})]),_:3},8,["type","as-child","as","aria-pressed","data-state","data-disabled","disabled"]))}})),[Mm,Sm]=Za("ToggleGroupRoot"),Dm=(0,o.pM)({__name:"ToggleGroupRoot",props:{rovingFocus:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},orientation:{},dir:{},loop:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,r=t,{loop:l,rovingFocus:i,disabled:s,dir:u}=(0,a.QW)(n),d=Wl(u),{forwardRef:c}=Gl(),{modelValue:f,changeModelValue:p,isSingle:v}=Oi(n,r);return Sm({isSingle:v,modelValue:f,changeModelValue:p,dir:d,orientation:n.orientation,loop:l,rovingFocus:i,disabled:s}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,o.$y)((0,a.R1)(i)?(0,a.R1)(fd):(0,a.R1)(bi)),{"as-child":"",orientation:(0,a.R1)(i)?e.orientation:void 0,dir:(0,a.R1)(d),loop:(0,a.R1)(i)?(0,a.R1)(l):void 0},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),{ref:(0,a.R1)(c),role:"group","as-child":e.asChild,as:e.as},{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default",{modelValue:(0,a.R1)(f)})]),_:3},8,["as-child","as"])]),_:3},8,["orientation","dir","loop"]))}}),Am=(0,o.pM)({__name:"ToggleGroupItem",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,n=Mm(),r=(0,o.EW)(()=>{var e;return(null==(e=n.disabled)?void 0:e.value)||t.disabled}),l=(0,o.EW)(()=>{var e;return null==(e=n.modelValue.value)?void 0:e.includes(t.value)}),i=(0,o.EW)(()=>{var e;return n.isSingle.value?n.modelValue.value===t.value:null==(e=n.modelValue.value)?void 0:e.includes(t.value)}),{forwardRef:s}=Gl();return(e,u)=>((0,o.uX)(),(0,o.Wv)((0,o.$y)((0,a.R1)(n).rovingFocus.value?(0,a.R1)(pd):(0,a.R1)(bi)),{"as-child":"",focusable:!r.value,active:l.value},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(km),(0,o.v6)(t,{ref:(0,a.R1)(s),disabled:r.value,pressed:i.value,"onUpdate:pressed":u[0]||(u[0]=t=>(0,a.R1)(n).changeModelValue(e.value))}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["disabled","pressed"])]),_:3},8,["focusable","active"]))}}),[Om,Tm]=Za("ToolbarRoot"),Pm=(Boolean,Boolean,(0,o.pM)({__name:"ToolbarButton",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,{forwardRef:n}=Gl();return(e,r)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(pd),{"as-child":"",focusable:!e.disabled},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(bi),(0,o.v6)({ref:(0,a.R1)(n),type:"button"===e.as?"button":void 0},t),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16,["type"])]),_:3},8,["focusable"]))}})),Im=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,"tooltip.open"),[Wm,Fm]=Za("TooltipProvider"),[$m,Nm]=(Boolean,Boolean,Boolean,Boolean,Za("TooltipRoot")),Vm=(Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,(0,o.pM)({__name:"TooltipContentImpl",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{default:0},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean,default:!0},collisionBoundary:{default:()=>[]},collisionPadding:{default:0},arrowPadding:{default:0},sticky:{default:"partial"},hideWhenDetached:{type:Boolean,default:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,i=t,s=$m(),{forwardRef:u}=Gl(),d=(0,o.Ht)(),c=(0,o.EW)(()=>{var e;return null==(e=d.default)?void 0:e.call(d)}),f=(0,o.EW)(()=>{var e;if(n.ariaLabel)return n.ariaLabel;let t="";function a(e){"string"==typeof e.children&&e.type!==o.Mw?t+=e.children:Array.isArray(e.children)&&e.children.forEach(e=>a(e))}return null==(e=c.value)||e.forEach(e=>a(e)),t}),p=(0,o.EW)(()=>{const{ariaLabel:e,...t}=n;return t});return(0,o.sV)(()=>{al(window,"scroll",e=>{const t=e.target;null!=t&&t.contains(s.trigger.value)&&s.onClose()}),al(window,Im,s.onClose)}),(e,t)=>((0,o.uX)(),(0,o.Wv)((0,a.R1)(Hi),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:t[0]||(t[0]=e=>i("escapeKeyDown",e)),onPointerDownOutside:t[1]||(t[1]=e=>{var t;(0,a.R1)(s).disableClosingTrigger.value&&null!=(t=(0,a.R1)(s).trigger.value)&&t.contains(e.target)&&e.preventDefault(),i("pointerDownOutside",e)}),onFocusOutside:t[2]||(t[2]=(0,l.D$)(()=>{},["prevent"])),onDismiss:t[3]||(t[3]=e=>(0,a.R1)(s).onClose())},{default:(0,o.k6)(()=>[(0,o.bF)((0,a.R1)(Bu),(0,o.v6)({ref:(0,a.R1)(u),"data-state":(0,a.R1)(s).stateAttribute.value},{...e.$attrs,...p.value},{style:{"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}}),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default"),(0,o.bF)((0,a.R1)(Mu),{id:(0,a.R1)(s).contentId,role:"tooltip"},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(f.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}})),Lm=(0,o.pM)({__name:"TooltipContentHoverable",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean}},setup(e){const t=Kl(e),{forwardRef:n,currentElement:r}=Gl(),{trigger:l,onClose:i}=$m(),s=Wm(),{isPointerInTransit:u,onPointerExit:d}=Xl(l,r);return s.isPointerInTransitRef=u,d(()=>{i()}),(e,r)=>((0,o.uX)(),(0,o.Wv)(Vm,(0,o.v6)({ref:(0,a.R1)(n)},(0,a.R1)(t)),{default:(0,o.k6)(()=>[(0,o.RG)(e.$slots,"default")]),_:3},16))}});Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;function Km(e){return e.reduce((e,t)=>(e.push(t),t.children&&e.push(...Km(t.children)),e),[])}const[jm,Gm]=Za("TreeRoot"),Xm=(Boolean,Boolean,Boolean,Boolean,"tree.select"),Um="tree.toggle";Boolean,Boolean},33:(e,t,n)=>{
/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function o(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}n.d(t,{$3:()=>p,$H:()=>F,BH:()=>X,BX:()=>oe,Bm:()=>R,C4:()=>q,CE:()=>h,CP:()=>u,DY:()=>$,Gv:()=>x,J$:()=>ee,Kg:()=>w,MZ:()=>a,Mp:()=>s,NO:()=>i,Oj:()=>r,PT:()=>O,Qd:()=>k,Ro:()=>L,SU:()=>S,TF:()=>c,Tg:()=>P,Tn:()=>b,Tr:()=>U,We:()=>j,X$:()=>d,Y2:()=>te,ZH:()=>I,Zf:()=>C,_B:()=>J,bB:()=>V,cy:()=>v,gd:()=>g,pD:()=>o,rU:()=>W,tE:()=>l,u3:()=>ae,vM:()=>m,v_:()=>le,yI:()=>M,yL:()=>B,yQ:()=>N});const a={},r=[],l=()=>{},i=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),d=Object.assign,c=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},f=Object.prototype.hasOwnProperty,p=(e,t)=>f.call(e,t),v=Array.isArray,h=e=>"[object Map]"===E(e),m=e=>"[object Set]"===E(e),y=e=>"[object Date]"===E(e),g=e=>"[object RegExp]"===E(e),b=e=>"function"===typeof e,w=e=>"string"===typeof e,R=e=>"symbol"===typeof e,x=e=>null!==e&&"object"===typeof e,B=e=>(x(e)||b(e))&&b(e.then)&&b(e.catch),_=Object.prototype.toString,E=e=>_.call(e),C=e=>E(e).slice(8,-1),k=e=>"[object Object]"===E(e),M=e=>w(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=o(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),D=e=>{const t=Object.create(null);return n=>{const o=t[n];return o||(t[n]=e(n))}},A=/-(\w)/g,O=D(e=>e.replace(A,(e,t)=>t?t.toUpperCase():"")),T=/\B([A-Z])/g,P=D(e=>e.replace(T,"-$1").toLowerCase()),I=D(e=>e.charAt(0).toUpperCase()+e.slice(1)),W=D(e=>{const t=e?`on${I(e)}`:"";return t}),F=(e,t)=>!Object.is(e,t),$=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},N=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},V=e=>{const t=parseFloat(e);return isNaN(t)?e:t},L=e=>{const t=w(e)?Number(e):NaN;return isNaN(t)?e:t};let K;const j=()=>K||(K="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{});const G="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",X=o(G);function U(e){if(v(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],a=w(o)?Z(o):U(o);if(a)for(const e in a)t[e]=a[e]}return t}if(w(e)||x(e))return e}const z=/;(?![^(]*\))/g,H=/:([^]+)/,Y=/\/\*[^]*?\*\//g;function Z(e){const t={};return e.replace(Y,"").split(z).forEach(e=>{if(e){const n=e.split(H);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function q(e){let t="";if(w(e))t=e;else if(v(e))for(let n=0;n<e.length;n++){const o=q(e[n]);o&&(t+=o+" ")}else if(x(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function J(e){if(!e)return null;let{class:t,style:n}=e;return t&&!w(t)&&(e.class=q(t)),n&&(e.style=U(n)),e}const Q="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ee=o(Q);function te(e){return!!e||""===e}function ne(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=oe(e[o],t[o]);return n}function oe(e,t){if(e===t)return!0;let n=y(e),o=y(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=R(e),o=R(t),n||o)return e===t;if(n=v(e),o=v(t),n||o)return!(!n||!o)&&ne(e,t);if(n=x(e),o=x(t),n||o){if(!n||!o)return!1;const a=Object.keys(e).length,r=Object.keys(t).length;if(a!==r)return!1;for(const n in e){const o=e.hasOwnProperty(n),a=t.hasOwnProperty(n);if(o&&!a||!o&&a||!oe(e[n],t[n]))return!1}}return String(e)===String(t)}function ae(e,t){return e.findIndex(e=>oe(e,t))}const re=e=>!(!e||!0!==e["__v_isRef"]),le=e=>w(e)?e:null==e?"":v(e)||x(e)&&(e.toString===_||!b(e.toString))?re(e)?le(e.value):JSON.stringify(e,ie,2):String(e),ie=(e,t)=>re(t)?ie(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],o)=>(e[se(t,o)+" =>"]=n,e),{})}:m(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>se(e))}:R(t)?se(t):!x(t)||v(t)||k(t)?t:String(t),se=(e,t="")=>{var n;return R(e)?`Symbol(${null!=(n=e.description)?n:t})`:e}},262:(e,t)=>{t.A=(e,t)=>{const n=e.__vccOpts||e;for(const[o,a]of t)n[o]=a;return n}},615:(e,t,n)=>{n.d(t,{Ey:()=>E,nY:()=>N});var o=n(953),a=!1;function r(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}var l=n(641);
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */
let i;const s=e=>i=e,u=Symbol();function d(e){return e&&"object"===typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!==typeof e.toJSON}var c;(function(e){e["direct"]="direct",e["patchObject"]="patch object",e["patchFunction"]="patch function"})(c||(c={}));const f="undefined"!==typeof window,p=(()=>"object"===typeof window&&window.window===window?window:"object"===typeof self&&self.self===self?self:"object"===typeof global&&global.global===global?global:"object"===typeof globalThis?globalThis:{HTMLElement:null})();function v(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function h(e,t,n){const o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){w(o.response,t,n)},o.onerror=function(){console.error("could not download file")},o.send()}function m(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(n){}return t.status>=200&&t.status<=299}function y(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}const g="object"===typeof navigator?navigator:{userAgent:""},b=(()=>/Macintosh/.test(g.userAgent)&&/AppleWebKit/.test(g.userAgent)&&!/Safari/.test(g.userAgent))(),w=f?"undefined"!==typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype&&!b?R:"msSaveOrOpenBlob"in g?x:B:()=>{};function R(e,t="download",n){const o=document.createElement("a");o.download=t,o.rel="noopener","string"===typeof e?(o.href=e,o.origin!==location.origin?m(o.href)?h(e,t,n):(o.target="_blank",y(o)):y(o)):(o.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(o.href)},4e4),setTimeout(function(){y(o)},0))}function x(e,t="download",n){if("string"===typeof e)if(m(e))h(e,t,n);else{const t=document.createElement("a");t.href=e,t.target="_blank",setTimeout(function(){y(t)})}else navigator.msSaveOrOpenBlob(v(e,n),t)}function B(e,t,n,o){if(o=o||open("","_blank"),o&&(o.document.title=o.document.body.innerText="downloading..."),"string"===typeof e)return h(e,t,n);const a="application/octet-stream"===e.type,r=/constructor/i.test(String(p.HTMLElement))||"safari"in p,l=/CriOS\/[\d]+/.test(navigator.userAgent);if((l||a&&r||b)&&"undefined"!==typeof FileReader){const t=new FileReader;t.onloadend=function(){let e=t.result;if("string"!==typeof e)throw o=null,new Error("Wrong reader.result type");e=l?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location.assign(e),o=null},t.readAsDataURL(e)}else{const t=URL.createObjectURL(e);o?o.location.assign(t):location.href=t,o=null,setTimeout(function(){URL.revokeObjectURL(t)},4e4)}}const{assign:_}=Object;function E(){const e=(0,o.uY)(!0),t=e.run(()=>(0,o.KR)({}));let n=[],r=[];const l=(0,o.IG)({install(e){s(l),a||(l._a=e,e.provide(u,l),e.config.globalProperties.$pinia=l,r.forEach(e=>n.push(e)),r=[])},use(e){return this._a||a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return l}const C=()=>{};function k(e,t,n,a=C){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),a())};return!n&&(0,o.o5)()&&(0,o.jr)(r),r}function M(e,...t){e.slice().forEach(e=>{e(...t)})}const S=e=>e(),D=Symbol(),A=Symbol();function O(e,t){e instanceof Map&&t instanceof Map?t.forEach((t,n)=>e.set(n,t)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const a=t[n],r=e[n];d(r)&&d(a)&&e.hasOwnProperty(n)&&!(0,o.i9)(a)&&!(0,o.g8)(a)?e[n]=O(r,a):e[n]=a}return e}const T=Symbol();function P(e){return!d(e)||!e.hasOwnProperty(T)}const{assign:I}=Object;function W(e){return!(!(0,o.i9)(e)||!e.effect)}function F(e,t,n,i){const{state:u,actions:d,getters:c}=t,f=n.state.value[e];let p;function v(){f||(a?r(n.state.value,e,u?u():{}):n.state.value[e]=u?u():{});const t=(0,o.QW)(n.state.value[e]);return I(t,d,Object.keys(c||{}).reduce((t,r)=>(t[r]=(0,o.IG)((0,l.EW)(()=>{s(n);const t=n._s.get(e);if(!a||t._r)return c[r].call(t,t)})),t),{}))}return p=$(e,v,t,n,i,!0),p}function $(e,t,n={},i,u,d){let f;const p=I({actions:{}},n);const v={deep:!0};let h,m;let y,g=[],b=[];const w=i.state.value[e];d||w||(a?r(i.state.value,e,{}):i.state.value[e]={});(0,o.KR)({});let R;function x(t){let n;h=m=!1,"function"===typeof t?(t(i.state.value[e]),n={type:c.patchFunction,storeId:e,events:y}):(O(i.state.value[e],t),n={type:c.patchObject,payload:t,storeId:e,events:y});const o=R=Symbol();(0,l.dY)().then(()=>{R===o&&(h=!0)}),m=!0,M(g,n,i.state.value[e])}const B=d?function(){const{state:e}=n,t=e?e():{};this.$patch(e=>{I(e,t)})}:C;function _(){f.stop(),g=[],b=[],i._s.delete(e)}const E=(t,n="")=>{if(D in t)return t[A]=n,t;const o=function(){s(i);const n=Array.from(arguments),a=[],r=[];function l(e){a.push(e)}function u(e){r.push(e)}let d;M(b,{args:n,name:o[A],store:F,after:l,onError:u});try{d=t.apply(this&&this.$id===e?this:F,n)}catch(c){throw M(r,c),c}return d instanceof Promise?d.then(e=>(M(a,e),e)).catch(e=>(M(r,e),Promise.reject(e))):(M(a,d),d)};return o[D]=!0,o[A]=n,o},T={_p:i,$id:e,$onAction:k.bind(null,b),$patch:x,$reset:B,$subscribe(t,n={}){const o=k(g,t,n.detached,()=>a()),a=f.run(()=>(0,l.wB)(()=>i.state.value[e],o=>{("sync"===n.flush?m:h)&&t({storeId:e,type:c.direct,events:y},o)},I({},v,n)));return o},$dispose:_};a&&(T._r=!1);const F=(0,o.Kh)(T);i._s.set(e,F);const $=i._a&&i._a.runWithContext||S,N=$(()=>i._e.run(()=>(f=(0,o.uY)()).run(()=>t({action:E}))));for(const l in N){const t=N[l];if((0,o.i9)(t)&&!W(t)||(0,o.g8)(t))d||(w&&P(t)&&((0,o.i9)(t)?t.value=w[l]:O(t,w[l])),a?r(i.state.value[e],l,t):i.state.value[e][l]=t);else if("function"===typeof t){const e=E(t,l);a?r(N,l,e):N[l]=e,p.actions[l]=t}else 0}return a?Object.keys(N).forEach(e=>{r(F,e,N[e])}):(I(F,N),I((0,o.ux)(F),N)),Object.defineProperty(F,"$state",{get:()=>i.state.value[e],set:e=>{x(t=>{I(t,e)})}}),a&&(F._r=!0),i._p.forEach(e=>{I(F,f.run(()=>e({store:F,app:i._a,pinia:i,options:p})))}),w&&d&&n.hydrate&&n.hydrate(F.$state,w),h=!0,m=!0,F}
/*! #__NO_SIDE_EFFECTS__ */function N(e,t,n){let o,a;const r="function"===typeof t;function d(e,n){const d=(0,l.PS)();e=e||(d?(0,l.WQ)(u,null):null),e&&s(e),e=i,e._s.has(o)||(r?$(o,t,a,e):F(o,a,e));const c=e._s.get(o);return c}return"string"===typeof e?(o=e,a=r?n:t):(a=e,o=e.id),d.$id=o,d}},641:(e,t,n)=>{n.d(t,{$u:()=>ye,$y:()=>Ce,Bi:()=>ee,CE:()=>yn,Df:()=>J,E3:()=>kn,EW:()=>ao,FK:()=>rn,Fv:()=>Sn,Gt:()=>lt,Gy:()=>U,HF:()=>Ve,Ht:()=>Fe,Ic:()=>me,Im:()=>L,K9:()=>St,KC:()=>ve,Lk:()=>Bn,MZ:()=>q,Mw:()=>sn,Ng:()=>Cn,OW:()=>Z,PS:()=>st,Q3:()=>Dn,QP:()=>H,RG:()=>De,Tb:()=>Oe,U_:()=>Kt,WQ:()=>it,Wv:()=>gn,bF:()=>_n,bo:()=>S,dY:()=>m,eW:()=>Mn,h:()=>ro,hi:()=>be,k6:()=>M,nI:()=>Vn,nT:()=>Vt,p9:()=>Lt,pI:()=>Se,pM:()=>Q,qL:()=>l,sV:()=>he,uX:()=>fn,v6:()=>Pn,wB:()=>jt,xo:()=>ge});var o=n(953),a=n(33);function r(e,t,n,o){try{return o?e(...o):e()}catch(a){i(a,t,n)}}function l(e,t,n,o){if((0,a.Tn)(e)){const l=r(e,t,n,o);return l&&(0,a.yL)(l)&&l.catch(e=>{i(e,t,n)}),l}if((0,a.cy)(e)){const a=[];for(let r=0;r<e.length;r++)a.push(l(e[r],t,n,o));return a}}function i(e,t,n,l=!0){const i=t?t.vnode:null,{errorHandler:u,throwUnhandledErrorInProduction:d}=t&&t.appContext.config||a.MZ;if(t){let a=t.parent;const l=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;while(a){const t=a.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,l,i))return;a=a.parent}if(u)return(0,o.C4)(),r(u,null,10,[e,l,i]),void(0,o.bl)()}s(e,n,i,l,d)}function s(e,t,n,o=!0,a=!1){if(a)throw e;console.error(e)}const u=[];let d=-1;const c=[];let f=null,p=0;const v=Promise.resolve();let h=null;function m(e){const t=h||v;return e?t.then(this?e.bind(this):e):t}function y(e){let t=d+1,n=u.length;while(t<n){const o=t+n>>>1,a=u[o],r=B(a);r<e||r===e&&2&a.flags?t=o+1:n=o}return t}function g(e){if(!(1&e.flags)){const t=B(e),n=u[u.length-1];!n||!(2&e.flags)&&t>=B(n)?u.push(e):u.splice(y(t),0,e),e.flags|=1,b()}}function b(){h||(h=v.then(_))}function w(e){(0,a.cy)(e)?c.push(...e):f&&-1===e.id?f.splice(p+1,0,e):1&e.flags||(c.push(e),e.flags|=1),b()}function R(e,t,n=d+1){for(0;n<u.length;n++){const t=u[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;0,u.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function x(e){if(c.length){const e=[...new Set(c)].sort((e,t)=>B(e)-B(t));if(c.length=0,f)return void f.push(...e);for(f=e,p=0;p<f.length;p++){const e=f[p];0,4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}f=null,p=0}}const B=e=>null==e.id?2&e.flags?-1:1/0:e.id;function _(e){a.tE;try{for(d=0;d<u.length;d++){const e=u[d];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),r(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;d<u.length;d++){const e=u[d];e&&(e.flags&=-2)}d=-1,u.length=0,x(e),h=null,(u.length||c.length)&&_(e)}}let E=null,C=null;function k(e){const t=E;return E=e,C=e&&e.type.__scopeId||null,t}function M(e,t=E,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&hn(-1);const a=k(t);let r;try{r=e(...n)}finally{k(a),o._d&&hn(1)}return r};return o._n=!0,o._c=!0,o._d=!0,o}function S(e,t){if(null===E)return e;const n=to(E),r=e.dirs||(e.dirs=[]);for(let l=0;l<t.length;l++){let[e,i,s,u=a.MZ]=t[l];e&&((0,a.Tn)(e)&&(e={mounted:e,updated:e}),e.deep&&(0,o.hV)(i),r.push({dir:e,instance:n,value:i,oldValue:void 0,arg:s,modifiers:u}))}return e}function D(e,t,n,a){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const u=r[s];i&&(u.oldValue=i[s].value);let d=u.dir[a];d&&((0,o.C4)(),l(d,n,8,[e.el,u,e,t]),(0,o.bl)())}}const A=Symbol("_vte"),O=e=>e.__isTeleport,T=e=>e&&(e.disabled||""===e.disabled),P=e=>e&&(e.defer||""===e.defer),I=e=>"undefined"!==typeof SVGElement&&e instanceof SVGElement,W=e=>"function"===typeof MathMLElement&&e instanceof MathMLElement,F=(e,t)=>{const n=e&&e.to;if((0,a.Kg)(n)){if(t){const e=t(n);return e}return null}return n},$={name:"Teleport",__isTeleport:!0,process(e,t,n,o,a,r,l,i,s,u){const{mc:d,pc:c,pbc:f,o:{insert:p,querySelector:v,createText:h,createComment:m}}=u,y=T(t.props);let{shapeFlag:g,children:b,dynamicChildren:w}=t;if(null==e){const e=t.el=h(""),u=t.anchor=h("");p(e,n,o),p(u,n,o);const c=(e,t)=>{16&g&&(a&&a.isCE&&(a.ce._teleportTarget=e),d(b,e,t,a,r,l,i,s))},f=()=>{const e=t.target=F(t.props,v),n=j(e,t,h,p);e&&("svg"!==l&&I(e)?l="svg":"mathml"!==l&&W(e)&&(l="mathml"),y||(c(e,n),K(t,!1)))};y&&(c(n,u),K(t,!0)),P(t.props)?(t.el.__isMounted=!1,Mt(()=>{f(),delete t.el.__isMounted},r)):f()}else{if(P(t.props)&&!1===e.el.__isMounted)return void Mt(()=>{$.process(e,t,n,o,a,r,l,i,s,u)},r);t.el=e.el,t.targetStart=e.targetStart;const d=t.anchor=e.anchor,p=t.target=e.target,h=t.targetAnchor=e.targetAnchor,m=T(e.props),g=m?n:p,b=m?d:h;if("svg"===l||I(p)?l="svg":("mathml"===l||W(p))&&(l="mathml"),w?(f(e.dynamicChildren,w,g,a,r,l,i),Pt(e,t,!0)):s||c(e,t,g,b,a,r,l,i,!1),y)m?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):N(t,n,d,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=F(t.props,v);e&&N(t,e,null,u,0)}else m&&N(t,p,h,u,1);K(t,y)}},remove(e,t,n,{um:o,o:{remove:a}},r){const{shapeFlag:l,children:i,anchor:s,targetStart:u,targetAnchor:d,target:c,props:f}=e;if(c&&(a(u),a(d)),r&&a(s),16&l){const e=r||!T(f);for(let a=0;a<i.length;a++){const r=i[a];o(r,t,n,e,!!r.dynamicChildren)}}},move:N,hydrate:V};function N(e,t,n,{o:{insert:o},m:a},r=2){0===r&&o(e.targetAnchor,t,n);const{el:l,anchor:i,shapeFlag:s,children:u,props:d}=e,c=2===r;if(c&&o(l,t,n),(!c||T(d))&&16&s)for(let f=0;f<u.length;f++)a(u[f],t,n,2);c&&o(i,t,n)}function V(e,t,n,o,a,r,{o:{nextSibling:l,parentNode:i,querySelector:s,insert:u,createText:d}},c){const f=t.target=F(t.props,s);if(f){const s=T(t.props),p=f._lpa||f.firstChild;if(16&t.shapeFlag)if(s)t.anchor=c(l(e),t,i(e),n,o,a,r),t.targetStart=p,t.targetAnchor=p&&l(p);else{t.anchor=l(e);let i=p;while(i){if(i&&8===i.nodeType)if("teleport start anchor"===i.data)t.targetStart=i;else if("teleport anchor"===i.data){t.targetAnchor=i,f._lpa=t.targetAnchor&&l(t.targetAnchor);break}i=l(i)}t.targetAnchor||j(f,t,d,u),c(p&&l(p),t,f,n,o,a,r)}K(t,s)}return t.anchor&&l(t.anchor)}const L=$;function K(e,t){const n=e.ctx;if(n&&n.ut){let o,a;t?(o=e.el,a=e.anchor):(o=e.targetStart,a=e.targetAnchor);while(o&&o!==a)1===o.nodeType&&o.setAttribute("data-v-owner",n.uid),o=o.nextSibling;n.ut()}}function j(e,t,n,o){const a=t.targetStart=n(""),r=t.targetAnchor=n("");return a[A]=r,e&&(o(a,e),o(r,e)),r}const G=Symbol("_leaveCb"),X=Symbol("_enterCb");function U(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return he(()=>{e.isMounted=!0}),ge(()=>{e.isUnmounting=!0}),e}const z=[Function,Array],H={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:z,onEnter:z,onAfterEnter:z,onEnterCancelled:z,onBeforeLeave:z,onLeave:z,onAfterLeave:z,onLeaveCancelled:z,onBeforeAppear:z,onAppear:z,onAfterAppear:z,onAppearCancelled:z};function Y(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Z(e,t,n,o,r){const{appear:i,mode:s,persisted:u=!1,onBeforeEnter:d,onEnter:c,onAfterEnter:f,onEnterCancelled:p,onBeforeLeave:v,onLeave:h,onAfterLeave:m,onLeaveCancelled:y,onBeforeAppear:g,onAppear:b,onAfterAppear:w,onAppearCancelled:R}=t,x=String(e.key),B=Y(n,e),_=(e,t)=>{e&&l(e,o,9,t)},E=(e,t)=>{const n=t[1];_(e,t),(0,a.cy)(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},C={mode:s,persisted:u,beforeEnter(t){let o=d;if(!n.isMounted){if(!i)return;o=g||d}t[G]&&t[G](!0);const a=B[x];a&&wn(e,a)&&a.el[G]&&a.el[G](),_(o,[t])},enter(e){let t=c,o=f,a=p;if(!n.isMounted){if(!i)return;t=b||c,o=w||f,a=R||p}let r=!1;const l=e[X]=t=>{r||(r=!0,_(t?a:o,[e]),C.delayedLeave&&C.delayedLeave(),e[X]=void 0)};t?E(t,[e,l]):l()},leave(t,o){const a=String(e.key);if(t[X]&&t[X](!0),n.isUnmounting)return o();_(v,[t]);let r=!1;const l=t[G]=n=>{r||(r=!0,o(),_(n?y:m,[t]),t[G]=void 0,B[a]===e&&delete B[a])};B[a]=e,h?E(h,[t,l]):l()},clone(e){const a=Z(e,t,n,o,r);return r&&r(a),a}};return C}function q(e,t){6&e.shapeFlag&&e.component?(e.transition=t,q(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function J(e,t=!1,n){let o=[],a=0;for(let r=0;r<e.length;r++){let l=e[r];const i=null==n?l.key:String(n)+String(null!=l.key?l.key:r);l.type===rn?(128&l.patchFlag&&a++,o=o.concat(J(l.children,t,i))):(t||l.type!==sn)&&o.push(null!=i?kn(l,{key:i}):l)}if(a>1)for(let r=0;r<o.length;r++)o[r].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function Q(e,t){return(0,a.Tn)(e)?(()=>(0,a.X$)({name:e.name},t,{setup:e}))():e}function ee(){const e=Vn();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function te(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ne(e,t,n,l,i=!1){if((0,a.cy)(e))return void e.forEach((e,o)=>ne(e,t&&((0,a.cy)(t)?t[o]:t),n,l,i));if(oe(l)&&!i)return void(512&l.shapeFlag&&l.type.__asyncResolved&&l.component.subTree.component&&ne(e,t,n,l.component.subTree));const s=4&l.shapeFlag?to(l.component):l.el,u=i?null:s,{i:d,r:c}=e;const f=t&&t.r,p=d.refs===a.MZ?d.refs={}:d.refs,v=d.setupState,h=(0,o.ux)(v),m=v===a.MZ?()=>!1:e=>(0,a.$3)(h,e);if(null!=f&&f!==c&&((0,a.Kg)(f)?(p[f]=null,m(f)&&(v[f]=null)):(0,o.i9)(f)&&(f.value=null)),(0,a.Tn)(c))r(c,d,12,[u,p]);else{const t=(0,a.Kg)(c),r=(0,o.i9)(c);if(t||r){const o=()=>{if(e.f){const n=t?m(c)?v[c]:p[c]:c.value;i?(0,a.cy)(n)&&(0,a.TF)(n,s):(0,a.cy)(n)?n.includes(s)||n.push(s):t?(p[c]=[s],m(c)&&(v[c]=p[c])):(c.value=[s],e.k&&(p[e.k]=c.value))}else t?(p[c]=u,m(c)&&(v[c]=u)):r&&(c.value=u,e.k&&(p[e.k]=u))};u?(o.id=-1,Mt(o,n)):o()}else 0}}(0,a.We)().requestIdleCallback,(0,a.We)().cancelIdleCallback;const oe=e=>!!e.type.__asyncLoader;
/*! #__NO_SIDE_EFFECTS__ */const ae=e=>e.type.__isKeepAlive;RegExp,RegExp;function re(e,t){return(0,a.cy)(e)?e.some(e=>re(e,t)):(0,a.Kg)(e)?e.split(",").includes(t):!!(0,a.gd)(e)&&(e.lastIndex=0,e.test(t))}function le(e,t){se(e,"a",t)}function ie(e,t){se(e,"da",t)}function se(e,t,n=Nn){const o=e.__wdc||(e.__wdc=()=>{let t=n;while(t){if(t.isDeactivated)return;t=t.parent}return e()});if(fe(t,o,n),n){let e=n.parent;while(e&&e.parent)ae(e.parent.vnode)&&ue(o,t,n,e),e=e.parent}}function ue(e,t,n,o){const r=fe(t,e,o,!0);be(()=>{(0,a.TF)(o[t],r)},n)}function de(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function ce(e){return 128&e.shapeFlag?e.ssContent:e}function fe(e,t,n=Nn,a=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...a)=>{(0,o.C4)();const r=jn(n),i=l(t,n,e,a);return r(),(0,o.bl)(),i});return a?r.unshift(i):r.push(i),i}}const pe=e=>(t,n=Nn)=>{Hn&&"sp"!==e||fe(e,(...e)=>t(...e),n)},ve=pe("bm"),he=pe("m"),me=pe("bu"),ye=pe("u"),ge=pe("bum"),be=pe("um"),we=pe("sp"),Re=pe("rtg"),xe=pe("rtc");function Be(e,t=Nn){fe("ec",e,t)}const _e="components";const Ee=Symbol.for("v-ndc");function Ce(e){return(0,a.Kg)(e)?ke(_e,e,!1)||e:e||Ee}function ke(e,t,n=!0,o=!1){const r=E||Nn;if(r){const n=r.type;if(e===_e){const e=no(n,!1);if(e&&(e===t||e===(0,a.PT)(t)||e===(0,a.ZH)((0,a.PT)(t))))return n}const l=Me(r[e]||n[e],t)||Me(r.appContext[e],t);return!l&&o?n:l}}function Me(e,t){return e&&(e[t]||e[(0,a.PT)(t)]||e[(0,a.ZH)((0,a.PT)(t))])}function Se(e,t,n,r){let l;const i=n&&n[r],s=(0,a.cy)(e);if(s||(0,a.Kg)(e)){const n=s&&(0,o.g8)(e);let a=!1,r=!1;n&&(a=!(0,o.fE)(e),r=(0,o.Tm)(e),e=(0,o.qA)(e)),l=new Array(e.length);for(let s=0,u=e.length;s<u;s++)l[s]=t(a?r?(0,o.a1)((0,o.lJ)(e[s])):(0,o.lJ)(e[s]):e[s],s,void 0,i&&i[s])}else if("number"===typeof e){0,l=new Array(e);for(let n=0;n<e;n++)l[n]=t(n+1,n,void 0,i&&i[n])}else if((0,a.Gv)(e))if(e[Symbol.iterator])l=Array.from(e,(e,n)=>t(e,n,void 0,i&&i[n]));else{const n=Object.keys(e);l=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];l[o]=t(e[a],a,o,i&&i[o])}}else l=[];return n&&(n[r]=l),l}function De(e,t,n={},o,r){if(E.ce||E.parent&&oe(E.parent)&&E.parent.ce)return"default"!==t&&(n.name=t),fn(),gn(rn,null,[_n("slot",n,o&&o())],64);let l=e[t];l&&l._c&&(l._d=!1),fn();const i=l&&Ae(l(n)),s=n.key||i&&i.key,u=gn(rn,{key:(s&&!(0,a.Bm)(s)?s:`_${t}`)+(!i&&o?"_fb":"")},i||(o?o():[]),i&&1===e._?64:-2);return!r&&u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),l&&l._c&&(l._d=!0),u}function Ae(e){return e.some(e=>!bn(e)||e.type!==sn&&!(e.type===rn&&!Ae(e.children)))?e:null}function Oe(e,t){const n={};for(const o in e)n[t&&/[A-Z]/.test(o)?`on:${o}`:(0,a.rU)(o)]=e[o];return n}const Te=e=>e?Xn(e)?to(e):Te(e.parent):null,Pe=(0,a.X$)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Te(e.parent),$root:e=>Te(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ue(e),$forceUpdate:e=>e.f||(e.f=()=>{g(e.update)}),$nextTick:e=>e.n||(e.n=m.bind(e.proxy)),$watch:e=>Xt.bind(e)}),Ie=(e,t)=>e!==a.MZ&&!e.__isScriptSetup&&(0,a.$3)(e,t),We={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:n,setupState:r,data:l,props:i,accessCache:s,type:u,appContext:d}=e;let c;if("$"!==t[0]){const o=s[t];if(void 0!==o)switch(o){case 1:return r[t];case 2:return l[t];case 4:return n[t];case 3:return i[t]}else{if(Ie(r,t))return s[t]=1,r[t];if(l!==a.MZ&&(0,a.$3)(l,t))return s[t]=2,l[t];if((c=e.propsOptions[0])&&(0,a.$3)(c,t))return s[t]=3,i[t];if(n!==a.MZ&&(0,a.$3)(n,t))return s[t]=4,n[t];Le&&(s[t]=0)}}const f=Pe[t];let p,v;return f?("$attrs"===t&&(0,o.u4)(e.attrs,"get",""),f(e)):(p=u.__cssModules)&&(p=p[t])?p:n!==a.MZ&&(0,a.$3)(n,t)?(s[t]=4,n[t]):(v=d.config.globalProperties,(0,a.$3)(v,t)?v[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:l}=e;return Ie(r,t)?(r[t]=n,!0):o!==a.MZ&&(0,a.$3)(o,t)?(o[t]=n,!0):!(0,a.$3)(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(l[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:l}},i){let s;return!!n[i]||e!==a.MZ&&(0,a.$3)(e,i)||Ie(t,i)||(s=l[0])&&(0,a.$3)(s,i)||(0,a.$3)(o,i)||(0,a.$3)(Pe,i)||(0,a.$3)(r.config.globalProperties,i)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:(0,a.$3)(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Fe(){return $e().slots}function $e(){const e=Vn();return e.setupContext||(e.setupContext=eo(e))}function Ne(e){return(0,a.cy)(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}function Ve(e,t){const n=Ne(e);for(const o in t){if(o.startsWith("__skip"))continue;let e=n[o];e?(0,a.cy)(e)||(0,a.Tn)(e)?e=n[o]={type:e,default:t[o]}:e.default=t[o]:null===e&&(e=n[o]={default:t[o]}),e&&t[`__skip_${o}`]&&(e.skipFactory=!0)}return n}let Le=!0;function Ke(e){const t=Ue(e),n=e.proxy,r=e.ctx;Le=!1,t.beforeCreate&&Ge(t.beforeCreate,e,"bc");const{data:l,computed:i,methods:s,watch:u,provide:d,inject:c,created:f,beforeMount:p,mounted:v,beforeUpdate:h,updated:m,activated:y,deactivated:g,beforeDestroy:b,beforeUnmount:w,destroyed:R,unmounted:x,render:B,renderTracked:_,renderTriggered:E,errorCaptured:C,serverPrefetch:k,expose:M,inheritAttrs:S,components:D,directives:A,filters:O}=t,T=null;if(c&&je(c,r,T),s)for(const o in s){const e=s[o];(0,a.Tn)(e)&&(r[o]=e.bind(n))}if(l){0;const t=l.call(n,n);0,(0,a.Gv)(t)&&(e.data=(0,o.Kh)(t))}if(Le=!0,i)for(const o in i){const e=i[o],t=(0,a.Tn)(e)?e.bind(n,n):(0,a.Tn)(e.get)?e.get.bind(n,n):a.tE;0;const l=!(0,a.Tn)(e)&&(0,a.Tn)(e.set)?e.set.bind(n):a.tE,s=ao({get:t,set:l});Object.defineProperty(r,o,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(u)for(const o in u)Xe(u[o],r,n,o);if(d){const e=(0,a.Tn)(d)?d.call(n):d;Reflect.ownKeys(e).forEach(t=>{lt(t,e[t])})}function P(e,t){(0,a.cy)(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(f&&Ge(f,e,"c"),P(ve,p),P(he,v),P(me,h),P(ye,m),P(le,y),P(ie,g),P(Be,C),P(xe,_),P(Re,E),P(ge,w),P(be,x),P(we,k),(0,a.cy)(M))if(M.length){const t=e.exposed||(e.exposed={});M.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});B&&e.render===a.tE&&(e.render=B),null!=S&&(e.inheritAttrs=S),D&&(e.components=D),A&&(e.directives=A),k&&te(e)}function je(e,t,n=a.tE){(0,a.cy)(e)&&(e=qe(e));for(const r in e){const n=e[r];let l;l=(0,a.Gv)(n)?"default"in n?it(n.from||r,n.default,!0):it(n.from||r):it(n),(0,o.i9)(l)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e}):t[r]=l}}function Ge(e,t,n){l((0,a.cy)(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function Xe(e,t,n,o){let r=o.includes(".")?Ut(n,o):()=>n[o];if((0,a.Kg)(e)){const n=t[e];(0,a.Tn)(n)&&jt(r,n)}else if((0,a.Tn)(e))jt(r,e.bind(n));else if((0,a.Gv)(e))if((0,a.cy)(e))e.forEach(e=>Xe(e,t,n,o));else{const o=(0,a.Tn)(e.handler)?e.handler.bind(n):t[e.handler];(0,a.Tn)(o)&&jt(r,o,e)}else 0}function Ue(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:l,config:{optionMergeStrategies:i}}=e.appContext,s=l.get(t);let u;return s?u=s:r.length||n||o?(u={},r.length&&r.forEach(e=>ze(u,e,i,!0)),ze(u,t,i)):u=t,(0,a.Gv)(t)&&l.set(t,u),u}function ze(e,t,n,o=!1){const{mixins:a,extends:r}=t;r&&ze(e,r,n,!0),a&&a.forEach(t=>ze(e,t,n,!0));for(const l in t)if(o&&"expose"===l);else{const o=He[l]||n&&n[l];e[l]=o?o(e[l],t[l]):t[l]}return e}const He={data:Ye,props:et,emits:et,methods:Qe,computed:Qe,beforeCreate:Je,created:Je,beforeMount:Je,mounted:Je,beforeUpdate:Je,updated:Je,beforeDestroy:Je,beforeUnmount:Je,destroyed:Je,unmounted:Je,activated:Je,deactivated:Je,errorCaptured:Je,serverPrefetch:Je,components:Qe,directives:Qe,watch:tt,provide:Ye,inject:Ze};function Ye(e,t){return t?e?function(){return(0,a.X$)((0,a.Tn)(e)?e.call(this,this):e,(0,a.Tn)(t)?t.call(this,this):t)}:t:e}function Ze(e,t){return Qe(qe(e),qe(t))}function qe(e){if((0,a.cy)(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Je(e,t){return e?[...new Set([].concat(e,t))]:t}function Qe(e,t){return e?(0,a.X$)(Object.create(null),e,t):t}function et(e,t){return e?(0,a.cy)(e)&&(0,a.cy)(t)?[...new Set([...e,...t])]:(0,a.X$)(Object.create(null),Ne(e),Ne(null!=t?t:{})):t}function tt(e,t){if(!e)return t;if(!t)return e;const n=(0,a.X$)(Object.create(null),e);for(const o in t)n[o]=Je(e[o],t[o]);return n}function nt(){return{app:null,config:{isNativeTag:a.NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ot=0;function at(e,t){return function(n,o=null){(0,a.Tn)(n)||(n=(0,a.X$)({},n)),null==o||(0,a.Gv)(o)||(o=null);const r=nt(),i=new WeakSet,s=[];let u=!1;const d=r.app={_uid:ot++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:lo,get config(){return r.config},set config(e){0},use(e,...t){return i.has(e)||(e&&(0,a.Tn)(e.install)?(i.add(e),e.install(d,...t)):(0,a.Tn)(e)&&(i.add(e),e(d,...t))),d},mixin(e){return r.mixins.includes(e)||r.mixins.push(e),d},component(e,t){return t?(r.components[e]=t,d):r.components[e]},directive(e,t){return t?(r.directives[e]=t,d):r.directives[e]},mount(a,l,i){if(!u){0;const s=d._ceVNode||_n(n,o);return s.appContext=r,!0===i?i="svg":!1===i&&(i=void 0),l&&t?t(s,a):e(s,a,i),u=!0,d._container=a,a.__vue_app__=d,to(s.component)}},onUnmount(e){s.push(e)},unmount(){u&&(l(s,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(e,t){return r.provides[e]=t,d},runWithContext(e){const t=rt;rt=d;try{return e()}finally{rt=t}}};return d}}let rt=null;function lt(e,t){if(Nn){let n=Nn.provides;const o=Nn.parent&&Nn.parent.provides;o===n&&(n=Nn.provides=Object.create(o)),n[e]=t}else 0}function it(e,t,n=!1){const o=Nn||E;if(o||rt){let r=rt?rt._context.provides:o?null==o.parent||o.ce?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&(0,a.Tn)(t)?t.call(o&&o.proxy):t}else 0}function st(){return!!(Nn||E||rt)}const ut={},dt=()=>Object.create(ut),ct=e=>Object.getPrototypeOf(e)===ut;function ft(e,t,n,a=!1){const r={},l=dt();e.propsDefaults=Object.create(null),vt(e,t,r,l);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=a?r:(0,o.Gc)(r):e.type.props?e.props=r:e.props=l,e.attrs=l}function pt(e,t,n,r){const{props:l,attrs:i,vnode:{patchFlag:s}}=e,u=(0,o.ux)(l),[d]=e.propsOptions;let c=!1;if(!(r||s>0)||16&s){let o;vt(e,t,l,i)&&(c=!0);for(const r in u)t&&((0,a.$3)(t,r)||(o=(0,a.Tg)(r))!==r&&(0,a.$3)(t,o))||(d?!n||void 0===n[r]&&void 0===n[o]||(l[r]=ht(d,u,r,void 0,e,!0)):delete l[r]);if(i!==u)for(const e in i)t&&(0,a.$3)(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let r=n[o];if(Zt(e.emitsOptions,r))continue;const s=t[r];if(d)if((0,a.$3)(i,r))s!==i[r]&&(i[r]=s,c=!0);else{const t=(0,a.PT)(r);l[t]=ht(d,u,t,s,e,!1)}else s!==i[r]&&(i[r]=s,c=!0)}}c&&(0,o.hZ)(e.attrs,"set","")}function vt(e,t,n,r){const[l,i]=e.propsOptions;let s,u=!1;if(t)for(let o in t){if((0,a.SU)(o))continue;const d=t[o];let c;l&&(0,a.$3)(l,c=(0,a.PT)(o))?i&&i.includes(c)?(s||(s={}))[c]=d:n[c]=d:Zt(e.emitsOptions,o)||o in r&&d===r[o]||(r[o]=d,u=!0)}if(i){const t=(0,o.ux)(n),r=s||a.MZ;for(let o=0;o<i.length;o++){const s=i[o];n[s]=ht(l,t,s,r[s],e,!(0,a.$3)(r,s))}}return u}function ht(e,t,n,o,r,l){const i=e[n];if(null!=i){const e=(0,a.$3)(i,"default");if(e&&void 0===o){const e=i.default;if(i.type!==Function&&!i.skipFactory&&(0,a.Tn)(e)){const{propsDefaults:a}=r;if(n in a)o=a[n];else{const l=jn(r);o=a[n]=e.call(null,t),l()}}else o=e;r.ce&&r.ce._setProp(n,o)}i[0]&&(l&&!e?o=!1:!i[1]||""!==o&&o!==(0,a.Tg)(n)||(o=!0))}return o}const mt=new WeakMap;function yt(e,t,n=!1){const o=n?mt:t.propsCache,r=o.get(e);if(r)return r;const l=e.props,i={},s=[];let u=!1;if(!(0,a.Tn)(e)){const o=e=>{u=!0;const[n,o]=yt(e,t,!0);(0,a.X$)(i,n),o&&s.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!l&&!u)return(0,a.Gv)(e)&&o.set(e,a.Oj),a.Oj;if((0,a.cy)(l))for(let c=0;c<l.length;c++){0;const e=(0,a.PT)(l[c]);gt(e)&&(i[e]=a.MZ)}else if(l){0;for(const e in l){const t=(0,a.PT)(e);if(gt(t)){const n=l[e],o=i[t]=(0,a.cy)(n)||(0,a.Tn)(n)?{type:n}:(0,a.X$)({},n),r=o.type;let u=!1,d=!0;if((0,a.cy)(r))for(let e=0;e<r.length;++e){const t=r[e],n=(0,a.Tn)(t)&&t.name;if("Boolean"===n){u=!0;break}"String"===n&&(d=!1)}else u=(0,a.Tn)(r)&&"Boolean"===r.name;o[0]=u,o[1]=d,(u||(0,a.$3)(o,"default"))&&s.push(t)}}}const d=[i,s];return(0,a.Gv)(e)&&o.set(e,d),d}function gt(e){return"$"!==e[0]&&!(0,a.SU)(e)}const bt=e=>"_"===e[0]||"$stable"===e,wt=e=>(0,a.cy)(e)?e.map(An):[An(e)],Rt=(e,t,n)=>{if(t._n)return t;const o=M((...e)=>wt(t(...e)),n);return o._c=!1,o},xt=(e,t,n)=>{const o=e._ctx;for(const r in e){if(bt(r))continue;const n=e[r];if((0,a.Tn)(n))t[r]=Rt(r,n,o);else if(null!=n){0;const e=wt(n);t[r]=()=>e}}},Bt=(e,t)=>{const n=wt(t);e.slots.default=()=>n},_t=(e,t,n)=>{for(const o in t)!n&&bt(o)||(e[o]=t[o])},Et=(e,t,n)=>{const o=e.slots=dt();if(32&e.vnode.shapeFlag){const e=t.__;e&&(0,a.yQ)(o,"__",e,!0);const r=t._;r?(_t(o,t,n),n&&(0,a.yQ)(o,"_",r,!0)):xt(t,o)}else t&&Bt(e,t)},Ct=(e,t,n)=>{const{vnode:o,slots:r}=e;let l=!0,i=a.MZ;if(32&o.shapeFlag){const e=t._;e?n&&1===e?l=!1:_t(r,t,n):(l=!t.$stable,xt(t,r)),i=t}else t&&(Bt(e,t),i={default:1});if(l)for(const a in r)bt(a)||null!=i[a]||delete r[a]};function kt(){}const Mt=an;function St(e){return Dt(e)}function Dt(e,t){kt();const n=(0,a.We)();n.__VUE__=!0;const{insert:r,remove:l,patchProp:i,createElement:s,createText:u,createComment:d,setText:c,setElementText:f,parentNode:p,nextSibling:v,setScopeId:h=a.tE,insertStaticContent:m}=e,y=(e,t,n,o=null,a=null,r=null,l=void 0,i=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!wn(e,t)&&(o=Z(e),X(e,a,r,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);const{type:u,ref:d,shapeFlag:c}=t;switch(u){case ln:b(e,t,n,o);break;case sn:w(e,t,n,o);break;case un:null==e&&B(t,n,o,l);break;case rn:I(e,t,n,o,a,r,l,i,s);break;default:1&c?C(e,t,n,o,a,r,l,i,s):6&c?W(e,t,n,o,a,r,l,i,s):(64&c||128&c)&&u.process(e,t,n,o,a,r,l,i,s,Q)}null!=d&&a?ne(d,e&&e.ref,r,t||e,!t):null==d&&e&&null!=e.ref&&ne(e.ref,null,r,e,!0)},b=(e,t,n,o)=>{if(null==e)r(t.el=u(t.children),n,o);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},w=(e,t,n,o)=>{null==e?r(t.el=d(t.children||""),n,o):t.el=e.el},B=(e,t,n,o)=>{[e.el,e.anchor]=m(e.children,t,n,o,e.el,e.anchor)},_=({el:e,anchor:t},n,o)=>{let a;while(e&&e!==t)a=v(e),r(e,n,o),e=a;r(t,n,o)},E=({el:e,anchor:t})=>{let n;while(e&&e!==t)n=v(e),l(e),e=n;l(t)},C=(e,t,n,o,a,r,l,i,s)=>{"svg"===t.type?l="svg":"math"===t.type&&(l="mathml"),null==e?k(t,n,o,a,r,l,i,s):O(e,t,a,r,l,i,s)},k=(e,t,n,o,l,u,d,c)=>{let p,v;const{props:h,shapeFlag:m,transition:y,dirs:g}=e;if(p=e.el=s(e.type,u,h&&h.is,h),8&m?f(p,e.children):16&m&&S(e.children,p,null,o,l,At(e,u),d,c),g&&D(e,null,o,"created"),M(p,e,e.scopeId,d,o),h){for(const e in h)"value"===e||(0,a.SU)(e)||i(p,e,null,h[e],u,o);"value"in h&&i(p,"value",null,h.value,u),(v=h.onVnodeBeforeMount)&&In(v,o,e)}g&&D(e,null,o,"beforeMount");const b=Tt(l,y);b&&y.beforeEnter(p),r(p,t,n),((v=h&&h.onVnodeMounted)||b||g)&&Mt(()=>{v&&In(v,o,e),b&&y.enter(p),g&&D(e,null,o,"mounted")},l)},M=(e,t,n,o,a)=>{if(n&&h(e,n),o)for(let r=0;r<o.length;r++)h(e,o[r]);if(a){let n=a.subTree;if(t===n||on(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=a.vnode;M(e,t,t.scopeId,t.slotScopeIds,a.parent)}}},S=(e,t,n,o,a,r,l,i,s=0)=>{for(let u=s;u<e.length;u++){const s=e[u]=i?On(e[u]):An(e[u]);y(null,s,t,n,o,a,r,l,i)}},O=(e,t,n,o,r,l,s)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:c,dirs:p}=t;d|=16&e.patchFlag;const v=e.props||a.MZ,h=t.props||a.MZ;let m;if(n&&Ot(n,!1),(m=h.onVnodeBeforeUpdate)&&In(m,n,t,e),p&&D(t,e,n,"beforeUpdate"),n&&Ot(n,!0),(v.innerHTML&&null==h.innerHTML||v.textContent&&null==h.textContent)&&f(u,""),c?T(e.dynamicChildren,c,u,n,o,At(t,r),l):s||L(e,t,u,null,n,o,At(t,r),l,!1),d>0){if(16&d)P(u,v,h,n,r);else if(2&d&&v.class!==h.class&&i(u,"class",null,h.class,r),4&d&&i(u,"style",v.style,h.style,r),8&d){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const o=e[t],a=v[o],l=h[o];l===a&&"value"!==o||i(u,o,a,l,r,n)}}1&d&&e.children!==t.children&&f(u,t.children)}else s||null!=c||P(u,v,h,n,r);((m=h.onVnodeUpdated)||p)&&Mt(()=>{m&&In(m,n,t,e),p&&D(t,e,n,"updated")},o)},T=(e,t,n,o,a,r,l)=>{for(let i=0;i<t.length;i++){const s=e[i],u=t[i],d=s.el&&(s.type===rn||!wn(s,u)||198&s.shapeFlag)?p(s.el):n;y(s,u,d,null,o,a,r,l,!0)}},P=(e,t,n,o,r)=>{if(t!==n){if(t!==a.MZ)for(const l in t)(0,a.SU)(l)||l in n||i(e,l,t[l],null,r,o);for(const l in n){if((0,a.SU)(l))continue;const s=n[l],u=t[l];s!==u&&"value"!==l&&i(e,l,u,s,r,o)}"value"in n&&i(e,"value",t.value,n.value,r)}},I=(e,t,n,o,a,l,i,s,d)=>{const c=t.el=e?e.el:u(""),f=t.anchor=e?e.anchor:u("");let{patchFlag:p,dynamicChildren:v,slotScopeIds:h}=t;h&&(s=s?s.concat(h):h),null==e?(r(c,n,o),r(f,n,o),S(t.children||[],n,f,a,l,i,s,d)):p>0&&64&p&&v&&e.dynamicChildren?(T(e.dynamicChildren,v,n,a,l,i,s),(null!=t.key||a&&t===a.subTree)&&Pt(e,t,!0)):L(e,t,n,f,a,l,i,s,d)},W=(e,t,n,o,a,r,l,i,s)=>{t.slotScopeIds=i,null==e?512&t.shapeFlag?a.ctx.activate(t,n,o,l,s):F(t,n,o,a,r,l,s):$(e,t,s)},F=(e,t,n,o,a,r,l)=>{const i=e.component=$n(e,o,a);if(ae(e)&&(i.ctx.renderer=Q),Yn(i,!1,l),i.asyncDep){if(a&&a.registerDep(i,N,l),!e.el){const e=i.subTree=_n(sn);w(null,e,t,n)}}else N(i,e,t,n,a,r,l)},$=(e,t,n)=>{const o=t.component=e.component;if(en(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void V(o,t,n);o.next=t,o.update()}else t.el=e.el,o.vnode=t},N=(e,t,n,r,l,i,s)=>{const u=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:r,vnode:d}=e;{const n=Wt(e);if(n)return t&&(t.el=d.el,V(e,t,s)),void n.asyncDep.then(()=>{e.isUnmounted||u()})}let c,f=t;0,Ot(e,!1),t?(t.el=d.el,V(e,t,s)):t=d,n&&(0,a.DY)(n),(c=t.props&&t.props.onVnodeBeforeUpdate)&&In(c,r,t,d),Ot(e,!0);const v=qt(e);0;const h=e.subTree;e.subTree=v,y(h,v,p(h.el),Z(h),e,l,i),t.el=v.el,null===f&&nn(e,v.el),o&&Mt(o,l),(c=t.props&&t.props.onVnodeUpdated)&&Mt(()=>In(c,r,t,d),l)}else{let o;const{el:s,props:u}=t,{bm:d,m:c,parent:f,root:p,type:v}=e,h=oe(t);if(Ot(e,!1),d&&(0,a.DY)(d),!h&&(o=u&&u.onVnodeBeforeMount)&&In(o,f,t),Ot(e,!0),s&&te){const t=()=>{e.subTree=qt(e),te(s,e.subTree,e,l,null)};h&&v.__asyncHydrate?v.__asyncHydrate(s,e,t):t()}else{p.ce&&!1!==p.ce._def.shadowRoot&&p.ce._injectChildStyle(v);const o=e.subTree=qt(e);0,y(null,o,n,r,e,l,i),t.el=o.el}if(c&&Mt(c,l),!h&&(o=u&&u.onVnodeMounted)){const e=t;Mt(()=>In(o,f,e),l)}(256&t.shapeFlag||f&&oe(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&Mt(e.a,l),e.isMounted=!0,t=n=r=null}};e.scope.on();const d=e.effect=new o.X2(u);e.scope.off();const c=e.update=d.run.bind(d),f=e.job=d.runIfDirty.bind(d);f.i=e,f.id=e.uid,d.scheduler=()=>g(f),Ot(e,!0),c()},V=(e,t,n)=>{t.component=e;const a=e.vnode.props;e.vnode=t,e.next=null,pt(e,t.props,a,n),Ct(e,t.children,n),(0,o.C4)(),R(e),(0,o.bl)()},L=(e,t,n,o,a,r,l,i,s=!1)=>{const u=e&&e.children,d=e?e.shapeFlag:0,c=t.children,{patchFlag:p,shapeFlag:v}=t;if(p>0){if(128&p)return void j(u,c,n,o,a,r,l,i,s);if(256&p)return void K(u,c,n,o,a,r,l,i,s)}8&v?(16&d&&Y(u,a,r),c!==u&&f(n,c)):16&d?16&v?j(u,c,n,o,a,r,l,i,s):Y(u,a,r,!0):(8&d&&f(n,""),16&v&&S(c,n,o,a,r,l,i,s))},K=(e,t,n,o,r,l,i,s,u)=>{e=e||a.Oj,t=t||a.Oj;const d=e.length,c=t.length,f=Math.min(d,c);let p;for(p=0;p<f;p++){const o=t[p]=u?On(t[p]):An(t[p]);y(e[p],o,n,null,r,l,i,s,u)}d>c?Y(e,r,l,!0,!1,f):S(t,n,o,r,l,i,s,u,f)},j=(e,t,n,o,r,l,i,s,u)=>{let d=0;const c=t.length;let f=e.length-1,p=c-1;while(d<=f&&d<=p){const o=e[d],a=t[d]=u?On(t[d]):An(t[d]);if(!wn(o,a))break;y(o,a,n,null,r,l,i,s,u),d++}while(d<=f&&d<=p){const o=e[f],a=t[p]=u?On(t[p]):An(t[p]);if(!wn(o,a))break;y(o,a,n,null,r,l,i,s,u),f--,p--}if(d>f){if(d<=p){const e=p+1,a=e<c?t[e].el:o;while(d<=p)y(null,t[d]=u?On(t[d]):An(t[d]),n,a,r,l,i,s,u),d++}}else if(d>p)while(d<=f)X(e[d],r,l,!0),d++;else{const v=d,h=d,m=new Map;for(d=h;d<=p;d++){const e=t[d]=u?On(t[d]):An(t[d]);null!=e.key&&m.set(e.key,d)}let g,b=0;const w=p-h+1;let R=!1,x=0;const B=new Array(w);for(d=0;d<w;d++)B[d]=0;for(d=v;d<=f;d++){const o=e[d];if(b>=w){X(o,r,l,!0);continue}let a;if(null!=o.key)a=m.get(o.key);else for(g=h;g<=p;g++)if(0===B[g-h]&&wn(o,t[g])){a=g;break}void 0===a?X(o,r,l,!0):(B[a-h]=d+1,a>=x?x=a:R=!0,y(o,t[a],n,null,r,l,i,s,u),b++)}const _=R?It(B):a.Oj;for(g=_.length-1,d=w-1;d>=0;d--){const e=h+d,a=t[e],f=e+1<c?t[e+1].el:o;0===B[d]?y(null,a,n,f,r,l,i,s,u):R&&(g<0||d!==_[g]?G(a,n,f,2):g--)}}},G=(e,t,n,o,a=null)=>{const{el:i,type:s,transition:u,children:d,shapeFlag:c}=e;if(6&c)return void G(e.component.subTree,t,n,o);if(128&c)return void e.suspense.move(t,n,o);if(64&c)return void s.move(e,t,n,Q);if(s===rn){r(i,t,n);for(let e=0;e<d.length;e++)G(d[e],t,n,o);return void r(e.anchor,t,n)}if(s===un)return void _(e,t,n);const f=2!==o&&1&c&&u;if(f)if(0===o)u.beforeEnter(i),r(i,t,n),Mt(()=>u.enter(i),a);else{const{leave:o,delayLeave:a,afterLeave:s}=u,d=()=>{e.ctx.isUnmounted?l(i):r(i,t,n)},c=()=>{o(i,()=>{d(),s&&s()})};a?a(i,d,c):c()}else r(i,t,n)},X=(e,t,n,a=!1,r=!1)=>{const{type:l,props:i,ref:s,children:u,dynamicChildren:d,shapeFlag:c,patchFlag:f,dirs:p,cacheIndex:v}=e;if(-2===f&&(r=!1),null!=s&&((0,o.C4)(),ne(s,null,n,e,!0),(0,o.bl)()),null!=v&&(t.renderCache[v]=void 0),256&c)return void t.ctx.deactivate(e);const h=1&c&&p,m=!oe(e);let y;if(m&&(y=i&&i.onVnodeBeforeUnmount)&&In(y,t,e),6&c)H(e.component,n,a);else{if(128&c)return void e.suspense.unmount(n,a);h&&D(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,Q,a):d&&!d.hasOnce&&(l!==rn||f>0&&64&f)?Y(d,t,n,!1,!0):(l===rn&&384&f||!r&&16&c)&&Y(u,t,n),a&&U(e)}(m&&(y=i&&i.onVnodeUnmounted)||h)&&Mt(()=>{y&&In(y,t,e),h&&D(e,null,t,"unmounted")},n)},U=e=>{const{type:t,el:n,anchor:o,transition:a}=e;if(t===rn)return void z(n,o);if(t===un)return void E(e);const r=()=>{l(n),a&&!a.persisted&&a.afterLeave&&a.afterLeave()};if(1&e.shapeFlag&&a&&!a.persisted){const{leave:t,delayLeave:o}=a,l=()=>t(n,r);o?o(e.el,r,l):l()}else r()},z=(e,t)=>{let n;while(e!==t)n=v(e),l(e),e=n;l(t)},H=(e,t,n)=>{const{bum:o,scope:r,job:l,subTree:i,um:s,m:u,a:d,parent:c,slots:{__:f}}=e;Ft(u),Ft(d),o&&(0,a.DY)(o),c&&(0,a.cy)(f)&&f.forEach(e=>{c.renderCache[e]=void 0}),r.stop(),l&&(l.flags|=8,X(i,e,t,n)),s&&Mt(s,t),Mt(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Y=(e,t,n,o=!1,a=!1,r=0)=>{for(let l=r;l<e.length;l++)X(e[l],t,n,o,a)},Z=e=>{if(6&e.shapeFlag)return Z(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=v(e.anchor||e.el),n=t&&t[A];return n?v(n):t};let q=!1;const J=(e,t,n)=>{null==e?t._vnode&&X(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),t._vnode=e,q||(q=!0,R(),x(),q=!1)},Q={p:y,um:X,m:G,r:U,mt:F,mc:S,pc:L,pbc:T,n:Z,o:e};let ee,te;return t&&([ee,te]=t(Q)),{render:J,hydrate:ee,createApp:at(J,ee)}}function At({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ot({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Tt(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Pt(e,t,n=!1){const o=e.children,r=t.children;if((0,a.cy)(o)&&(0,a.cy)(r))for(let a=0;a<o.length;a++){const e=o[a];let t=r[a];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[a]=On(r[a]),t.el=e.el),n||-2===t.patchFlag||Pt(e,t)),t.type===ln&&(t.el=e.el),t.type!==sn||t.el||(t.el=e.el)}}function It(e){const t=e.slice(),n=[0];let o,a,r,l,i;const s=e.length;for(o=0;o<s;o++){const s=e[o];if(0!==s){if(a=n[n.length-1],e[a]<s){t[o]=a,n.push(o);continue}r=0,l=n.length-1;while(r<l)i=r+l>>1,e[n[i]]<s?r=i+1:l=i;s<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}r=n.length,l=n[r-1];while(r-- >0)n[r]=l,l=t[l];return n}function Wt(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Wt(t)}function Ft(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const $t=Symbol.for("v-scx"),Nt=()=>{{const e=it($t);return e}};function Vt(e,t){return Gt(e,null,t)}function Lt(e,t){return Gt(e,null,{flush:"post"})}function Kt(e,t){return Gt(e,null,{flush:"sync"})}function jt(e,t,n){return Gt(e,t,n)}function Gt(e,t,n=a.MZ){const{immediate:r,deep:i,flush:s,once:u}=n;const d=(0,a.X$)({},n);const c=t&&r||!t&&"post"!==s;let f;if(Hn)if("sync"===s){const e=Nt();f=e.__watcherHandles||(e.__watcherHandles=[])}else if(!c){const e=()=>{};return e.stop=a.tE,e.resume=a.tE,e.pause=a.tE,e}const p=Nn;d.call=(e,t,n)=>l(e,p,t,n);let v=!1;"post"===s?d.scheduler=e=>{Mt(e,p&&p.suspense)}:"sync"!==s&&(v=!0,d.scheduler=(e,t)=>{t?e():g(e)}),d.augmentJob=e=>{t&&(e.flags|=4),v&&(e.flags|=2,p&&(e.id=p.uid,e.i=p))};const h=(0,o.wB)(e,t,d);return Hn&&(f?f.push(h):c&&h()),h}function Xt(e,t,n){const o=this.proxy,r=(0,a.Kg)(e)?e.includes(".")?Ut(o,e):()=>o[e]:e.bind(o,o);let l;(0,a.Tn)(t)?l=t:(l=t.handler,n=t);const i=jn(this),s=Gt(r,l.bind(o),n);return i(),s}function Ut(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const zt=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${(0,a.PT)(t)}Modifiers`]||e[`${(0,a.Tg)(t)}Modifiers`];function Ht(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||a.MZ;let r=n;const i=t.startsWith("update:"),s=i&&zt(o,t.slice(7));let u;s&&(s.trim&&(r=n.map(e=>(0,a.Kg)(e)?e.trim():e)),s.number&&(r=n.map(a.bB)));let d=o[u=(0,a.rU)(t)]||o[u=(0,a.rU)((0,a.PT)(t))];!d&&i&&(d=o[u=(0,a.rU)((0,a.Tg)(t))]),d&&l(d,e,6,r);const c=o[u+"Once"];if(c){if(e.emitted){if(e.emitted[u])return}else e.emitted={};e.emitted[u]=!0,l(c,e,6,r)}}function Yt(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const l=e.emits;let i={},s=!1;if(!(0,a.Tn)(e)){const o=e=>{const n=Yt(e,t,!0);n&&(s=!0,(0,a.X$)(i,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return l||s?((0,a.cy)(l)?l.forEach(e=>i[e]=null):(0,a.X$)(i,l),(0,a.Gv)(e)&&o.set(e,i),i):((0,a.Gv)(e)&&o.set(e,null),null)}function Zt(e,t){return!(!e||!(0,a.Mp)(t))&&(t=t.slice(2).replace(/Once$/,""),(0,a.$3)(e,t[0].toLowerCase()+t.slice(1))||(0,a.$3)(e,(0,a.Tg)(t))||(0,a.$3)(e,t))}function qt(e){const{type:t,vnode:n,proxy:o,withProxy:r,propsOptions:[l],slots:s,attrs:u,emit:d,render:c,renderCache:f,props:p,data:v,setupState:h,ctx:m,inheritAttrs:y}=e,g=k(e);let b,w;try{if(4&n.shapeFlag){const e=r||o,t=e;b=An(c.call(t,e,f,p,h,v,m)),w=u}else{const e=t;0,b=An(e.length>1?e(p,{attrs:u,slots:s,emit:d}):e(p,null)),w=t.props?u:Jt(u)}}catch(x){dn.length=0,i(x,e,1),b=_n(sn)}let R=b;if(w&&!1!==y){const e=Object.keys(w),{shapeFlag:t}=R;e.length&&7&t&&(l&&e.some(a.CP)&&(w=Qt(w,l)),R=kn(R,w,!1,!0))}return n.dirs&&(R=kn(R,null,!1,!0),R.dirs=R.dirs?R.dirs.concat(n.dirs):n.dirs),n.transition&&q(R,n.transition),b=R,k(g),b}const Jt=e=>{let t;for(const n in e)("class"===n||"style"===n||(0,a.Mp)(n))&&((t||(t={}))[n]=e[n]);return t},Qt=(e,t)=>{const n={};for(const o in e)(0,a.CP)(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function en(e,t,n){const{props:o,children:a,component:r}=e,{props:l,children:i,patchFlag:s}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return!(!a&&!i||i&&i.$stable)||o!==l&&(o?!l||tn(o,l,u):!!l);if(1024&s)return!0;if(16&s)return o?tn(o,l,u):!!l;if(8&s){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(l[n]!==o[n]&&!Zt(u,n))return!0}}return!1}function tn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let a=0;a<o.length;a++){const r=o[a];if(t[r]!==e[r]&&!Zt(n,r))return!0}return!1}function nn({vnode:e,parent:t},n){while(t){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}const on=e=>e.__isSuspense;function an(e,t){t&&t.pendingBranch?(0,a.cy)(e)?t.effects.push(...e):t.effects.push(e):w(e)}const rn=Symbol.for("v-fgt"),ln=Symbol.for("v-txt"),sn=Symbol.for("v-cmt"),un=Symbol.for("v-stc"),dn=[];let cn=null;function fn(e=!1){dn.push(cn=e?null:[])}function pn(){dn.pop(),cn=dn[dn.length-1]||null}let vn=1;function hn(e,t=!1){vn+=e,e<0&&cn&&t&&(cn.hasOnce=!0)}function mn(e){return e.dynamicChildren=vn>0?cn||a.Oj:null,pn(),vn>0&&cn&&cn.push(e),e}function yn(e,t,n,o,a,r){return mn(Bn(e,t,n,o,a,r,!0))}function gn(e,t,n,o,a){return mn(_n(e,t,n,o,a,!0))}function bn(e){return!!e&&!0===e.__v_isVNode}function wn(e,t){return e.type===t.type&&e.key===t.key}const Rn=({key:e})=>null!=e?e:null,xn=({ref:e,ref_key:t,ref_for:n})=>("number"===typeof e&&(e=""+e),null!=e?(0,a.Kg)(e)||(0,o.i9)(e)||(0,a.Tn)(e)?{i:E,r:e,k:t,f:!!n}:e:null);function Bn(e,t=null,n=null,o=0,r=null,l=(e===rn?0:1),i=!1,s=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Rn(t),ref:t&&xn(t),scopeId:C,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:l,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:E};return s?(Tn(u,n),128&l&&e.normalize(u)):n&&(u.shapeFlag|=(0,a.Kg)(n)?8:16),vn>0&&!i&&cn&&(u.patchFlag>0||6&l)&&32!==u.patchFlag&&cn.push(u),u}const _n=En;function En(e,t=null,n=null,r=0,l=null,i=!1){if(e&&e!==Ee||(e=sn),bn(e)){const o=kn(e,t,!0);return n&&Tn(o,n),vn>0&&!i&&cn&&(6&o.shapeFlag?cn[cn.indexOf(e)]=o:cn.push(o)),o.patchFlag=-2,o}if(oo(e)&&(e=e.__vccOpts),t){t=Cn(t);let{class:e,style:n}=t;e&&!(0,a.Kg)(e)&&(t.class=(0,a.C4)(e)),(0,a.Gv)(n)&&((0,o.ju)(n)&&!(0,a.cy)(n)&&(n=(0,a.X$)({},n)),t.style=(0,a.Tr)(n))}const s=(0,a.Kg)(e)?1:on(e)?128:O(e)?64:(0,a.Gv)(e)?4:(0,a.Tn)(e)?2:0;return Bn(e,t,n,r,l,s,i,!0)}function Cn(e){return e?(0,o.ju)(e)||ct(e)?(0,a.X$)({},e):e:null}function kn(e,t,n=!1,o=!1){const{props:r,ref:l,patchFlag:i,children:s,transition:u}=e,d=t?Pn(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Rn(d),ref:t&&t.ref?n&&l?(0,a.cy)(l)?l.concat(xn(t)):[l,xn(t)]:xn(t):l,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==rn?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&kn(e.ssContent),ssFallback:e.ssFallback&&kn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&o&&q(c,u.clone(c)),c}function Mn(e=" ",t=0){return _n(ln,null,e,t)}function Sn(e,t){const n=_n(un,null,e);return n.staticCount=t,n}function Dn(e="",t=!1){return t?(fn(),gn(sn,null,e)):_n(sn,null,e)}function An(e){return null==e||"boolean"===typeof e?_n(sn):(0,a.cy)(e)?_n(rn,null,e.slice()):bn(e)?On(e):_n(ln,null,String(e))}function On(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:kn(e)}function Tn(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if((0,a.cy)(t))n=16;else if("object"===typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Tn(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||ct(t)?3===o&&E&&(1===E.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=E}}else(0,a.Tn)(t)?(t={default:t,_ctx:E},n=32):(t=String(t),64&o?(n=16,t=[Mn(t)]):n=8);e.children=t,e.shapeFlag|=n}function Pn(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=(0,a.C4)([t.class,o.class]));else if("style"===e)t.style=(0,a.Tr)([t.style,o.style]);else if((0,a.Mp)(e)){const n=t[e],r=o[e];!r||n===r||(0,a.cy)(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function In(e,t,n,o=null){l(e,t,7,[n,o])}const Wn=nt();let Fn=0;function $n(e,t,n){const r=e.type,l=(t?t.appContext:e.appContext)||Wn,i={uid:Fn++,vnode:e,type:r,parent:t,appContext:l,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new o.yC(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(l.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:yt(r,l),emitsOptions:Yt(r,l),emit:null,emitted:null,propsDefaults:a.MZ,inheritAttrs:r.inheritAttrs,ctx:a.MZ,data:a.MZ,props:a.MZ,attrs:a.MZ,slots:a.MZ,refs:a.MZ,setupState:a.MZ,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Ht.bind(null,i),e.ce&&e.ce(i),i}let Nn=null;const Vn=()=>Nn||E;let Ln,Kn;{const e=(0,a.We)(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach(t=>t(e)):o[0](e)}};Ln=t("__VUE_INSTANCE_SETTERS__",e=>Nn=e),Kn=t("__VUE_SSR_SETTERS__",e=>Hn=e)}const jn=e=>{const t=Nn;return Ln(e),e.scope.on(),()=>{e.scope.off(),Ln(t)}},Gn=()=>{Nn&&Nn.scope.off(),Ln(null)};function Xn(e){return 4&e.vnode.shapeFlag}let Un,zn,Hn=!1;function Yn(e,t=!1,n=!1){t&&Kn(t);const{props:o,children:a}=e.vnode,r=Xn(e);ft(e,o,r,t),Et(e,a,n||t);const l=r?Zn(e,t):void 0;return t&&Kn(!1),l}function Zn(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,We);const{setup:l}=n;if(l){(0,o.C4)();const n=e.setupContext=l.length>1?eo(e):null,s=jn(e),u=r(l,e,0,[e.props,n]),d=(0,a.yL)(u);if((0,o.bl)(),s(),!d&&!e.sp||oe(e)||te(e),d){if(u.then(Gn,Gn),t)return u.then(n=>{qn(e,n,t)}).catch(t=>{i(t,e,0)});e.asyncDep=u}else qn(e,u,t)}else Jn(e,t)}function qn(e,t,n){(0,a.Tn)(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:(0,a.Gv)(t)&&(e.setupState=(0,o.Pr)(t)),Jn(e,n)}function Jn(e,t,n){const r=e.type;if(!e.render){if(!t&&Un&&!r.render){const t=r.template||Ue(e).template;if(t){0;const{isCustomElement:n,compilerOptions:o}=e.appContext.config,{delimiters:l,compilerOptions:i}=r,s=(0,a.X$)((0,a.X$)({isCustomElement:n,delimiters:l},o),i);r.render=Un(t,s)}}e.render=r.render||a.tE,zn&&zn(e)}{const t=jn(e);(0,o.C4)();try{Ke(e)}finally{(0,o.bl)(),t()}}}const Qn={get(e,t){return(0,o.u4)(e,"get",""),e[t]}};function eo(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,Qn),slots:e.slots,emit:e.emit,expose:t}}function to(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy((0,o.Pr)((0,o.IG)(e.exposed)),{get(t,n){return n in t?t[n]:n in Pe?Pe[n](e):void 0},has(e,t){return t in e||t in Pe}})):e.proxy}function no(e,t=!0){return(0,a.Tn)(e)?e.displayName||e.name:e.name||t&&e.__name}function oo(e){return(0,a.Tn)(e)&&"__vccOpts"in e}const ao=(e,t)=>{const n=(0,o.EW)(e,t,Hn);return n};function ro(e,t,n){const o=arguments.length;return 2===o?(0,a.Gv)(t)&&!(0,a.cy)(t)?bn(t)?_n(e,null,[t]):_n(e,t):_n(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&bn(n)&&(n=[n]),_n(e,t,n))}const lo="3.5.17"},751:(e,t,n)=>{n.d(t,{D$:()=>te,Ef:()=>ie,Jo:()=>Y,aG:()=>g,jR:()=>oe,u1:()=>Z});var o=n(641),a=n(33);n(953);
/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let r;const l="undefined"!==typeof window&&window.trustedTypes;if(l)try{r=l.createPolicy("vue",{createHTML:e=>e})}catch(de){}const i=r?e=>r.createHTML(e):e=>e,s="http://www.w3.org/2000/svg",u="http://www.w3.org/1998/Math/MathML",d="undefined"!==typeof document?document:null,c=d&&d.createElement("template"),f={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const a="svg"===t?d.createElementNS(s,e):"mathml"===t?d.createElementNS(u,e):n?d.createElement(e,{is:n}):d.createElement(e);return"select"===e&&o&&null!=o.multiple&&a.setAttribute("multiple",o.multiple),a},createText:e=>d.createTextNode(e),createComment:e=>d.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>d.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,a,r){const l=n?n.previousSibling:t.lastChild;if(a&&(a===r||a.nextSibling)){while(1)if(t.insertBefore(a.cloneNode(!0),n),a===r||!(a=a.nextSibling))break}else{c.innerHTML=i("svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e);const a=c.content;if("svg"===o||"mathml"===o){const e=a.firstChild;while(e.firstChild)a.appendChild(e.firstChild);a.removeChild(e)}t.insertBefore(a,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},p=Symbol("_vtc"),v={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};o.QP;function h(e,t,n){const o=e[p];o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const m=Symbol("_vod"),y=Symbol("_vsh"),g={beforeMount(e,{value:t},{transition:n}){e[m]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):b(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!==!n&&(o?t?(o.beforeEnter(e),b(e,!0),o.enter(e)):o.leave(e,()=>{b(e,!1)}):b(e,t))},beforeUnmount(e,{value:t}){b(e,t)}};function b(e,t){e.style.display=t?e[m]:"none",e[y]=!t}const w=Symbol("");const R=/(^|;)\s*display\s*:/;function x(e,t,n){const o=e.style,r=(0,a.Kg)(n);let l=!1;if(n&&!r){if(t)if((0,a.Kg)(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&_(o,t,"")}else for(const e in t)null==n[e]&&_(o,e,"");for(const e in n)"display"===e&&(l=!0),_(o,e,n[e])}else if(r){if(t!==n){const e=o[w];e&&(n+=";"+e),o.cssText=n,l=R.test(n)}}else t&&e.removeAttribute("style");m in e&&(e[m]=l?o.display:"",e[y]&&(o.display="none"))}const B=/\s*!important$/;function _(e,t,n){if((0,a.cy)(n))n.forEach(n=>_(e,t,n));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=k(e,t);B.test(n)?e.setProperty((0,a.Tg)(o),n.replace(B,""),"important"):e[o]=n}}const E=["Webkit","Moz","ms"],C={};function k(e,t){const n=C[t];if(n)return n;let o=(0,a.PT)(t);if("filter"!==o&&o in e)return C[t]=o;o=(0,a.ZH)(o);for(let a=0;a<E.length;a++){const n=E[a]+o;if(n in e)return C[t]=n}return t}const M="http://www.w3.org/1999/xlink";function S(e,t,n,o,r,l=(0,a.J$)(t)){o&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(M,t.slice(6,t.length)):e.setAttributeNS(M,t,n):null==n||l&&!(0,a.Y2)(n)?e.removeAttribute(t):e.setAttribute(t,l?"":(0,a.Bm)(n)?String(n):n)}function D(e,t,n,o,r){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?i(n):n));const l=e.tagName;if("value"===t&&"PROGRESS"!==l&&!l.includes("-")){const o="OPTION"===l?e.getAttribute("value")||"":e.value,a=null==n?"checkbox"===e.type?"on":"":String(n);return o===a&&"_value"in e||(e.value=a),null==n&&e.removeAttribute(t),void(e._value=n)}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=(0,a.Y2)(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(de){0}s&&e.removeAttribute(r||t)}function A(e,t,n,o){e.addEventListener(t,n,o)}function O(e,t,n,o){e.removeEventListener(t,n,o)}const T=Symbol("_vei");function P(e,t,n,o,a=null){const r=e[T]||(e[T]={}),l=r[t];if(o&&l)l.value=o;else{const[n,i]=W(t);if(o){const l=r[t]=V(o,a);A(e,n,l,i)}else l&&(O(e,n,l,i),r[t]=void 0)}}const I=/(?:Once|Passive|Capture)$/;function W(e){let t;if(I.test(e)){let n;t={};while(n=e.match(I))e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):(0,a.Tg)(e.slice(2));return[n,t]}let F=0;const $=Promise.resolve(),N=()=>F||($.then(()=>F=0),F=Date.now());function V(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();(0,o.qL)(L(e,n.value),t,5,[e])};return n.value=e,n.attached=N(),n}function L(e,t){if((0,a.cy)(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}return t}const K=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,j=(e,t,n,o,r,l)=>{const i="svg"===r;"class"===t?h(e,o,i):"style"===t?x(e,n,o):(0,a.Mp)(t)?(0,a.CP)(t)||P(e,t,n,o,l):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):G(e,t,o,i))?(D(e,t,o),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||S(e,t,o,i,l,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&(0,a.Kg)(o)?("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),S(e,t,o,i)):D(e,(0,a.PT)(t),o,l,t)};function G(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&K(t)&&(0,a.Tn)(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}return(!K(t)||!(0,a.Kg)(n))&&t in e}
/*! #__NO_SIDE_EFFECTS__ */
"undefined"!==typeof HTMLElement&&HTMLElement;Symbol("_moveCb"),Symbol("_enterCb");const X=e=>{const t=e.props["onUpdate:modelValue"]||!1;return(0,a.cy)(t)?e=>(0,a.DY)(t,e):t};function U(e){e.target.composing=!0}function z(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const H=Symbol("_assign"),Y={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e[H]=X(r);const l=o||r.props&&"number"===r.props.type;A(e,t?"change":"input",t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),l&&(o=(0,a.bB)(o)),e[H](o)}),n&&A(e,"change",()=>{e.value=e.value.trim()}),t||(A(e,"compositionstart",U),A(e,"compositionend",z),A(e,"change",z))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:o,trim:r,number:l}},i){if(e[H]=X(i),e.composing)return;const s=!l&&"number"!==e.type||/^0\d/.test(e.value)?e.value:(0,a.bB)(e.value),u=null==t?"":t;if(s!==u){if(document.activeElement===e&&"range"!==e.type){if(o&&t===n)return;if(r&&e.value.trim()===u)return}e.value=u}}};const Z={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const l=(0,a.vM)(t);A(e,"change",()=>{const t=Array.prototype.filter.call(e.options,e=>e.selected).map(e=>n?(0,a.bB)(J(e)):J(e));e[H](e.multiple?l?new Set(t):t:t[0]),e._assigning=!0,(0,o.dY)(()=>{e._assigning=!1})}),e[H]=X(r)},mounted(e,{value:t}){q(e,t)},beforeUpdate(e,t,n){e[H]=X(n)},updated(e,{value:t}){e._assigning||q(e,t)}};function q(e,t){const n=e.multiple,o=(0,a.cy)(t);if(!n||o||(0,a.vM)(t)){for(let r=0,l=e.options.length;r<l;r++){const l=e.options[r],i=J(l);if(n)if(o){const e=typeof i;l.selected="string"===e||"number"===e?t.some(e=>String(e)===String(i)):(0,a.u3)(t,i)>-1}else l.selected=t.has(i);else if((0,a.BX)(J(l),t))return void(e.selectedIndex!==r&&(e.selectedIndex=r))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function J(e){return"_value"in e?e._value:e.value}const Q=["ctrl","shift","alt","meta"],ee={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Q.some(n=>e[`${n}Key`]&&!t.includes(n))},te=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=ee[t[e]];if(o&&o(n,t))return}return e(n,...o)})},ne={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},oe=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=n=>{if(!("key"in n))return;const o=(0,a.Tg)(n.key);return t.some(e=>e===o||ne[e]===o)?e(n):void 0})},ae=(0,a.X$)({patchProp:j},f);let re;function le(){return re||(re=(0,o.K9)(ae))}const ie=(...e)=>{const t=le().createApp(...e);const{mount:n}=t;return t.mount=e=>{const o=ue(e);if(!o)return;const r=t._component;(0,a.Tn)(r)||r.render||r.template||(r.template=o.innerHTML),1===o.nodeType&&(o.textContent="");const l=n(o,!1,se(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),l},t};function se(e){return e instanceof SVGElement?"svg":"function"===typeof MathMLElement&&e instanceof MathMLElement?"mathml":void 0}function ue(e){if((0,a.Kg)(e)){const t=document.querySelector(e);return t}return e}},953:(e,t,n)=>{n.d(t,{C4:()=>C,EW:()=>qe,Gc:()=>xe,IG:()=>Ae,IJ:()=>We,KR:()=>Ie,Kh:()=>Re,Pr:()=>Ke,QW:()=>Xe,R1:()=>Ve,Tm:()=>ke,X2:()=>c,a1:()=>Te,bl:()=>k,fE:()=>Me,g8:()=>Ce,hV:()=>ot,hZ:()=>$,i9:()=>Pe,jr:()=>u,ju:()=>Se,lJ:()=>Oe,lW:()=>He,mu:()=>Ne,nD:()=>_e,o5:()=>s,qA:()=>L,rY:()=>Ge,tB:()=>Be,u4:()=>F,uY:()=>i,ux:()=>De,wB:()=>nt,yC:()=>l});var o=n(33);
/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let a,r;class l{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=a,!e&&a&&(this.index=(a.scopes||(a.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=a;try{return a=this,e()}finally{a=t}}else 0}on(){1===++this._on&&(this.prevScope=a,a=this)}off(){this._on>0&&0===--this._on&&(a=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function i(e){return new l(e)}function s(){return a}function u(e,t=!1){a&&a.cleanups.push(e)}const d=new WeakSet;class c{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,a&&a.active&&a.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,d.has(this)&&(d.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||h(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,M(this),g(this);const e=r,t=_;r=this,_=!0;try{return this.fn()}finally{0,b(this),r=e,_=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)x(e);this.deps=this.depsTail=void 0,M(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?d.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){w(this)&&this.run()}get dirty(){return w(this)}}let f,p,v=0;function h(e,t=!1){if(e.flags|=8,t)return e.next=p,void(p=e);e.next=f,f=e}function m(){v++}function y(){if(--v>0)return;if(p){let e=p;p=void 0;while(e){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;while(f){let n=f;f=void 0;while(n){const o=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=o}}if(e)throw e}function g(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function b(e){let t,n=e.depsTail,o=n;while(o){const e=o.prevDep;-1===o.version?(o===n&&(n=e),x(o),B(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=e}e.deps=t,e.depsTail=n}function w(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(R(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function R(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===S)return;if(e.globalVersion=S,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!w(e)))return;e.flags|=2;const t=e.dep,n=r,a=_;r=e,_=!0;try{g(e);const n=e.fn(e._value);(0===t.version||(0,o.$H)(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(l){throw t.version++,l}finally{r=n,_=a,b(e),e.flags&=-3}}function x(e,t=!1){const{dep:n,prevSub:o,nextSub:a}=e;if(o&&(o.nextSub=a,e.prevSub=void 0),a&&(a.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)x(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function B(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let _=!0;const E=[];function C(){E.push(_),_=!1}function k(){const e=E.pop();_=void 0===e||e}function M(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=r;r=void 0;try{t()}finally{r=e}}}let S=0;class D{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class A{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!r||!_||r===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==r)t=this.activeLink=new D(r,this),r.deps?(t.prevDep=r.depsTail,r.depsTail.nextDep=t,r.depsTail=t):r.deps=r.depsTail=t,O(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=r.depsTail,t.nextDep=void 0,r.depsTail.nextDep=t,r.depsTail=t,r.deps===t&&(r.deps=e)}return t}trigger(e){this.version++,S++,this.notify(e)}notify(e){m();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{y()}}}function O(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)O(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const T=new WeakMap,P=Symbol(""),I=Symbol(""),W=Symbol("");function F(e,t,n){if(_&&r){let t=T.get(e);t||T.set(e,t=new Map);let o=t.get(n);o||(t.set(n,o=new A),o.map=t,o.key=n),o.track()}}function $(e,t,n,a,r,l){const i=T.get(e);if(!i)return void S++;const s=e=>{e&&e.trigger()};if(m(),"clear"===t)i.forEach(s);else{const r=(0,o.cy)(e),l=r&&(0,o.yI)(n);if(r&&"length"===n){const e=Number(a);i.forEach((t,n)=>{("length"===n||n===W||!(0,o.Bm)(n)&&n>=e)&&s(t)})}else switch((void 0!==n||i.has(void 0))&&s(i.get(n)),l&&s(i.get(W)),t){case"add":r?l&&s(i.get("length")):(s(i.get(P)),(0,o.CE)(e)&&s(i.get(I)));break;case"delete":r||(s(i.get(P)),(0,o.CE)(e)&&s(i.get(I)));break;case"set":(0,o.CE)(e)&&s(i.get(P));break}}y()}function N(e,t){const n=T.get(e);return n&&n.get(t)}function V(e){const t=De(e);return t===e?t:(F(t,"iterate",W),Me(e)?t:t.map(Oe))}function L(e){return F(e=De(e),"iterate",W),e}const K={__proto__:null,[Symbol.iterator](){return j(this,Symbol.iterator,Oe)},concat(...e){return V(this).concat(...e.map(e=>(0,o.cy)(e)?V(e):e))},entries(){return j(this,"entries",e=>(e[1]=Oe(e[1]),e))},every(e,t){return X(this,"every",e,t,void 0,arguments)},filter(e,t){return X(this,"filter",e,t,e=>e.map(Oe),arguments)},find(e,t){return X(this,"find",e,t,Oe,arguments)},findIndex(e,t){return X(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return X(this,"findLast",e,t,Oe,arguments)},findLastIndex(e,t){return X(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return X(this,"forEach",e,t,void 0,arguments)},includes(...e){return z(this,"includes",e)},indexOf(...e){return z(this,"indexOf",e)},join(e){return V(this).join(e)},lastIndexOf(...e){return z(this,"lastIndexOf",e)},map(e,t){return X(this,"map",e,t,void 0,arguments)},pop(){return H(this,"pop")},push(...e){return H(this,"push",e)},reduce(e,...t){return U(this,"reduce",e,t)},reduceRight(e,...t){return U(this,"reduceRight",e,t)},shift(){return H(this,"shift")},some(e,t){return X(this,"some",e,t,void 0,arguments)},splice(...e){return H(this,"splice",e)},toReversed(){return V(this).toReversed()},toSorted(e){return V(this).toSorted(e)},toSpliced(...e){return V(this).toSpliced(...e)},unshift(...e){return H(this,"unshift",e)},values(){return j(this,"values",Oe)}};function j(e,t,n){const o=L(e),a=o[t]();return o===e||Me(e)||(a._next=a.next,a.next=()=>{const e=a._next();return e.value&&(e.value=n(e.value)),e}),a}const G=Array.prototype;function X(e,t,n,o,a,r){const l=L(e),i=l!==e&&!Me(e),s=l[t];if(s!==G[t]){const t=s.apply(e,r);return i?Oe(t):t}let u=n;l!==e&&(i?u=function(t,o){return n.call(this,Oe(t),o,e)}:n.length>2&&(u=function(t,o){return n.call(this,t,o,e)}));const d=s.call(l,u,o);return i&&a?a(d):d}function U(e,t,n,o){const a=L(e);let r=n;return a!==e&&(Me(e)?n.length>3&&(r=function(t,o,a){return n.call(this,t,o,a,e)}):r=function(t,o,a){return n.call(this,t,Oe(o),a,e)}),a[t](r,...o)}function z(e,t,n){const o=De(e);F(o,"iterate",W);const a=o[t](...n);return-1!==a&&!1!==a||!Se(n[0])?a:(n[0]=De(n[0]),o[t](...n))}function H(e,t,n=[]){C(),m();const o=De(e)[t].apply(e,n);return y(),k(),o}const Y=(0,o.pD)("__proto__,__v_isRef,__isVue"),Z=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(o.Bm));function q(e){(0,o.Bm)(e)||(e=String(e));const t=De(this);return F(t,"has",e),t.hasOwnProperty(e)}class J{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e["__v_skip"];const a=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!a;if("__v_isReadonly"===t)return a;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(a?r?ge:ye:r?me:he).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const l=(0,o.cy)(e);if(!a){let e;if(l&&(e=K[t]))return e;if("hasOwnProperty"===t)return q}const i=Reflect.get(e,t,Pe(e)?e:n);return((0,o.Bm)(t)?Z.has(t):Y(t))?i:(a||F(e,"get",t),r?i:Pe(i)?l&&(0,o.yI)(t)?i:i.value:(0,o.Gv)(i)?a?Be(i):Re(i):i)}}class Q extends J{constructor(e=!1){super(!1,e)}set(e,t,n,a){let r=e[t];if(!this._isShallow){const t=ke(r);if(Me(n)||ke(n)||(r=De(r),n=De(n)),!(0,o.cy)(e)&&Pe(r)&&!Pe(n))return!t&&(r.value=n,!0)}const l=(0,o.cy)(e)&&(0,o.yI)(t)?Number(t)<e.length:(0,o.$3)(e,t),i=Reflect.set(e,t,n,Pe(e)?e:a);return e===De(a)&&(l?(0,o.$H)(n,r)&&$(e,"set",t,n,r):$(e,"add",t,n)),i}deleteProperty(e,t){const n=(0,o.$3)(e,t),a=e[t],r=Reflect.deleteProperty(e,t);return r&&n&&$(e,"delete",t,void 0,a),r}has(e,t){const n=Reflect.has(e,t);return(0,o.Bm)(t)&&Z.has(t)||F(e,"has",t),n}ownKeys(e){return F(e,"iterate",(0,o.cy)(e)?"length":P),Reflect.ownKeys(e)}}class ee extends J{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const te=new Q,ne=new ee,oe=new Q(!0),ae=new ee(!0),re=e=>e,le=e=>Reflect.getPrototypeOf(e);function ie(e,t,n){return function(...a){const r=this["__v_raw"],l=De(r),i=(0,o.CE)(l),s="entries"===e||e===Symbol.iterator&&i,u="keys"===e&&i,d=r[e](...a),c=n?re:t?Te:Oe;return!t&&F(l,"iterate",u?I:P),{next(){const{value:e,done:t}=d.next();return t?{value:e,done:t}:{value:s?[c(e[0]),c(e[1])]:c(e),done:t}},[Symbol.iterator](){return this}}}}function se(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function ue(e,t){const n={get(n){const a=this["__v_raw"],r=De(a),l=De(n);e||((0,o.$H)(n,l)&&F(r,"get",n),F(r,"get",l));const{has:i}=le(r),s=t?re:e?Te:Oe;return i.call(r,n)?s(a.get(n)):i.call(r,l)?s(a.get(l)):void(a!==r&&a.get(n))},get size(){const t=this["__v_raw"];return!e&&F(De(t),"iterate",P),Reflect.get(t,"size",t)},has(t){const n=this["__v_raw"],a=De(n),r=De(t);return e||((0,o.$H)(t,r)&&F(a,"has",t),F(a,"has",r)),t===r?n.has(t):n.has(t)||n.has(r)},forEach(n,o){const a=this,r=a["__v_raw"],l=De(r),i=t?re:e?Te:Oe;return!e&&F(l,"iterate",P),r.forEach((e,t)=>n.call(o,i(e),i(t),a))}};(0,o.X$)(n,e?{add:se("add"),set:se("set"),delete:se("delete"),clear:se("clear")}:{add(e){t||Me(e)||ke(e)||(e=De(e));const n=De(this),o=le(n),a=o.has.call(n,e);return a||(n.add(e),$(n,"add",e,e)),this},set(e,n){t||Me(n)||ke(n)||(n=De(n));const a=De(this),{has:r,get:l}=le(a);let i=r.call(a,e);i||(e=De(e),i=r.call(a,e));const s=l.call(a,e);return a.set(e,n),i?(0,o.$H)(n,s)&&$(a,"set",e,n,s):$(a,"add",e,n),this},delete(e){const t=De(this),{has:n,get:o}=le(t);let a=n.call(t,e);a||(e=De(e),a=n.call(t,e));const r=o?o.call(t,e):void 0,l=t.delete(e);return a&&$(t,"delete",e,void 0,r),l},clear(){const e=De(this),t=0!==e.size,n=void 0,o=e.clear();return t&&$(e,"clear",void 0,void 0,n),o}});const a=["keys","values","entries",Symbol.iterator];return a.forEach(o=>{n[o]=ie(o,e,t)}),n}function de(e,t){const n=ue(e,t);return(t,a,r)=>"__v_isReactive"===a?!e:"__v_isReadonly"===a?e:"__v_raw"===a?t:Reflect.get((0,o.$3)(n,a)&&a in t?n:t,a,r)}const ce={get:de(!1,!1)},fe={get:de(!1,!0)},pe={get:de(!0,!1)},ve={get:de(!0,!0)};const he=new WeakMap,me=new WeakMap,ye=new WeakMap,ge=new WeakMap;function be(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function we(e){return e["__v_skip"]||!Object.isExtensible(e)?0:be((0,o.Zf)(e))}function Re(e){return ke(e)?e:Ee(e,!1,te,ce,he)}function xe(e){return Ee(e,!1,oe,fe,me)}function Be(e){return Ee(e,!0,ne,pe,ye)}function _e(e){return Ee(e,!0,ae,ve,ge)}function Ee(e,t,n,a,r){if(!(0,o.Gv)(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const l=we(e);if(0===l)return e;const i=r.get(e);if(i)return i;const s=new Proxy(e,2===l?a:n);return r.set(e,s),s}function Ce(e){return ke(e)?Ce(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function ke(e){return!(!e||!e["__v_isReadonly"])}function Me(e){return!(!e||!e["__v_isShallow"])}function Se(e){return!!e&&!!e["__v_raw"]}function De(e){const t=e&&e["__v_raw"];return t?De(t):e}function Ae(e){return!(0,o.$3)(e,"__v_skip")&&Object.isExtensible(e)&&(0,o.yQ)(e,"__v_skip",!0),e}const Oe=e=>(0,o.Gv)(e)?Re(e):e,Te=e=>(0,o.Gv)(e)?Be(e):e;function Pe(e){return!!e&&!0===e["__v_isRef"]}function Ie(e){return Fe(e,!1)}function We(e){return Fe(e,!0)}function Fe(e,t){return Pe(e)?e:new $e(e,t)}class $e{constructor(e,t){this.dep=new A,this["__v_isRef"]=!0,this["__v_isShallow"]=!1,this._rawValue=t?e:De(e),this._value=t?e:Oe(e),this["__v_isShallow"]=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this["__v_isShallow"]||Me(e)||ke(e);e=n?e:De(e),(0,o.$H)(e,t)&&(this._rawValue=e,this._value=n?e:Oe(e),this.dep.trigger())}}function Ne(e){e.dep&&e.dep.trigger()}function Ve(e){return Pe(e)?e.value:e}const Le={get:(e,t,n)=>"__v_raw"===t?e:Ve(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const a=e[t];return Pe(a)&&!Pe(n)?(a.value=n,!0):Reflect.set(e,t,n,o)}};function Ke(e){return Ce(e)?e:new Proxy(e,Le)}class je{constructor(e){this["__v_isRef"]=!0,this._value=void 0;const t=this.dep=new A,{get:n,set:o}=e(t.track.bind(t),t.trigger.bind(t));this._get=n,this._set=o}get value(){return this._value=this._get()}set value(e){this._set(e)}}function Ge(e){return new je(e)}function Xe(e){const t=(0,o.cy)(e)?new Array(e.length):{};for(const n in e)t[n]=Ye(e,n);return t}class Ue{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this["__v_isRef"]=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return N(De(this._object),this._key)}}class ze{constructor(e){this._getter=e,this["__v_isRef"]=!0,this["__v_isReadonly"]=!0,this._value=void 0}get value(){return this._value=this._getter()}}function He(e,t,n){return Pe(e)?e:(0,o.Tn)(e)?new ze(e):(0,o.Gv)(e)&&arguments.length>1?Ye(e,t,n):Ie(e)}function Ye(e,t,n){const o=e[t];return Pe(o)?o:new Ue(e,t,n)}class Ze{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new A(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=S-1,this.next=void 0,this.effect=this,this["__v_isReadonly"]=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags||r===this))return h(this,!0),!0}get value(){const e=this.dep.track();return R(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function qe(e,t,n=!1){let a,r;(0,o.Tn)(e)?a=e:(a=e.get,r=e.set);const l=new Ze(a,r,n);return l}const Je={},Qe=new WeakMap;let et;function tt(e,t=!1,n=et){if(n){let t=Qe.get(n);t||Qe.set(n,t=[]),t.push(e)}else 0}function nt(e,t,n=o.MZ){const{immediate:a,deep:r,once:l,scheduler:i,augmentJob:u,call:d}=n,f=e=>r?e:Me(e)||!1===r||0===r?ot(e,1):ot(e);let p,v,h,m,y=!1,g=!1;if(Pe(e)?(v=()=>e.value,y=Me(e)):Ce(e)?(v=()=>f(e),y=!0):(0,o.cy)(e)?(g=!0,y=e.some(e=>Ce(e)||Me(e)),v=()=>e.map(e=>Pe(e)?e.value:Ce(e)?f(e):(0,o.Tn)(e)?d?d(e,2):e():void 0)):v=(0,o.Tn)(e)?t?d?()=>d(e,2):e:()=>{if(h){C();try{h()}finally{k()}}const t=et;et=p;try{return d?d(e,3,[m]):e(m)}finally{et=t}}:o.tE,t&&r){const e=v,t=!0===r?1/0:r;v=()=>ot(e(),t)}const b=s(),w=()=>{p.stop(),b&&b.active&&(0,o.TF)(b.effects,p)};if(l&&t){const e=t;t=(...t)=>{e(...t),w()}}let R=g?new Array(e.length).fill(Je):Je;const x=e=>{if(1&p.flags&&(p.dirty||e))if(t){const e=p.run();if(r||y||(g?e.some((e,t)=>(0,o.$H)(e,R[t])):(0,o.$H)(e,R))){h&&h();const n=et;et=p;try{const n=[e,R===Je?void 0:g&&R[0]===Je?[]:R,m];R=e,d?d(t,3,n):t(...n)}finally{et=n}}}else p.run()};return u&&u(x),p=new c(v),p.scheduler=i?()=>i(x,!1):x,m=e=>tt(e,!1,p),h=p.onStop=()=>{const e=Qe.get(p);if(e){if(d)d(e,4);else for(const t of e)t();Qe.delete(p)}},t?a?x(!0):R=p.run():i?i(x.bind(null,!0),!0):p.run(),w.pause=p.pause.bind(p),w.resume=p.resume.bind(p),w.stop=w,w}function ot(e,t=1/0,n){if(t<=0||!(0,o.Gv)(e)||e["__v_skip"])return e;if(n=n||new Set,n.has(e))return e;if(n.add(e),t--,Pe(e))ot(e.value,t,n);else if((0,o.cy)(e))for(let o=0;o<e.length;o++)ot(e[o],t,n);else if((0,o.vM)(e)||(0,o.CE)(e))e.forEach(e=>{ot(e,t,n)});else if((0,o.Qd)(e)){for(const o in e)ot(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&ot(e[o],t,n)}return e}}}]);
//# sourceMappingURL=chunk-vendors.ca68ecde.js.map