(()=>{"use strict";var e={639:(e,r,t)=>{var a=t(751),o=t(615),s=t(641),i=t(953),n=t(33);const d=(0,o.nY)("app",{state:()=>({promptElements:{},mainPrompt:"",prompt:"",dataFolder:"",categories:[],checkboxes:[],usedItems:new Set,isLoading:!0,savedPrompts:[]}),actions:{appendToMainPrompt(e){e&&(this.mainPrompt.length>0?this.mainPrompt+="\n"+e:this.mainPrompt=e)},appendFormattedToMainPrompt(e,r){if(!r||!e)return;const t=`**${e}**\n${r}`;this.mainPrompt.length>0?this.mainPrompt+="\n"+t:this.mainPrompt=t},setPrompt(e){this.prompt=e},appendToPrompt(e,r){e&&(this.prompt+=(this.prompt?"\n\n":"")+e,this.usedItems.add(r))},clearPrompt(){this.prompt="",this.usedItems.clear(),this.categories.forEach(e=>{e.selected=""})},async fetchData(){this.isLoading=!0;try{const[e,r,t]=await Promise.all([fetch("/categories.json").catch(e=>e),fetch("/checkboxes.json").catch(e=>e),fetch("/prompt_elements.json").catch(e=>e)]);if(e.ok){const r=await e.json();this.categories=r.map(e=>({...e,selected:""}))}if(r.ok&&(this.checkboxes=await r.json()),t.ok&&(this.promptElements=await t.json()),window.electronAPI&&window.electronAPI.loadData){const e=await window.electronAPI.loadData();if(e){this.savedPrompts=e.prompts||[];for(const r of this.categories)e[r.id]&&e[r.id].items&&(r.items=e[r.id].items)}}else console.warn("window.electronAPI is not available. Running in browser mode.")}catch(e){console.error("Error loading initial data:",e)}finally{this.isLoading=!1}},async savePrompt(){if(!this.prompt.trim())return!1;try{return window.electronAPI&&window.electronAPI.savePrompt?await window.electronAPI.savePrompt(this.prompt):(console.warn("Mocking prompt save."),!0)}catch(e){return console.error("Error saving prompt:",e),!1}},async copyToClipboard(){if(!this.prompt)return!1;try{return await navigator.clipboard.writeText(this.prompt),!0}catch(e){return console.error("Error copying to clipboard:",e),!1}},setDataFolder(e){this.dataFolder=e,this.fetchData()},isItemUsed(e){return this.usedItems.has(e)},clearMainPrompt(){this.mainPrompt=""},async saveMainPromptToList(){if(!this.mainPrompt.trim())return!1;const e={id:Date.now(),content:this.mainPrompt.trim(),createdAt:(new Date).toISOString(),preview:this.mainPrompt.trim().substring(0,100)+(this.mainPrompt.trim().length>100?"...":"")};this.savedPrompts.unshift(e);try{return window.electronAPI&&window.electronAPI.savePrompt&&await window.electronAPI.savePrompt(this.mainPrompt),!0}catch(r){return console.error("Error saving prompt to file:",r),!0}},loadPromptFromSaved(e){const r=this.savedPrompts.find(r=>r.id===e);return!!r&&(this.mainPrompt=r.content,!0)},removeSavedPrompt(e){const r=this.savedPrompts.findIndex(r=>r.id===e);return-1!==r&&(this.savedPrompts.splice(r,1),!0)}}});var l=t(0);const c={class:"space-y-4 p-4"},p=["for"],u={class:"space-y-2"},m={class:"flex items-center space-x-2"},f=["id","onUpdate:modelValue"],k={value:"",class:"text-gray-500 dark:text-gray-400"},g=["value"],h=["onClick","disabled","title"],v={key:0,class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},b={key:1,class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},y={class:"flex flex-col space-y-2"},x={class:"flex items-center space-x-2"},w=["onUpdate:modelValue","onKeyup","placeholder"],L=["onClick"],P=["onClick"],C={class:"space-y-3"},j={class:"flex items-center justify-between p-2.5 bg-gray-50 dark:bg-black rounded-lg border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-200 hover:shadow-sm"},E={class:"flex items-center space-x-3"},M=["for"],I=["onClick","disabled"],_={class:"flex items-center space-x-1"},A={key:0,class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},F={key:1,class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},R={__name:"LeftPanel",setup(e){const r=d(),t=(0,i.Kh)({}),o=e=>{const r=(t[e.id]||"").trim();r&&!e.items.includes(r)&&(e.items.push(r),t[e.id]="")},R=e=>{if(e.selected){const r=e.items.indexOf(e.selected);-1!==r&&(e.items.splice(r,1),e.selected="")}},T=e=>{if(e.selected){const t=`category-${e.id}`;r.appendFormattedToMainPrompt(e.name,e.selected),r.usedItems.add(t)}},X=e=>{e.checked&&(r.appendFormattedToMainPrompt("Instructions",e.label),r.usedItems.add(`checkbox-${e.id}`),e.used=!0)};return(0,s.sV)(()=>{r.fetchData(),(0,s.wB)(()=>r.categories,e=>{e.forEach(e=>{void 0===t[e.id]&&(t[e.id]="")})},{immediate:!0,deep:!0})}),(e,d)=>((0,s.uX)(),(0,s.CE)("div",c,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)((0,i.R1)(r).categories,e=>((0,s.uX)(),(0,s.CE)("div",{key:e.id,class:"space-y-2"},[(0,s.Lk)("label",{for:e.id,class:"block text-xs font-semibold text-gray-800 dark:text-white tracking-wide"},(0,n.v_)(e.name),9,p),(0,s.Lk)("div",u,[(0,s.Lk)("div",m,[(0,s.bo)((0,s.Lk)("select",{id:e.id,"onUpdate:modelValue":r=>e.selected=r,class:(0,n.C4)(["flex-1 px-3 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500",{"border-emerald-500 dark:border-emerald-400 ring-2 ring-emerald-200 dark:ring-emerald-800":(0,i.R1)(r).isItemUsed(`category-${e.id}`)}])},[(0,s.Lk)("option",k,"Select "+(0,n.v_)(e.name.toLowerCase()),1),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.items,(e,r)=>((0,s.uX)(),(0,s.CE)("option",{key:r,value:e,class:"text-gray-900 dark:text-white"},(0,n.v_)(e),9,g))),128))],10,f),[[a.u1,e.selected]]),(0,s.Lk)("button",{onClick:r=>T(e),class:(0,n.C4)(["px-2.5 py-1.5 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95",{"bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-600 dark:hover:bg-emerald-700 ring-2 ring-emerald-200 dark:ring-emerald-800":(0,i.R1)(r).isItemUsed(`category-${e.id}`)}]),disabled:!e.selected,title:(0,i.R1)(r).isItemUsed(`category-${e.id}`)?"Added to prompt":"Add to prompt"},[(0,i.R1)(r).isItemUsed(`category-${e.id}`)?((0,s.uX)(),(0,s.CE)("svg",v,d[0]||(d[0]=[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):((0,s.uX)(),(0,s.CE)("svg",b,d[1]||(d[1]=[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7l5 5m0 0l-5 5m5-5H6"},null,-1)])))],10,h)]),(0,s.Lk)("div",y,[(0,s.Lk)("div",x,[(0,s.bo)((0,s.Lk)("input",{"onUpdate:modelValue":r=>t[e.id]=r,onKeyup:(0,a.jR)(r=>o(e),["enter"]),type:"text",class:"flex-1 px-2.5 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg text-xs text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200",placeholder:`Add new ${e.name.toLowerCase()}`},null,40,w),[[a.Jo,t[e.id]]]),(0,s.Lk)("button",{onClick:r=>o(e),class:"px-2.5 py-1.5 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-lg text-xs font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200",title:"Add item"},d[2]||(d[2]=[(0,s.Lk)("svg",{class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)]),8,L),e.items.length>0?((0,s.uX)(),(0,s.CE)("button",{key:0,onClick:r=>R(e),class:"px-2.5 py-1.5 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg text-xs font-medium hover:bg-red-100 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200",title:"Remove selected"},d[3]||(d[3]=[(0,s.Lk)("svg",{class:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)]),8,P)):(0,s.Q3)("",!0)])])])]))),128)),d[7]||(d[7]=(0,s.Fv)('<div class="relative" data-v-0e1273f5><div class="absolute inset-0 flex items-center" data-v-0e1273f5><div class="w-full border-t border-gray-300 dark:border-gray-600" data-v-0e1273f5></div></div><div class="relative flex justify-center" data-v-0e1273f5><span class="px-3 bg-white dark:bg-black text-xs font-medium text-gray-500 dark:text-gray-400" data-v-0e1273f5>Options</span></div></div>',1)),(0,s.Lk)("div",C,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)((0,i.R1)(r).checkboxes,e=>((0,s.uX)(),(0,s.CE)("div",{key:e.id,class:"group"},[(0,s.Lk)("div",j,[(0,s.Lk)("div",E,[(0,s.bF)((0,i.R1)(l.y8v),{checked:e.checked,"onUpdate:checked":r=>e.checked=r,id:e.id,class:"flex items-center justify-center w-5 h-5 bg-white dark:bg-black border-2 border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 hover:border-primary-400 dark:hover:border-primary-400 data-[state=checked]:bg-primary-600 data-[state=checked]:border-primary-600 dark:data-[state=checked]:bg-primary-500 dark:data-[state=checked]:border-primary-500"},{default:(0,s.k6)(()=>[(0,s.bF)((0,i.R1)(l.P7L),null,{default:(0,s.k6)(()=>d[4]||(d[4]=[(0,s.Lk)("svg",{class:"w-3 h-3 text-white",viewBox:"0 0 20 20",fill:"currentColor"},[(0,s.Lk)("path",{"fill-rule":"evenodd",d:"M16.707 6.293a1 1 0 00-1.414 0L9 12.586l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z","clip-rule":"evenodd"})],-1)])),_:1,__:[4]})]),_:2},1032,["checked","onUpdate:checked","id"]),(0,s.Lk)("label",{for:e.id,class:"text-xs font-medium text-gray-800 dark:text-white cursor-pointer select-none"},(0,n.v_)(e.label),9,M)]),(0,s.Lk)("button",{onClick:r=>X(e),class:(0,n.C4)(["px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transform hover:scale-105 active:scale-95",e.used?"bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-200 dark:hover:bg-emerald-900/50 focus:ring-emerald-500":"bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:ring-purple-500"]),disabled:!e.checked},[(0,s.Lk)("span",_,[e.used?((0,s.uX)(),(0,s.CE)("svg",A,d[5]||(d[5]=[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):((0,s.uX)(),(0,s.CE)("svg",F,d[6]||(d[6]=[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},null,-1)]))),(0,s.Lk)("span",null,(0,n.v_)(e.used?"Added":"Add"),1)])],10,I)])]))),128))])]))}};var T=t(262);const X=(0,T.A)(R,[["__scopeId","data-v-0e1273f5"]]),B=X,S={class:"flex flex-col h-full bg-white dark:bg-black"},V={class:"p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black"},H={class:"flex items-center justify-between"},O={class:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-300"},D={class:"flex-1 p-6 min-h-0"},U={class:"relative h-full"},$={key:0,class:"absolute inset-0 flex items-center justify-center pointer-events-none"},K={class:"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-black"},z={class:"flex flex-wrap gap-3"},N=["disabled"],Q=["disabled"],J=["disabled"],Y={class:"flex-1 p-6 border-t border-gray-200 dark:border-gray-700 min-h-0 bg-gray-50 dark:bg-gray-800/50"},q={class:"h-full flex flex-col"},G={class:"flex items-center justify-between mb-6"},W={class:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"},Z={class:"flex-1 overflow-y-auto space-y-3 min-h-0 custom-scrollbar"},ee={key:0,class:"flex flex-col items-center justify-center h-full text-center space-y-4 opacity-60"},re=["onClick"],te={class:"flex justify-between items-start mb-3"},ae={class:"flex items-center space-x-2"},oe={class:"text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wide"},se=["onClick"],ie={class:"text-sm text-gray-700 dark:text-white line-clamp-4 leading-relaxed"},ne={class:"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-600"},de={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},le={__name:"MainPanel",setup(e){const r=d(),t=()=>{r.mainPrompt.trim()?navigator.clipboard.writeText(r.mainPrompt).then(()=>{alert("Prompt copied to clipboard!")}).catch(e=>{console.error("Failed to copy text: ",e),alert("Failed to copy prompt.")}):alert("No prompt to copy!")},o=()=>{r.mainPrompt.trim()&&!confirm("Are you sure you want to clear the prompt?")||r.clearMainPrompt()},l=async()=>{if(!r.mainPrompt.trim())return void alert("No prompt to save!");const e=await r.saveMainPromptToList();e?alert("Prompt saved successfully!"):alert("Failed to save prompt.")},c=e=>{if(r.mainPrompt.trim()&&!confirm("Loading a saved prompt will replace the current content. Continue?"))return;const t=r.loadPromptFromSaved(e);t||alert("Failed to load prompt.")},p=e=>{if(!confirm("Are you sure you want to delete this saved prompt?"))return;const t=r.removeSavedPrompt(e);t||alert("Failed to remove prompt.")},u=e=>{const r=new Date(e);return r.toLocaleDateString()+" "+r.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})};return(e,d)=>((0,s.uX)(),(0,s.CE)("div",S,[(0,s.Lk)("div",V,[(0,s.Lk)("div",H,[d[2]||(d[2]=(0,s.Lk)("h2",{class:"text-xl font-bold text-gray-900 dark:text-white"},"Prompt Editor",-1)),(0,s.Lk)("div",O,[(0,s.Lk)("span",null,(0,n.v_)((0,i.R1)(r).mainPrompt.length)+" characters",1),d[1]||(d[1]=(0,s.Lk)("span",null,"•",-1)),(0,s.Lk)("span",null,(0,n.v_)((0,i.R1)(r).mainPrompt.split("\n").length)+" lines",1)])])]),(0,s.Lk)("div",D,[(0,s.Lk)("div",U,[(0,s.bo)((0,s.Lk)("textarea",{"onUpdate:modelValue":d[0]||(d[0]=e=>(0,i.R1)(r).mainPrompt=e),class:"w-full h-full p-4 bg-gray-50 dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 shadow-sm hover:shadow-md font-mono text-sm leading-relaxed",placeholder:"Your crafted prompt will appear here...\n\nStart by selecting categories and options from the left panel to build your prompt."},null,512),[[a.Jo,(0,i.R1)(r).mainPrompt]]),(0,i.R1)(r).mainPrompt.trim()?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.CE)("div",$,d[3]||(d[3]=[(0,s.Fv)('<div class="text-center space-y-3 opacity-50" data-v-d907fff0><div class="w-16 h-16 mx-auto bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center" data-v-d907fff0><svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d907fff0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" data-v-d907fff0></path></svg></div><p class="text-gray-500 dark:text-gray-400 font-medium" data-v-d907fff0>Start crafting your prompt</p></div>',1)])))])]),(0,s.Lk)("div",K,[(0,s.Lk)("div",z,[(0,s.Lk)("button",{onClick:t,disabled:!(0,i.R1)(r).mainPrompt.trim(),class:"flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"},d[4]||(d[4]=[(0,s.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),(0,s.Lk)("span",null,"Copy",-1)]),8,N),(0,s.Lk)("button",{onClick:o,disabled:!(0,i.R1)(r).mainPrompt.trim(),class:"flex items-center space-x-2 px-6 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"},d[5]||(d[5]=[(0,s.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),(0,s.Lk)("span",null,"Clear",-1)]),8,Q),(0,s.Lk)("button",{onClick:l,disabled:!(0,i.R1)(r).mainPrompt.trim(),class:"flex items-center space-x-2 px-6 py-2 bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"},d[6]||(d[6]=[(0,s.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"})],-1),(0,s.Lk)("span",null,"Save",-1)]),8,J)])]),(0,s.Lk)("div",Y,[(0,s.Lk)("div",q,[(0,s.Lk)("div",G,[d[8]||(d[8]=(0,s.Lk)("h3",{class:"text-lg font-bold text-gray-900 dark:text-white"},"Saved Prompts",-1)),(0,s.Lk)("div",W,[d[7]||(d[7]=(0,s.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10"})],-1)),(0,s.Lk)("span",null,(0,n.v_)((0,i.R1)(r).savedPrompts.length)+" saved",1)])]),(0,s.Lk)("div",Z,[0===(0,i.R1)(r).savedPrompts.length?((0,s.uX)(),(0,s.CE)("div",ee,d[9]||(d[9]=[(0,s.Fv)('<div class="w-20 h-20 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center" data-v-d907fff0><svg class="w-10 h-10 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d907fff0><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10" data-v-d907fff0></path></svg></div><div class="space-y-2" data-v-d907fff0><p class="text-gray-600 dark:text-gray-300 font-medium" data-v-d907fff0>No saved prompts yet</p><p class="text-sm text-gray-500 dark:text-gray-500" data-v-d907fff0>Save your first prompt using the Save button above</p></div>',2)]))):(0,s.Q3)("",!0),((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)((0,i.R1)(r).savedPrompts,e=>((0,s.uX)(),(0,s.CE)("div",{key:e.id,class:"group relative p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-600 rounded-xl hover:border-gray-300 dark:hover:border-gray-500 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md hover:scale-[1.01] active:scale-[0.99]",onClick:r=>c(e.id)},[(0,s.Lk)("div",te,[(0,s.Lk)("div",ae,[d[10]||(d[10]=(0,s.Lk)("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),(0,s.Lk)("span",oe,(0,n.v_)(u(e.createdAt)),1)]),(0,s.Lk)("button",{onClick:(0,a.D$)(r=>p(e.id),["stop"]),class:"opacity-0 group-hover:opacity-100 p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-700",title:"Remove prompt"},d[11]||(d[11]=[(0,s.Lk)("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,se)]),(0,s.Lk)("div",ie,(0,n.v_)(e.content),1),(0,s.Lk)("div",ne,[(0,s.Lk)("div",de,[d[12]||(d[12]=(0,s.Lk)("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[(0,s.Lk)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})],-1)),(0,s.Lk)("span",null,(0,n.v_)(e.content.length)+" chars",1)]),d[13]||(d[13]=(0,s.Lk)("div",{class:"text-xs text-primary-600 dark:text-primary-400 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200"}," Click to load ",-1))])],8,re))),128))])])])]))}},ce=(0,T.A)(le,[["__scopeId","data-v-d907fff0"]]),pe=ce,ue=(0,i.KR)(!0);function me(){const e=()=>{},r=e=>{},t=()=>{const e=document.documentElement;e.classList.add("dark")},a=()=>{ue.value=!0,t()},o=()=>(a(),()=>{});return{isDark:(0,i.tB)(ue),toggleTheme:e,setTheme:r,initTheme:o}}const{isDark:fe,toggleTheme:ke,setTheme:ge,initTheme:he}=me(),ve={id:"app",class:"flex h-screen bg-gray-50 dark:bg-black transition-colors duration-200"},be={class:"w-1/3 max-w-sm min-w-[320px] overflow-y-auto bg-white dark:bg-black shadow-lg dark:shadow-soft-dark border-r border-gray-200 dark:border-gray-700"},ye={class:"flex-1 bg-gray-50 dark:bg-black"},xe={__name:"App",setup(e){return(0,s.sV)(()=>{he()}),(e,r)=>((0,s.uX)(),(0,s.CE)("div",ve,[(0,s.Lk)("div",be,[r[0]||(r[0]=(0,s.Lk)("div",{class:"flex items-center justify-center p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black"},[(0,s.Lk)("h1",{class:"text-base font-semibold text-gray-900 dark:text-white"},"Prompt Crafter")],-1)),(0,s.bF)(B)]),(0,s.Lk)("div",ye,[(0,s.bF)(pe)])]))}},we=xe,Le=we,Pe=(0,a.Ef)(Le);Pe.use((0,o.Ey)()),Pe.mount("#app")}},r={};function t(a){var o=r[a];if(void 0!==o)return o.exports;var s=r[a]={exports:{}};return e[a](s,s.exports,t),s.exports}t.m=e,(()=>{var e=[];t.O=(r,a,o,s)=>{if(!a){var i=1/0;for(c=0;c<e.length;c++){for(var[a,o,s]=e[c],n=!0,d=0;d<a.length;d++)(!1&s||i>=s)&&Object.keys(t.O).every(e=>t.O[e](a[d]))?a.splice(d--,1):(n=!1,s<i&&(i=s));if(n){e.splice(c--,1);var l=o();void 0!==l&&(r=l)}}return r}s=s||0;for(var c=e.length;c>0&&e[c-1][2]>s;c--)e[c]=e[c-1];e[c]=[a,o,s]}})(),(()=>{t.d=(e,r)=>{for(var a in r)t.o(r,a)&&!t.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:r[a]})}})(),(()=>{t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r)})(),(()=>{var e={524:0};t.O.j=r=>0===e[r];var r=(r,a)=>{var o,s,[i,n,d]=a,l=0;if(i.some(r=>0!==e[r])){for(o in n)t.o(n,o)&&(t.m[o]=n[o]);if(d)var c=d(t)}for(r&&r(a);l<i.length;l++)s=i[l],t.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return t.O(c)},a=self["webpackChunkprompt_crafter"]=self["webpackChunkprompt_crafter"]||[];a.forEach(r.bind(null,0)),a.push=r.bind(null,a.push.bind(a))})();var a=t.O(void 0,[504],()=>t(639));a=t.O(a)})();
//# sourceMappingURL=app.005b9d85.js.map