{"version": 3, "file": "js/app.005b9d85.js", "mappings": "wFAEO,MAAMA,GAAc,QAAY,MAAO,CAC5CC,MAAO,KAAM,CACXC,eAAgB,CAAC,EACjBC,WAAY,GACZC,OAAQ,GACRC,WAAY,GACZC,WAAY,GACZC,WAAY,GACZC,UAAW,IAAIC,IACfC,WAAW,EACXC,aAAc,KAGhBC,QAAS,CACP,kBAAAC,CAAmBC,GACZA,IACDC,KAAKZ,WAAWa,OAAS,EAC3BD,KAAKZ,YAAc,KAAOW,EAE1BC,KAAKZ,WAAaW,EAEtB,EAEA,2BAAAG,CAA4BC,EAAcC,GACxC,IAAKA,IAAYD,EAAc,OAE/B,MAAME,EAAgB,KAAKF,QAAmBC,IAE1CJ,KAAKZ,WAAWa,OAAS,EAC3BD,KAAKZ,YAAc,KAAOiB,EAE1BL,KAAKZ,WAAaiB,CAEtB,EAEA,SAAAC,CAAUP,GACRC,KAAKX,OAASU,CAChB,EAEA,cAAAQ,CAAeR,EAAMS,GACfT,IACFC,KAAKX,SAAWW,KAAKX,OAAS,OAAS,IAAMU,EAC7CC,KAAKP,UAAUgB,IAAID,GAEvB,EAEA,WAAAE,GACEV,KAAKX,OAAS,GACdW,KAAKP,UAAUkB,QACfX,KAAKT,WAAWqB,QAAQC,IACtBA,EAAIC,SAAW,IAEnB,EAEA,eAAMC,GACJf,KAAKL,WAAY,EACjB,IACE,MAAOqB,EAAeC,EAAeC,SAA2BC,QAAQC,IAAI,CAC1EC,MAAM,oBAAoBC,MAAMC,GAAKA,GACrCF,MAAM,oBAAoBC,MAAMC,GAAKA,GACrCF,MAAM,yBAAyBC,MAAMC,GAAKA,KAG5C,GAAIP,EAAcQ,GAAI,CACpB,MAAMC,QAAuBT,EAAcU,OAC3C1B,KAAKT,WAAakC,EAAeE,IAAId,IAAO,IAAMA,EAAKC,SAAU,KACnE,CAUA,GARIG,EAAcO,KAChBxB,KAAKR,iBAAmByB,EAAcS,QAGpCR,EAAkBM,KACpBxB,KAAKb,qBAAuB+B,EAAkBQ,QAG5CE,OAAOC,aAAeD,OAAOC,YAAYC,SAAU,CACrD,MAAMC,QAAaH,OAAOC,YAAYC,WACtC,GAAIC,EAAM,CACR/B,KAAKJ,aAAemC,EAAKC,SAAW,GAEpC,IAAK,MAAMC,KAAYjC,KAAKT,WACtBwC,EAAKE,EAASC,KAAOH,EAAKE,EAASC,IAAIC,QACzCF,EAASE,MAAQJ,EAAKE,EAASC,IAAIC,MAGzC,CACF,MACEC,QAAQC,KAAK,gEAEjB,CAAE,MAAOC,GACPF,QAAQE,MAAM,8BAA+BA,EAC/C,CAAE,QACAtC,KAAKL,WAAY,CACnB,CACF,EAEA,gBAAM4C,GACJ,IAAKvC,KAAKX,OAAOmD,OAAQ,OAAO,EAChC,IACE,OAAIZ,OAAOC,aAAeD,OAAOC,YAAYU,iBAC9BX,OAAOC,YAAYU,WAAWvC,KAAKX,SAElD+C,QAAQC,KAAK,yBACN,EACT,CAAE,MAAOC,GAEP,OADAF,QAAQE,MAAM,uBAAwBA,IAC/B,CACT,CACF,EAEA,qBAAMG,GACJ,IAAKzC,KAAKX,OAAQ,OAAO,EACzB,IAEE,aADMqD,UAAUC,UAAUC,UAAU5C,KAAKX,SAClC,CACT,CAAE,MAAOiD,GAEP,OADAF,QAAQE,MAAM,8BAA+BA,IACtC,CACT,CACF,EAEA,aAAAO,CAAcC,GACZ9C,KAAKV,WAAawD,EAClB9C,KAAKe,WACP,EAEA,UAAAgC,CAAWvC,GACT,OAAOR,KAAKP,UAAUuD,IAAIxC,EAC5B,EAGA,eAAAyC,GACEjD,KAAKZ,WAAa,EACpB,EAEA,0BAAM8D,GACJ,IAAKlD,KAAKZ,WAAWoD,OAAQ,OAAO,EAEpC,MAAMW,EAAY,CAChBjB,GAAIkB,KAAKC,MACTjD,QAASJ,KAAKZ,WAAWoD,OACzBc,WAAW,IAAIF,MAAOG,cACtBC,QAASxD,KAAKZ,WAAWoD,OAAOiB,UAAU,EAAG,MAAQzD,KAAKZ,WAAWoD,OAAOvC,OAAS,IAAM,MAAQ,KAGrGD,KAAKJ,aAAa8D,QAAQP,GAG1B,IAIE,OAHIvB,OAAOC,aAAeD,OAAOC,YAAYU,kBACrCX,OAAOC,YAAYU,WAAWvC,KAAKZ,aAEpC,CACT,CAAE,MAAOkD,GAEP,OADAF,QAAQE,MAAM,+BAAgCA,IACvC,CACT,CACF,EAEA,mBAAAqB,CAAoBC,GAClB,MAAMC,EAAc7D,KAAKJ,aAAakE,KAAKC,GAAKA,EAAE7B,KAAO0B,GACzD,QAAIC,IACF7D,KAAKZ,WAAayE,EAAYzD,SACvB,EAGX,EAEA,iBAAA4D,CAAkBJ,GAChB,MAAMK,EAAQjE,KAAKJ,aAAasE,UAAUH,GAAKA,EAAE7B,KAAO0B,GACxD,OAAe,IAAXK,IACFjE,KAAKJ,aAAauE,OAAOF,EAAO,IACzB,EAGX,K,wmCCxCJ,MAAMG,EAAQnF,IAERoF,GAAW,QAAS,CAAC,GAErBC,EAAmBrC,IACvB,MAAMsC,GAAOF,EAASpC,EAASC,KAAO,IAAIM,OACtC+B,IAAQtC,EAASE,MAAMqC,SAASD,KAClCtC,EAASE,MAAMsC,KAAKF,GACpBF,EAASpC,EAASC,IAAM,KAItBwC,EAAsBzC,IAC1B,GAAIA,EAASnB,SAAU,CACrB,MAAM6D,EAAM1C,EAASE,MAAMyC,QAAQ3C,EAASnB,WAC/B,IAAT6D,IACF1C,EAASE,MAAMgC,OAAOQ,EAAK,GAC3B1C,EAASnB,SAAW,GAExB,GAGI+D,EAAa5C,IACjB,GAAIA,EAASnB,SAAU,CACrB,MAAMN,EAAM,YAAYyB,EAASC,KAEjCkC,EAAMlE,4BAA4B+B,EAAS6C,KAAM7C,EAASnB,UAE1DsD,EAAM3E,UAAUgB,IAAID,EACtB,GAGIuE,EAAqBC,IACrBA,EAASC,UAEXb,EAAMlE,4BAA4B,eAAgB8E,EAASE,OAE3Dd,EAAM3E,UAAUgB,IAAI,YAAYuE,EAAS9C,MACzC8C,EAASG,MAAO,I,OAIpB,QAAU,KACRf,EAAMrD,aAEN,QAAM,IAAMqD,EAAM7E,WAAa6F,IAC7BA,EAAcxE,QAAQC,SACKwE,IAArBhB,EAASxD,EAAIqB,MACfmC,EAASxD,EAAIqB,IAAM,OAGtB,CAAEoD,WAAW,EAAMC,MAAM,M,oBA5L5B,QAiIM,MAjIN,EAiIM,G,aA9HJ,QAyEM,oBAzEkB,QAAAnB,GAAM7E,WAAlB0C,K,WAAZ,QAyEM,OAzEqCzB,IAAKyB,EAASC,GAAIsD,MAAM,a,EACjE,QAEQ,SAFAC,IAAKxD,EAASC,GAAIsD,MAAM,4E,QAC3BvD,EAAS6C,MAAI,MAElB,QAoEM,MApEN,EAoEM,EAlEJ,QAgCM,MAhCN,EAgCM,E,SA/BJ,QAYS,UAXN5C,GAAID,EAASC,G,yBACLD,EAAiB,WAC1BuD,OAAK,SAAC,0WAAyW,C,4FAC3P,WAAM,WAAU,YAAa,EAAS,U,EAI1J,QAA2G,SAA3G,EAA0D,WAAO,QAAGvD,EAAS6C,KAAKY,eAAW,K,aAC7F,QAES,mBAFuBzD,EAASE,MAAK,CAA9BwD,EAAM1B,M,WAAtB,QAES,UAFwCzD,IAAKyD,EAAQ2B,MAAOD,EAAMH,MAAM,kC,QAC5EG,GAAI,O,oBARA1D,EAASnB,aAapB,QAeS,UAdN,QAAK,GAAE+D,EAAU5C,GAClBuD,OAAK,SAAC,8TAA6T,C,mIACxK,WAAM,WAAU,YAAa,EAAS,SAGhMK,UAAW5D,EAASnB,SACpBgF,OAAO,QAAA1B,GAAMrB,WAAW,YAAYd,EAASC,MAAQ,kBAAoB,iB,EAE/D,QAAAkC,GAAMrB,WAAW,YAAYd,EAASC,Q,WAAjD,QAEM,MAFN,EAEM,cADJ,QAA0F,QAApF,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAI6D,EAAE,kB,0BAE1E,QAEM,MAFN,EAEM,cADJ,QAAoG,QAA9F,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIA,EAAE,4B,uBAK9E,QA+BM,MA/BN,EA+BM,EA5BJ,QA2BM,MA3BN,EA2BM,E,SA1BJ,QAME,S,yBALS1B,EAASpC,EAASC,IAAE,EAC5B,SAAK,WAAQoC,EAAgBrC,GAAQ,WACtC+D,KAAK,OACLR,MAAM,gWACLS,YAAW,WAAahE,EAAS6C,KAAKY,iB,kBAJ9BrB,EAASpC,EAASC,QAM7B,QAQS,UAPN,QAAK,GAAEoC,EAAgBrC,GACxBuD,MAAM,8SACNM,MAAM,Y,cAEN,QAEM,OAFDN,MAAM,cAAcU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EACjE,QAAsG,QAAhG,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,iC,UAIpE9D,EAASE,MAAMlC,OAAS,I,WADhC,QASS,U,MAPN,QAAK,GAAEyE,EAAmBzC,GAC3BuD,MAAM,wRACNM,MAAM,mB,cAEN,QAEM,OAFDN,MAAM,cAAcU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EACjE,QAAoF,QAA9E,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,e,+cAmBpF,QAuCM,MAvCN,EAuCM,G,aAtCJ,QAqCM,oBArCkB,QAAA3B,GAAM5E,WAAlBwF,K,WAAZ,QAqCM,OArCqCxE,IAAKwE,EAAS9C,GAAIsD,MAAM,S,EACjE,QAmCM,MAnCN,EAmCM,EAlCJ,QAeM,MAfN,EAeM,EAdJ,SAUe,gBATLP,QAASD,EAASC,Q,sBAATD,EAAgB,UAChC9C,GAAI8C,EAAS9C,GACdsD,MAAM,4d,kBAEN,IAIoB,EAJpB,SAIoB,qB,iBAHlB,IAEM,cAFN,QAEM,OAFDA,MAAM,qBAAqBY,QAAQ,YAAYF,KAAK,gB,EACvD,QAAuK,QAAjK,YAAU,UAAUH,EAAE,qHAAqH,YAAU,c,qEAIjK,QAEQ,SAFAN,IAAKT,EAAS9C,GAAIsD,MAAM,iF,QAC3BR,EAASE,OAAK,QAGrB,QAiBS,UAhBN,QAAK,GAAEH,EAAkBC,GAC1BQ,OAAK,SAAC,uMACER,EAASG,K,2SAGhBU,UAAWb,EAASC,S,EAErB,QAQO,OARP,EAQO,CAPMD,EAAa,O,WAAxB,QAEM,MAFN,EAEM,cADJ,QAA0F,QAApF,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIe,EAAE,kB,0BAE1E,QAEM,MAFN,EAEM,cADJ,QAAsG,QAAhG,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIA,EAAE,8B,cAE1E,QAAkD,qBAAzCf,EAASG,KAAO,QAAU,OAAb,M,sCCtHpC,MAAMkB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,I,6/CCwJA,MAAMjC,EAAQnF,IAERwD,EAAkB,KACjB2B,EAAMhF,WAAWoD,OAKtBE,UAAUC,UAAUC,UAAUwB,EAAMhF,YAAYkH,KAAK,KACnDC,MAAM,iCACLjF,MAAMkF,IACPpE,QAAQE,MAAM,wBAAyBkE,GACvCD,MAAM,4BARNA,MAAM,uBAYJ7F,EAAc,KACd0D,EAAMhF,WAAWoD,SAAWiE,QAAQ,+CAGxCrC,EAAMnB,mBAGFV,EAAamE,UACjB,IAAKtC,EAAMhF,WAAWoD,OAEpB,YADA+D,MAAM,sBAIR,MAAMI,QAAgBvC,EAAMlB,uBACxByD,EACFJ,MAAM,8BAENA,MAAM,2BAIJK,EAAmBhD,IACvB,GAAIQ,EAAMhF,WAAWoD,SAAWiE,QAAQ,sEACtC,OAGF,MAAME,EAAUvC,EAAMT,oBAAoBC,GACrC+C,GACHJ,MAAM,2BAIJvC,EAAqBJ,IACzB,IAAK6C,QAAQ,sDACX,OAGF,MAAME,EAAUvC,EAAMJ,kBAAkBJ,GACnC+C,GACHJ,MAAM,6BAIJM,EAAcC,IAClB,MAAMC,EAAO,IAAI3D,KAAK0D,GACtB,OAAOC,EAAKC,qBAAuB,IAAMD,EAAKE,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,a,0BA5NhG,QAyJM,MAzJN,EAyJM,EAvJJ,QASM,MATN,EASM,EARJ,QAOM,MAPN,EAOM,C,aANJ,QAA8E,MAA1E3B,MAAM,mDAAkD,iBAAa,KACzE,QAIM,MAJN,EAIM,EAHJ,QAAqD,sBAA5C,QAAApB,GAAMhF,WAAWa,QAAS,cAAW,G,aAC9C,QAAc,YAAR,KAAC,KACP,QAA4D,sBAAnD,QAAAmE,GAAMhF,WAAWgI,MAAM,MAAMnH,QAAS,SAAM,UAM3D,QAsBM,MAtBN,EAsBM,EArBJ,QAoBM,MApBN,EAoBM,E,SAnBJ,QAMY,Y,sCALD,QAAAmE,GAAgB,cACzBoB,MAAM,+ZACNS,YAAY,kI,kBAFH,QAAA7B,GAAMhF,eAQL,QAAAgF,GAAMhF,WAAWoD,Q,4BAA7B,QASM,MATN,EASM,c,2pBAKV,QAmCM,MAnCN,EAmCM,EAlCJ,QAiCM,MAjCN,EAiCM,EAhCJ,QASS,UARN,QAAOC,EACPoD,WAAW,QAAAzB,GAAMhF,WAAWoD,OAC7BgD,MAAM,6a,cAEN,QAEM,OAFDA,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAAiM,QAA3L,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,4H,IAE1E,QAAiB,YAAX,QAAI,K,MAGZ,QASS,UARN,QAAOrF,EACPmF,WAAW,QAAAzB,GAAMhF,WAAWoD,OAC7BgD,MAAM,ma,cAEN,QAEM,OAFDA,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAAwM,QAAlM,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,mI,IAE1E,QAAkB,YAAZ,SAAK,K,MAGb,QASS,UARN,QAAOxD,EACPsD,WAAW,QAAAzB,GAAMhF,WAAWoD,OAC7BgD,MAAM,kb,cAEN,QAEM,OAFDA,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAAuK,QAAjK,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,kG,IAE1E,QAAiB,YAAX,QAAI,K,UAMhB,QA2EM,MA3EN,EA2EM,EA1EJ,QAyEM,MAzEN,EAyEM,EAxEJ,QAQM,MARN,EAQM,C,aAPJ,QAA8E,MAA1EP,MAAM,mDAAkD,iBAAa,KACzE,QAKM,MALN,EAKM,C,aAJJ,QAEM,OAFDA,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAA8M,QAAxM,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,yI,KAE1E,QAAkD,sBAAzC,QAAA3B,GAAMxE,aAAaK,QAAS,SAAM,QAI/C,QA6DM,MA7DN,EA6DM,CA1DkC,KAA9B,QAAAmE,GAAMxE,aAAaK,S,WAD3B,QAaM,MAbN,GAaM,c,wxBAGN,QA0CM,oBAzCa,QAAAmE,GAAMxE,aAAhBP,K,WADT,QA0CM,OAxCHmB,IAAKnB,EAAO6C,GACbsD,MAAM,gQACL,QAAK,GAAEoB,EAAgBvH,EAAO6C,K,EAG/B,QAgBM,MAhBN,GAgBM,EAfJ,QAKM,MALN,GAKM,C,eAJJ,QAAsD,OAAjDsD,MAAM,sCAAoC,WAC/C,QAEO,OAFP,IAEO,QADFqB,EAAWxH,EAAOiE,YAAS,MAGlC,QAQS,UAPN,SAAK,WAAOU,EAAkB3E,EAAO6C,IAAE,UACxCsD,MAAM,wRACNM,MAAM,iB,gBAEN,QAEM,OAFDN,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAAwM,QAAlM,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,mI,cAM9E,QAEM,MAFN,IAEM,QADD1G,EAAOe,SAAO,IAInB,QAUM,MAVN,GAUM,EATJ,QAKM,MALN,GAKM,C,eAJJ,QAEM,OAFDoF,MAAM,UAAUU,KAAK,OAAOC,OAAO,eAAeC,QAAQ,a,EAC7D,QAAsL,QAAhL,iBAAe,QAAQ,kBAAgB,QAAQ,eAAa,IAAIL,EAAE,iH,KAE1E,QAA8C,qBAArC1G,EAAOe,QAAQH,QAAS,SAAM,K,eAEzC,QAEM,OAFDuF,MAAM,gIAA+H,mBAE1I,O,yBC9IR,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,MCNM6B,IAAS,SAAI,GAGZ,SAASC,KACd,MAAMC,EAAc,OAKdC,EAAYC,MAKZC,EAAa,KACjB,MAAMC,EAAOC,SAASC,gBAEtBF,EAAKG,UAAUrH,IAAI,SAQfsH,EAAY,KAEhBV,GAAOzB,OAAQ,EACf8B,KAGIM,EAAY,KAChBD,IAEO,QAGT,MAAO,CACLV,QAAQ,QAASA,IACjBE,cACAC,WACAQ,YAEJ,CAGA,MAAQX,OAAQY,GAAcV,YAAaW,GAAmBV,SAAUW,GAAgBH,UAAWI,IAAoBd,K,yUCxBvH,QAAU,KACR,O,oBAxBA,QAcM,MAdN,GAcM,EAZJ,QAMM,MANN,GAMM,C,aAJJ,QAEM,OAFD9B,MAAM,6GAA2G,EACpH,QAAqF,MAAjFA,MAAM,yDAAwD,oB,KAEpE,QAAa,MAIf,QAEM,MAFN,GAEM,EADJ,QAAa,Q,GCRb,GAAc,GAEpB,MCFM6C,IAAM,QAAUC,IACtBD,GAAIE,KAAI,WACRF,GAAIG,MAAM,O,GCNNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBtD,IAAjBuD,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CAGjDE,QAAS,CAAC,GAOX,OAHAE,EAAoBJ,GAAUG,EAAQA,EAAOD,QAASH,GAG/CI,EAAOD,OACf,CAGAH,EAAoBM,EAAID,E,MCzBxB,IAAIE,EAAW,GACfP,EAAoBQ,EAAI,CAACC,EAAQC,EAAUC,EAAIC,KAC9C,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASC,EAAI,EAAGA,EAAIR,EAAShJ,OAAQwJ,IAAK,CAGzC,IAFA,IAAKL,EAAUC,EAAIC,GAAYL,EAASQ,GACpCC,GAAY,EACPC,EAAI,EAAGA,EAAIP,EAASnJ,OAAQ0J,MACpB,EAAXL,GAAsBC,GAAgBD,IAAaM,OAAOC,KAAKnB,EAAoBQ,GAAGY,MAAOtJ,GAASkI,EAAoBQ,EAAE1I,GAAK4I,EAASO,KAC9IP,EAASjF,OAAOwF,IAAK,IAErBD,GAAY,EACTJ,EAAWC,IAAcA,EAAeD,IAG7C,GAAGI,EAAW,CACbT,EAAS9E,OAAOsF,IAAK,GACrB,IAAIM,EAAIV,SACEhE,IAAN0E,IAAiBZ,EAASY,EAC/B,CACD,CACA,OAAOZ,CAnBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAIG,EAAIR,EAAShJ,OAAQwJ,EAAI,GAAKR,EAASQ,EAAI,GAAG,GAAKH,EAAUG,IAAKR,EAASQ,GAAKR,EAASQ,EAAI,GACrGR,EAASQ,GAAK,CAACL,EAAUC,EAAIC,G,WCJ/BZ,EAAoB3C,EAAI,CAAC8C,EAASmB,KACjC,IAAI,IAAIxJ,KAAOwJ,EACXtB,EAAoBuB,EAAED,EAAYxJ,KAASkI,EAAoBuB,EAAEpB,EAASrI,IAC5EoJ,OAAOM,eAAerB,EAASrI,EAAK,CAAE2J,YAAY,EAAMC,IAAKJ,EAAWxJ,K,WCJ3EkI,EAAoB2B,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOtK,MAAQ,IAAIuK,SAAS,cAAb,EAChB,CAAE,MAAOhJ,GACR,GAAsB,kBAAXK,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,WCAxB8G,EAAoBuB,EAAI,CAACO,EAAKC,IAAUb,OAAOc,UAAUC,eAAeC,KAAKJ,EAAKC,E,WCKlF,IAAII,EAAkB,CACrB,IAAK,GAaNnC,EAAoBQ,EAAES,EAAKmB,GAA0C,IAA7BD,EAAgBC,GAGxD,IAAIC,EAAuB,CAACC,EAA4BjJ,KACvD,IAGI4G,EAAUmC,GAHT1B,EAAU6B,EAAaC,GAAWnJ,EAGhB0H,EAAI,EAC3B,GAAGL,EAAS+B,KAAMjJ,GAAgC,IAAxB2I,EAAgB3I,IAAa,CACtD,IAAIyG,KAAYsC,EACZvC,EAAoBuB,EAAEgB,EAAatC,KACrCD,EAAoBM,EAAEL,GAAYsC,EAAYtC,IAGhD,GAAGuC,EAAS,IAAI/B,EAAS+B,EAAQxC,EAClC,CAEA,IADGsC,GAA4BA,EAA2BjJ,GACrD0H,EAAIL,EAASnJ,OAAQwJ,IACzBqB,EAAU1B,EAASK,GAChBf,EAAoBuB,EAAEY,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOpC,EAAoBQ,EAAEC,IAG1BiC,EAAqBC,KAAK,8BAAgCA,KAAK,+BAAiC,GACpGD,EAAmBxK,QAAQmK,EAAqBO,KAAK,KAAM,IAC3DF,EAAmB3G,KAAOsG,EAAqBO,KAAK,KAAMF,EAAmB3G,KAAK6G,KAAKF,G,KC7CvF,IAAIG,EAAsB7C,EAAoBQ,OAAE7D,EAAW,CAAC,KAAM,IAAOqD,EAAoB,MAC7F6C,EAAsB7C,EAAoBQ,EAAEqC,E", "sources": ["webpack://prompt-crafter/./src/store/index.js", "webpack://prompt-crafter/./src/components/LeftPanel.vue", "webpack://prompt-crafter/./src/components/LeftPanel.vue?bfa8", "webpack://prompt-crafter/./src/components/MainPanel.vue", "webpack://prompt-crafter/./src/components/MainPanel.vue?6be2", "webpack://prompt-crafter/./src/composables/useTheme.js", "webpack://prompt-crafter/./src/App.vue", "webpack://prompt-crafter/./src/App.vue?7ccd", "webpack://prompt-crafter/./src/main.js", "webpack://prompt-crafter/webpack/bootstrap", "webpack://prompt-crafter/webpack/runtime/chunk loaded", "webpack://prompt-crafter/webpack/runtime/define property getters", "webpack://prompt-crafter/webpack/runtime/global", "webpack://prompt-crafter/webpack/runtime/hasOwnProperty shorthand", "webpack://prompt-crafter/webpack/runtime/jsonp chunk loading", "webpack://prompt-crafter/webpack/startup"], "sourcesContent": ["import { defineStore } from 'pinia';\n\nexport const useAppStore = defineStore('app', {\n  state: () => ({\n    promptElements: {},\n    mainPrompt: '',\n    prompt: '',\n    dataFolder: '',\n    categories: [],\n    checkboxes: [],\n    usedItems: new Set(),\n    isLoading: true,\n    savedPrompts: []\n  }),\n\n  actions: {\n    appendToMainPrompt(text) {\n      if (!text) return;\n      if (this.mainPrompt.length > 0) {\n        this.mainPrompt += '\\n' + text;\n      } else {\n        this.mainPrompt = text;\n      }\n    },\n\n    appendFormattedToMainPrompt(categoryName, content) {\n      if (!content || !categoryName) return;\n\n      const formattedText = `**${categoryName}**\\n${content}`;\n\n      if (this.mainPrompt.length > 0) {\n        this.mainPrompt += '\\n' + formattedText;\n      } else {\n        this.mainPrompt = formattedText;\n      }\n    },\n\n    setPrompt(text) {\n      this.prompt = text;\n    },\n\n    appendToPrompt(text, key) {\n      if (text) {\n        this.prompt += (this.prompt ? '\\n\\n' : '') + text;\n        this.usedItems.add(key);\n      }\n    },\n\n    clearPrompt() {\n      this.prompt = '';\n      this.usedItems.clear();\n      this.categories.forEach(cat => {\n        cat.selected = '';\n      });\n    },\n\n    async fetchData() {\n      this.isLoading = true;\n      try {\n        const [categoriesRes, checkboxesRes, promptElementsRes] = await Promise.all([\n          fetch('/categories.json').catch(e => e),\n          fetch('/checkboxes.json').catch(e => e),\n          fetch('/prompt_elements.json').catch(e => e)\n        ]);\n\n        if (categoriesRes.ok) {\n          const categoriesData = await categoriesRes.json();\n          this.categories = categoriesData.map(cat => ({ ...cat, selected: '' }));\n        }\n\n        if (checkboxesRes.ok) {\n          this.checkboxes = await checkboxesRes.json();\n        }\n\n        if (promptElementsRes.ok) {\n          this.promptElements = await promptElementsRes.json();\n        }\n\n        if (window.electronAPI && window.electronAPI.loadData) {\n          const data = await window.electronAPI.loadData();\n          if (data) {\n            this.savedPrompts = data.prompts || [];\n            // Assign items to categories\n            for (const category of this.categories) {\n              if (data[category.id] && data[category.id].items) {\n                category.items = data[category.id].items;\n              }\n            }\n          }\n        } else {\n          console.warn('window.electronAPI is not available. Running in browser mode.');\n        }\n      } catch (error) {\n        console.error('Error loading initial data:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async savePrompt() {\n      if (!this.prompt.trim()) return false;\n      try {\n        if (window.electronAPI && window.electronAPI.savePrompt) {\n          return await window.electronAPI.savePrompt(this.prompt);\n        }\n        console.warn('Mocking prompt save.');\n        return true;\n      } catch (error) {\n        console.error('Error saving prompt:', error);\n        return false;\n      }\n    },\n\n    async copyToClipboard() {\n      if (!this.prompt) return false;\n      try {\n        await navigator.clipboard.writeText(this.prompt);\n        return true;\n      } catch (error) {\n        console.error('Error copying to clipboard:', error);\n        return false;\n      }\n    },\n\n    setDataFolder(folder) {\n      this.dataFolder = folder;\n      this.fetchData();\n    },\n\n    isItemUsed(key) {\n      return this.usedItems.has(key);\n    },\n\n    // New actions for MainPanel functionality\n    clearMainPrompt() {\n      this.mainPrompt = '';\n    },\n\n    async saveMainPromptToList() {\n      if (!this.mainPrompt.trim()) return false;\n\n      const newPrompt = {\n        id: Date.now(),\n        content: this.mainPrompt.trim(),\n        createdAt: new Date().toISOString(),\n        preview: this.mainPrompt.trim().substring(0, 100) + (this.mainPrompt.trim().length > 100 ? '...' : '')\n      };\n\n      this.savedPrompts.unshift(newPrompt); // Add to beginning of array\n\n      // Also save to file system if available\n      try {\n        if (window.electronAPI && window.electronAPI.savePrompt) {\n          await window.electronAPI.savePrompt(this.mainPrompt);\n        }\n        return true;\n      } catch (error) {\n        console.error('Error saving prompt to file:', error);\n        return true; // Still return true since we saved to local state\n      }\n    },\n\n    loadPromptFromSaved(promptId) {\n      const savedPrompt = this.savedPrompts.find(p => p.id === promptId);\n      if (savedPrompt) {\n        this.mainPrompt = savedPrompt.content;\n        return true;\n      }\n      return false;\n    },\n\n    removeSavedPrompt(promptId) {\n      const index = this.savedPrompts.findIndex(p => p.id === promptId);\n      if (index !== -1) {\n        this.savedPrompts.splice(index, 1);\n        return true;\n      }\n      return false;\n    }\n  }\n});\n", "<template>\n  <div class=\"space-y-4 p-4\">\n\n    <!-- Categories Section -->\n    <div v-for=\"category in store.categories\" :key=\"category.id\" class=\"space-y-2\">\n      <label :for=\"category.id\" class=\"block text-xs font-semibold text-gray-800 dark:text-white tracking-wide\">\n        {{ category.name }}\n      </label>\n      <div class=\"space-y-2\">\n        <!-- Dropdown with Add to Prompt Button -->\n        <div class=\"flex items-center space-x-2\">\n          <select\n            :id=\"category.id\"\n            v-model=\"category.selected\"\n            class=\"flex-1 px-3 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500\"\n            :class=\"{\n              'border-emerald-500 dark:border-emerald-400 ring-2 ring-emerald-200 dark:ring-emerald-800': store.isItemUsed(`category-${category.id}`)\n            }\"\n          >\n            <option value=\"\" class=\"text-gray-500 dark:text-gray-400\">Select {{ category.name.toLowerCase() }}</option>\n            <option v-for=\"(item, index) in category.items\" :key=\"index\" :value=\"item\" class=\"text-gray-900 dark:text-white\">\n              {{ item }}\n            </option>\n          </select>\n\n          <!-- Add to Prompt Arrow Button -->\n          <button\n            @click=\"handleAdd(category)\"\n            class=\"px-2.5 py-1.5 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95\"\n            :class=\"{\n              'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-600 dark:hover:bg-emerald-700 ring-2 ring-emerald-200 dark:ring-emerald-800': store.isItemUsed(`category-${category.id}`)\n            }\"\n            :disabled=\"!category.selected\"\n            :title=\"store.isItemUsed(`category-${category.id}`) ? 'Added to prompt' : 'Add to prompt'\"\n          >\n            <svg v-if=\"store.isItemUsed(`category-${category.id}`)\" class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"/>\n            </svg>\n            <svg v-else class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7l5 5m0 0l-5 5m5-5H6\"/>\n            </svg>\n          </button>\n        </div>\n\n        <div class=\"flex flex-col space-y-2\">\n\n          <!-- Add/Remove Controls -->\n          <div class=\"flex items-center space-x-2\">\n            <input\n              v-model=\"newItems[category.id]\"\n              @keyup.enter=\"addDropdownItem(category)\"\n              type=\"text\"\n              class=\"flex-1 px-2.5 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg text-xs text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200\"\n              :placeholder=\"`Add new ${category.name.toLowerCase()}`\"\n            />\n            <button\n              @click=\"addDropdownItem(category)\"\n              class=\"px-2.5 py-1.5 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-lg text-xs font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200\"\n              title=\"Add item\"\n            >\n              <svg class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"/>\n              </svg>\n            </button>\n            <button\n              v-if=\"category.items.length > 0\"\n              @click=\"removeDropdownItem(category)\"\n              class=\"px-2.5 py-1.5 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg text-xs font-medium hover:bg-red-100 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200\"\n              title=\"Remove selected\"\n            >\n              <svg class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 12H4\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Divider -->\n    <div class=\"relative\">\n      <div class=\"absolute inset-0 flex items-center\">\n        <div class=\"w-full border-t border-gray-300 dark:border-gray-600\"></div>\n      </div>\n      <div class=\"relative flex justify-center\">\n        <span class=\"px-3 bg-white dark:bg-black text-xs font-medium text-gray-500 dark:text-gray-400\">Options</span>\n      </div>\n    </div>\n\n    <!-- Checkboxes Section -->\n    <div class=\"space-y-3\">\n      <div v-for=\"checkbox in store.checkboxes\" :key=\"checkbox.id\" class=\"group\">\n        <div class=\"flex items-center justify-between p-2.5 bg-gray-50 dark:bg-black rounded-lg border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-200 hover:shadow-sm\">\n          <div class=\"flex items-center space-x-3\">\n            <CheckboxRoot\n              v-model:checked=\"checkbox.checked\"\n              :id=\"checkbox.id\"\n              class=\"flex items-center justify-center w-5 h-5 bg-white dark:bg-black border-2 border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 hover:border-primary-400 dark:hover:border-primary-400 data-[state=checked]:bg-primary-600 data-[state=checked]:border-primary-600 dark:data-[state=checked]:bg-primary-500 dark:data-[state=checked]:border-primary-500\"\n            >\n              <CheckboxIndicator>\n                <svg class=\"w-3 h-3 text-white\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fill-rule=\"evenodd\" d=\"M16.707 6.293a1 1 0 00-1.414 0L9 12.586l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n              </CheckboxIndicator>\n            </CheckboxRoot>\n            <label :for=\"checkbox.id\" class=\"text-xs font-medium text-gray-800 dark:text-white cursor-pointer select-none\">\n              {{ checkbox.label }}\n            </label>\n          </div>\n          <button\n            @click=\"handleAddCheckbox(checkbox)\"\n            class=\"px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transform hover:scale-105 active:scale-95\"\n            :class=\"checkbox.used\n              ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-200 dark:hover:bg-emerald-900/50 focus:ring-emerald-500'\n              : 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:ring-purple-500'\"\n            :disabled=\"!checkbox.checked\"\n          >\n            <span class=\"flex items-center space-x-1\">\n              <svg v-if=\"checkbox.used\" class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"/>\n              </svg>\n              <svg v-else class=\"w-3.5 h-3.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"/>\n              </svg>\n              <span>{{ checkbox.used ? 'Added' : 'Add' }}</span>\n            </span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { reactive, onMounted, watch } from 'vue';\nimport { useAppStore } from '../store';\nimport { CheckboxRoot, CheckboxIndicator } from 'radix-vue';\n\nconst store = useAppStore();\n\nconst newItems = reactive({});\n\nconst addDropdownItem = (category) => {\n  const val = (newItems[category.id] || '').trim();\n  if (val && !category.items.includes(val)) {\n    category.items.push(val);\n    newItems[category.id] = '';\n  }\n};\n\nconst removeDropdownItem = (category) => {\n  if (category.selected) {\n    const idx = category.items.indexOf(category.selected);\n    if (idx !== -1) {\n      category.items.splice(idx, 1);\n      category.selected = '';\n    }\n  }\n};\n\nconst handleAdd = (category) => {\n  if (category.selected) {\n    const key = `category-${category.id}`;\n    // Add to the main prompt editor with formatted category name\n    store.appendFormattedToMainPrompt(category.name, category.selected);\n    // Also track usage for UI feedback\n    store.usedItems.add(key);\n  }\n};\n\nconst handleAddCheckbox = (checkbox) => {\n  if (checkbox.checked) {\n    // Add to the main prompt editor with formatted category name\n    store.appendFormattedToMainPrompt('Instructions', checkbox.label);\n    // Track usage for UI feedback\n    store.usedItems.add(`checkbox-${checkbox.id}`);\n    checkbox.used = true;\n  }\n};\n\nonMounted(() => {\n  store.fetchData();\n  \n  watch(() => store.categories, (newCategories) => {\n    newCategories.forEach(cat => {\n      if (newItems[cat.id] === undefined) {\n        newItems[cat.id] = '';\n      }\n    });\n  }, { immediate: true, deep: true });\n});\n</script>\n\n<style scoped>\n/* No additional styles needed as Tailwind CSS is used. */\n</style>\n", "import script from \"./LeftPanel.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./LeftPanel.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./LeftPanel.vue?vue&type=style&index=0&id=0e1273f5&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0e1273f5\"]])\n\nexport default __exports__", "<template>\n  <div class=\"flex flex-col h-full bg-white dark:bg-black\">\n    <!-- Header -->\n    <div class=\"p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black\">\n      <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-xl font-bold text-gray-900 dark:text-white\">Prompt Editor</h2>\n        <div class=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-300\">\n          <span>{{ store.mainPrompt.length }} characters</span>\n          <span>•</span>\n          <span>{{ store.mainPrompt.split('\\n').length }} lines</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Prompt Editor -->\n    <div class=\"flex-1 p-6 min-h-0\">\n      <div class=\"relative h-full\">\n        <textarea\n          v-model=\"store.mainPrompt\"\n          class=\"w-full h-full p-4 bg-gray-50 dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 shadow-sm hover:shadow-md font-mono text-sm leading-relaxed\"\n          placeholder=\"Your crafted prompt will appear here...\n\nStart by selecting categories and options from the left panel to build your prompt.\"\n        ></textarea>\n\n        <!-- Floating action hint -->\n        <div v-if=\"!store.mainPrompt.trim()\" class=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\n          <div class=\"text-center space-y-3 opacity-50\">\n            <div class=\"w-16 h-16 mx-auto bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n              <svg class=\"w-8 h-8 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"/>\n              </svg>\n            </div>\n            <p class=\"text-gray-500 dark:text-gray-400 font-medium\">Start crafting your prompt</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Action Buttons -->\n    <div class=\"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-black\">\n      <div class=\"flex flex-wrap gap-3\">\n        <button\n          @click=\"copyToClipboard\"\n          :disabled=\"!store.mainPrompt.trim()\"\n          class=\"flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"/>\n          </svg>\n          <span>Copy</span>\n        </button>\n\n        <button\n          @click=\"clearPrompt\"\n          :disabled=\"!store.mainPrompt.trim()\"\n          class=\"flex items-center space-x-2 px-6 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"/>\n          </svg>\n          <span>Clear</span>\n        </button>\n\n        <button\n          @click=\"savePrompt\"\n          :disabled=\"!store.mainPrompt.trim()\"\n          class=\"flex items-center space-x-2 px-6 py-2 bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4\"/>\n          </svg>\n          <span>Save</span>\n        </button>\n      </div>\n    </div>\n\n    <!-- Saved Prompts List -->\n    <div class=\"flex-1 p-6 border-t border-gray-200 dark:border-gray-700 min-h-0 bg-gray-50 dark:bg-gray-800/50\">\n      <div class=\"h-full flex flex-col\">\n        <div class=\"flex items-center justify-between mb-6\">\n          <h3 class=\"text-lg font-bold text-gray-900 dark:text-white\">Saved Prompts</h3>\n          <div class=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10\"/>\n            </svg>\n            <span>{{ store.savedPrompts.length }} saved</span>\n          </div>\n        </div>\n\n        <div class=\"flex-1 overflow-y-auto space-y-3 min-h-0 custom-scrollbar\">\n          <!-- Empty State -->\n          <div\n            v-if=\"store.savedPrompts.length === 0\"\n            class=\"flex flex-col items-center justify-center h-full text-center space-y-4 opacity-60\"\n          >\n            <div class=\"w-20 h-20 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center\">\n              <svg class=\"w-10 h-10 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10\"/>\n              </svg>\n            </div>\n            <div class=\"space-y-2\">\n              <p class=\"text-gray-600 dark:text-gray-300 font-medium\">No saved prompts yet</p>\n              <p class=\"text-sm text-gray-500 dark:text-gray-500\">Save your first prompt using the Save button above</p>\n            </div>\n          </div>\n\n          <!-- Saved Prompts -->\n          <div\n            v-for=\"prompt in store.savedPrompts\"\n            :key=\"prompt.id\"\n            class=\"group relative p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-600 rounded-xl hover:border-gray-300 dark:hover:border-gray-500 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md hover:scale-[1.01] active:scale-[0.99]\"\n            @click=\"loadSavedPrompt(prompt.id)\"\n          >\n            <!-- Header -->\n            <div class=\"flex justify-between items-start mb-3\">\n              <div class=\"flex items-center space-x-2\">\n                <div class=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                <span class=\"text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wide\">\n                  {{ formatDate(prompt.createdAt) }}\n                </span>\n              </div>\n              <button\n                @click.stop=\"removeSavedPrompt(prompt.id)\"\n                class=\"opacity-0 group-hover:opacity-100 p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-700\"\n                title=\"Remove prompt\"\n              >\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"/>\n                </svg>\n              </button>\n            </div>\n\n            <!-- Content Preview -->\n            <div class=\"text-sm text-gray-700 dark:text-white line-clamp-4 leading-relaxed\">\n              {{ prompt.content }}\n            </div>\n\n            <!-- Footer -->\n            <div class=\"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-600\">\n              <div class=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"/>\n                </svg>\n                <span>{{ prompt.content.length }} chars</span>\n              </div>\n              <div class=\"text-xs text-primary-600 dark:text-primary-400 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                Click to load\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { useAppStore } from '../store';\n\nconst store = useAppStore();\n\nconst copyToClipboard = () => {\n  if (!store.mainPrompt.trim()) {\n    alert('No prompt to copy!');\n    return;\n  }\n\n  navigator.clipboard.writeText(store.mainPrompt).then(() => {\n    alert('Prompt copied to clipboard!');\n  }).catch(err => {\n    console.error('Failed to copy text: ', err);\n    alert('Failed to copy prompt.');\n  });\n};\n\nconst clearPrompt = () => {\n  if (store.mainPrompt.trim() && !confirm('Are you sure you want to clear the prompt?')) {\n    return;\n  }\n  store.clearMainPrompt();\n};\n\nconst savePrompt = async () => {\n  if (!store.mainPrompt.trim()) {\n    alert('No prompt to save!');\n    return;\n  }\n\n  const success = await store.saveMainPromptToList();\n  if (success) {\n    alert('Prompt saved successfully!');\n  } else {\n    alert('Failed to save prompt.');\n  }\n};\n\nconst loadSavedPrompt = (promptId) => {\n  if (store.mainPrompt.trim() && !confirm('Loading a saved prompt will replace the current content. Continue?')) {\n    return;\n  }\n\n  const success = store.loadPromptFromSaved(promptId);\n  if (!success) {\n    alert('Failed to load prompt.');\n  }\n};\n\nconst removeSavedPrompt = (promptId) => {\n  if (!confirm('Are you sure you want to delete this saved prompt?')) {\n    return;\n  }\n\n  const success = store.removeSavedPrompt(promptId);\n  if (!success) {\n    alert('Failed to remove prompt.');\n  }\n};\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n};\n</script>\n\n<style scoped>\n/* Custom scrollbar for saved prompts list */\n.custom-scrollbar::-webkit-scrollbar {\n  width: 8px;\n}\n\n.custom-scrollbar::-webkit-scrollbar-track {\n  @apply bg-gray-100 dark:bg-black rounded-full;\n}\n\n.custom-scrollbar::-webkit-scrollbar-thumb {\n  @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;\n}\n\n/* Firefox scrollbar */\n.custom-scrollbar {\n  scrollbar-width: thin;\n  scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');\n}\n\n.dark .custom-scrollbar {\n  scrollbar-color: theme('colors.gray.600') theme('colors.black');\n}\n\n/* Line clamp utility */\n.line-clamp-4 {\n  display: -webkit-box;\n  -webkit-line-clamp: 4;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* Smooth transitions for interactive elements */\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n</style>\n", "import script from \"./MainPanel.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./MainPanel.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./MainPanel.vue?vue&type=style&index=0&id=d907fff0&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-d907fff0\"]])\n\nexport default __exports__", "import { ref, readonly } from 'vue';\n\nconst isDark = ref(true); // Always dark mode\nconst THEME_KEY = 'prompt-crafter-theme';\n\nexport function useTheme() {\n  const toggleTheme = () => {\n    // Theme toggle disabled - always dark mode\n    return;\n  };\n\n  const setTheme = (dark) => {\n    // Theme setting disabled - always dark mode\n    return;\n  };\n\n  const applyTheme = () => {\n    const html = document.documentElement;\n    // Always apply dark mode\n    html.classList.add('dark');\n  };\n\n  const saveTheme = () => {\n    // Theme saving disabled - always dark mode\n    return;\n  };\n\n  const loadTheme = () => {\n    // Theme loading disabled - always dark mode\n    isDark.value = true;\n    applyTheme();\n  };\n\n  const initTheme = () => {\n    loadTheme();\n    // No need to listen for system theme changes - always dark mode\n    return () => {};\n  };\n\n  return {\n    isDark: readonly(isDark),\n    toggleTheme,\n    setTheme,\n    initTheme\n  };\n}\n\n// Global theme state\nconst { isDark: globalIsDark, toggleTheme: globalToggleTheme, setTheme: globalSetTheme, initTheme: globalInitTheme } = useTheme();\n\nexport { globalIsDark as isDark, globalToggleTheme as toggleTheme, globalSetTheme as setTheme, globalInitTheme as initTheme };\n", "<template>\n  <div id=\"app\" class=\"flex h-screen bg-gray-50 dark:bg-black transition-colors duration-200\">\n    <!-- Left Panel -->\n    <div class=\"w-1/3 max-w-sm min-w-[320px] overflow-y-auto bg-white dark:bg-black shadow-lg dark:shadow-soft-dark border-r border-gray-200 dark:border-gray-700\">\n      <!-- Header -->\n      <div class=\"flex items-center justify-center p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black\">\n        <h1 class=\"text-base font-semibold text-gray-900 dark:text-white\">Prompt Crafter</h1>\n      </div>\n      <LeftPanel />\n    </div>\n\n    <!-- Main Panel -->\n    <div class=\"flex-1 bg-gray-50 dark:bg-black\">\n      <MainPanel />\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { onMounted } from 'vue';\nimport LeftPanel from './components/LeftPanel.vue';\nimport MainPanel from './components/MainPanel.vue';\nimport { initTheme } from './composables/useTheme';\n\nonMounted(() => {\n  initTheme();\n});\n</script>\n\n<style>\n/* Global styles */\n#app {\n  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Custom scrollbar styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  @apply bg-gray-100 dark:bg-black;\n}\n\n::-webkit-scrollbar-thumb {\n  @apply bg-gray-300 dark:bg-gray-600 rounded-full;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  @apply bg-gray-400 dark:bg-gray-500;\n}\n\n/* Firefox scrollbar */\n* {\n  scrollbar-width: thin;\n  scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');\n}\n\n.dark * {\n  scrollbar-color: theme('colors.gray.600') theme('colors.black');\n}\n</style>\n", "import script from \"./App.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./App.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=5a828f03&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nimport './assets/main.css';\n\nconst app = createApp(App);\napp.use(createPinia());\napp.mount('#app');\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkprompt_crafter\"] = self[\"webpackChunkprompt_crafter\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], () => (__webpack_require__(639)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["useAppStore", "state", "promptElements", "mainPrompt", "prompt", "dataFolder", "categories", "checkboxes", "usedItems", "Set", "isLoading", "savedPrompts", "actions", "appendToMainPrompt", "text", "this", "length", "appendFormattedToMainPrompt", "categoryName", "content", "formattedText", "setPrompt", "appendToPrompt", "key", "add", "clearPrompt", "clear", "for<PERSON>ach", "cat", "selected", "fetchData", "categoriesRes", "checkboxesRes", "promptElementsRes", "Promise", "all", "fetch", "catch", "e", "ok", "categoriesData", "json", "map", "window", "electronAPI", "loadData", "data", "prompts", "category", "id", "items", "console", "warn", "error", "savePrompt", "trim", "copyToClipboard", "navigator", "clipboard", "writeText", "setDataFolder", "folder", "isItemUsed", "has", "clearMainPrompt", "saveMainPromptToList", "newPrompt", "Date", "now", "createdAt", "toISOString", "preview", "substring", "unshift", "loadPromptFromSaved", "promptId", "savedPrompt", "find", "p", "removeSavedPrompt", "index", "findIndex", "splice", "store", "newItems", "addDropdownItem", "val", "includes", "push", "removeDropdownItem", "idx", "indexOf", "handleAdd", "name", "handleAddCheckbox", "checkbox", "checked", "label", "used", "newCategories", "undefined", "immediate", "deep", "class", "for", "toLowerCase", "item", "value", "disabled", "title", "d", "type", "placeholder", "fill", "stroke", "viewBox", "__exports__", "then", "alert", "err", "confirm", "async", "success", "loadSavedPrompt", "formatDate", "dateString", "date", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "split", "isDark", "useTheme", "toggleTheme", "setTheme", "dark", "applyTheme", "html", "document", "documentElement", "classList", "loadTheme", "initTheme", "globalIsDark", "globalToggleTheme", "globalSetTheme", "globalInitTheme", "app", "App", "use", "mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "__webpack_modules__", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "i", "fulfilled", "j", "Object", "keys", "every", "r", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "call", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "bind", "__webpack_exports__"], "sourceRoot": ""}