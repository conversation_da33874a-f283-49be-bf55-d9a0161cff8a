{"name": "prompt-crafter", "version": "1.0.0", "description": "A Vue.js + Electron application for crafting AI prompts", "main": "background.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "electron:serve": "vue-cli-service electron:serve", "electron:build": "vue-cli-service electron:build", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "dependencies": {}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.3.4", "@vue/cli-service": "^5.0.8", "autoprefixer": "^10.4.15", "electron": "^25.3.2", "electron-builder": "^24.4.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.9", "vite-plugin-electron-renderer": "^0.14.3", "vite-plugin-vue-devtools": "^7.0.8", "vue-cli-plugin-electron-builder": "^2.1.1"}}