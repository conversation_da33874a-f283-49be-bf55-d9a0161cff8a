!function(e){var o={};function t(n){if(o[n])return o[n].exports;var r=o[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,t),r.l=!0,r.exports}t.m=e,t.c=o,t.d=function(e,o,n){t.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:n})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,o){if(1&o&&(e=t(e)),8&o)return e;if(4&o&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var r in e)t.d(n,r,function(o){return e[o]}.bind(null,r));return n},t.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(o,"a",o),o},t.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},t.p="",t(t.s=0)}([function(e,o,t){e.exports=t(1)},function(e,o,t){const{contextBridge:n,ipcRenderer:r}=t(2);n.exposeInMainWorld("electronAPI",{readJson:e=>r.invoke("read-json",e),writeJson:(e,o)=>r.invoke("write-json",e,o),savePrompt:e=>r.invoke("save-prompt",e),copyToClipboard:e=>r.invoke("copy-to-clipboard",e),onDataFolderSelected:e=>r.on("data-folder-selected",(o,t)=>e(t)),selectFolder:()=>r.invoke("select-folder"),loadData:()=>r.invoke("load-data")})},function(e,o){e.exports=require("electron")}]);