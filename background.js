const { app, BrowserWindow, ipc<PERSON><PERSON>, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const fsSync = require('fs');
const os = require('os');

let mainWindow;
let dataFolder = path.join(os.homedir(), 'PromptCrafterData');

// Ensure data directory exists
const ensureDataDir = async () => {
  try {
    await fs.mkdir(dataFolder, { recursive: true });
    
    // Create default JSON files if they don't exist
    const defaultFiles = {
      'role.json': { items: ["You are an expert AI assistant", "You are a helpful assistant", "You are a senior software engineer"] },
      'objective.json': { items: ["Create a detailed plan for", "Develop a solution for", "Explain how to"] },
      'feature.json': { items: ["with responsive design", "with user authentication", "with real-time updates"] },
      'constraint.json': { items: ["while following best practices", "within a tight deadline", "with minimal dependencies"] },
      'approach.json': { items: ["using a step-by-step approach", "with clear examples", "with code samples"] }
    };

    for (const [filename, content] of Object.entries(defaultFiles)) {
      const filePath = path.join(dataFolder, filename);
      if (!fsSync.existsSync(filePath)) {
        await fs.writeFile(filePath, JSON.stringify(content, null, 2));
      }
    }
  } catch (error) {
    console.error('Error setting up data directory:', error);
  }
};

const createWindow = async () => {
  await ensureDataDir();
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js') // Corrected path for Vue CLI
    },
    title: 'Prompt Crafter',
    icon: path.join(__dirname, 'public/icon.png'),
    backgroundColor: '#000000', // True black background
    darkTheme: true, // Force dark theme on Linux
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default', // macOS styling
    vibrancy: process.platform === 'darwin' ? 'dark' : undefined, // macOS dark vibrancy
    show: false // Don't show until ready to prevent white flash
  });

  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await mainWindow.loadURL(process.env.WEBPACK_DEV_SERVER_URL);
    if (!process.env.IS_TEST) mainWindow.webContents.openDevTools();
  } else {
    // Load the index.html when not in development
    mainWindow.loadFile('index.html');
  }

  // Show window when ready to prevent white flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  createMenu();
};

const createMenu = () => {
  const menuTemplate = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Select Data Folder',
          click: async () => {
            const result = await dialog.showOpenDialog({
              properties: ['openDirectory']
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
              dataFolder = result.filePaths[0];
              await ensureDataDir();
              mainWindow.webContents.send('data-folder-selected', dataFolder);
            }
          }
        },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'delete' },
        { type: 'separator' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'About',
      submenu: [
        {
          label: 'About Prompt Crafter',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Prompt Crafter',
              message: 'Prompt Crafter',
              detail: 'A professional tool for crafting AI prompts with Vue.js and Electron.'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(menu);
};

// Handle file operations
ipcMain.handle('read-json', async (_, filename) => {
  try {
    const filePath = path.join(dataFolder, filename);
    const data = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filename}:`, error);
    return { items: [] };
  }
});

ipcMain.handle('write-json', async (_, filename, data) => {
  try {
    const filePath = path.join(dataFolder, filename);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error(`Error writing ${filename}:`, error);
    return false;
  }
});

ipcMain.handle('save-prompt', async (_, promptData) => {
  try {
    const promptsPath = path.join(dataFolder, 'prompts.json');
    let prompts = [];
    
    if (fsSync.existsSync(promptsPath)) {
      const data = await fs.readFile(promptsPath, 'utf-8');
      prompts = JSON.parse(data);
    }
    
    prompts.push({
      id: Date.now(),
      content: promptData,
      createdAt: new Date().toISOString()
    });
    
    await fs.writeFile(promptsPath, JSON.stringify(prompts, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving prompt:', error);
    return false;
  }
});

// Handle data loading
ipcMain.handle('load-data', async () => {
  const data = {};
  try {
    // Load static data from the public folder
    const staticDataPath = process.env.WEBPACK_DEV_SERVER_URL
      ? path.join(__dirname, '../public')
      : path.join(process.resourcesPath, 'public');
    const staticFiles = ['categories.json', 'checkboxes.json', 'role.json', 'objective.json', 'feature.json', 'constraint.json', 'approach.json'];
    for (const file of staticFiles) {
      const filePath = path.join(staticDataPath, file);
      const fileContent = await fs.readFile(filePath, 'utf-8');
      data[file.replace('.json', '')] = JSON.parse(fileContent);
    }

    // Load user-specific prompts
    const promptsPath = path.join(dataFolder, 'prompts.json');
    if (fsSync.existsSync(promptsPath)) {
      const promptsContent = await fs.readFile(promptsPath, 'utf-8');
      data['prompts'] = JSON.parse(promptsContent);
    } else {
      data['prompts'] = [];
    }

    return data;
  } catch (error) {
    console.error('Error loading initial data:', error);
    return null;
  }
});

// App lifecycle events
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
