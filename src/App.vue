<template>
  <div id="app" class="flex h-screen bg-gray-50 dark:bg-black transition-colors duration-200">
    <!-- Left Panel -->
    <div class="w-1/3 max-w-sm min-w-[320px] overflow-y-auto bg-white dark:bg-black shadow-lg dark:shadow-soft-dark border-r border-gray-200 dark:border-gray-700">
      <!-- Header -->
      <div class="flex items-center justify-center p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black">
        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">Prompt Crafter</h1>
      </div>
      <LeftPanel />
    </div>

    <!-- Main Panel -->
    <div class="flex-1 bg-gray-50 dark:bg-black">
      <MainPanel />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import LeftPanel from './components/LeftPanel.vue';
import MainPanel from './components/MainPanel.vue';
import { initTheme } from './composables/useTheme';

onMounted(() => {
  initTheme();
});
</script>

<style>
/* Global styles */
#app {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');
}

.dark * {
  scrollbar-color: theme('colors.gray.600') theme('colors.gray.800');
}
</style>
