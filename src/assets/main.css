@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 262 83% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 70%;
    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 0%;
    --ring: 262 83% 58%;
  }

  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-gray-50 dark:bg-black text-gray-900 dark:text-white font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Typography scale */
  h1 {
    @apply text-2xl font-bold tracking-tight text-gray-900 dark:text-white;
  }

  h2 {
    @apply text-xl font-semibold tracking-tight text-gray-900 dark:text-white;
  }

  h3 {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
  }

  h4 {
    @apply text-base font-semibold text-gray-900 dark:text-white;
  }

  p {
    @apply text-gray-700 dark:text-white leading-relaxed;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-purple-500 ring-offset-2 dark:ring-offset-black;
  }
}

/* Enhanced scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-black;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');
}

.dark * {
  scrollbar-color: theme('colors.gray.600') theme('colors.black');
}

/* Enhanced component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-xl text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-black disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95;
  }

  .btn-primary {
    @apply bg-purple-600 text-white hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 focus-visible:ring-purple-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-purple-100 dark:bg-black text-purple-800 dark:text-white border border-gray-300 dark:border-gray-600 hover:bg-purple-200 dark:hover:bg-gray-900 focus-visible:ring-purple-500 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-white focus-visible:ring-purple-500;
  }

  .input {
    @apply flex h-10 w-full rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-black px-4 py-2 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:border-purple-500 dark:focus-visible:ring-purple-400 dark:focus-visible:border-purple-400 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .card {
    @apply rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-header {
    @apply p-6 border-b border-gray-200 dark:border-gray-700;
  }

  .card-content {
    @apply p-6;
  }

  .card-footer {
    @apply p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
