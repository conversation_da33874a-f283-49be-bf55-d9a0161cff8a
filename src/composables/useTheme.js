import { ref, readonly } from 'vue';

const isDark = ref(true); // Always dark mode
const THEME_KEY = 'prompt-crafter-theme';

export function useTheme() {
  const toggleTheme = () => {
    // Theme toggle disabled - always dark mode
    return;
  };

  const setTheme = (dark) => {
    // Theme setting disabled - always dark mode
    return;
  };

  const applyTheme = () => {
    const html = document.documentElement;
    // Always apply dark mode
    html.classList.add('dark');
  };

  const saveTheme = () => {
    // Theme saving disabled - always dark mode
    return;
  };

  const loadTheme = () => {
    // Theme loading disabled - always dark mode
    isDark.value = true;
    applyTheme();
  };

  const initTheme = () => {
    loadTheme();
    // No need to listen for system theme changes - always dark mode
    return () => {};
  };

  return {
    isDark: readonly(isDark),
    toggleTheme,
    setTheme,
    initTheme
  };
}

// Global theme state
const { isDark: globalIsDark, toggleTheme: globalToggleTheme, setTheme: globalSetTheme, initTheme: globalInitTheme } = useTheme();

export { globalIsDark as isDark, globalToggleTheme as toggleTheme, globalSetTheme as setTheme, globalInitTheme as initTheme };
