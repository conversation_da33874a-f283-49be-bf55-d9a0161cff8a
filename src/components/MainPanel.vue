<template>
  <div class="flex flex-col h-full bg-white dark:bg-black">
    <!-- Header -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-black">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Prompt Editor</h2>
        <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <span>{{ store.mainPrompt.length }} characters</span>
          <span>•</span>
          <span>{{ store.mainPrompt.split('\n').length }} lines</span>
        </div>
      </div>
    </div>

    <!-- Prompt Editor -->
    <div class="flex-1 p-6 min-h-0">
      <div class="relative h-full">
        <textarea
          v-model="store.mainPrompt"
          class="w-full h-full p-4 bg-gray-50 dark:bg-black border border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 shadow-sm hover:shadow-md font-mono text-sm leading-relaxed"
          placeholder="Your crafted prompt will appear here...

Start by selecting categories and options from the left panel to build your prompt."
        ></textarea>

        <!-- Floating action hint -->
        <div v-if="!store.mainPrompt.trim()" class="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div class="text-center space-y-3 opacity-50">
            <div class="w-16 h-16 mx-auto bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
              </svg>
            </div>
            <p class="text-gray-500 dark:text-gray-400 font-medium">Start crafting your prompt</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-black">
      <div class="flex flex-wrap gap-3">
        <button
          @click="copyToClipboard"
          :disabled="!store.mainPrompt.trim()"
          class="flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
          </svg>
          <span>Copy</span>
        </button>

        <button
          @click="clearPrompt"
          :disabled="!store.mainPrompt.trim()"
          class="flex items-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
          <span>Clear</span>
        </button>

        <button
          @click="savePrompt"
          :disabled="!store.mainPrompt.trim()"
          class="flex items-center space-x-2 px-6 py-3 bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white font-semibold rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
          </svg>
          <span>Save</span>
        </button>
      </div>
    </div>

    <!-- Saved Prompts List -->
    <div class="flex-1 p-6 border-t border-gray-200 dark:border-gray-700 min-h-0 bg-gray-50 dark:bg-gray-800/50">
      <div class="h-full flex flex-col">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold text-gray-900 dark:text-white">Saved Prompts</h3>
          <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10"/>
            </svg>
            <span>{{ store.savedPrompts.length }} saved</span>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto space-y-3 min-h-0 custom-scrollbar">
          <!-- Empty State -->
          <div
            v-if="store.savedPrompts.length === 0"
            class="flex flex-col items-center justify-center h-full text-center space-y-4 opacity-60"
          >
            <div class="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg class="w-10 h-10 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 7a2 2 0 002-2h10a2 2 0 002 2v2M7 7h10"/>
              </svg>
            </div>
            <div class="space-y-2">
              <p class="text-gray-600 dark:text-gray-400 font-medium">No saved prompts yet</p>
              <p class="text-sm text-gray-500 dark:text-gray-500">Save your first prompt using the Save button above</p>
            </div>
          </div>

          <!-- Saved Prompts -->
          <div
            v-for="prompt in store.savedPrompts"
            :key="prompt.id"
            class="group relative p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-600 rounded-xl hover:border-gray-300 dark:hover:border-gray-500 cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md hover:scale-[1.01] active:scale-[0.99]"
            @click="loadSavedPrompt(prompt.id)"
          >
            <!-- Header -->
            <div class="flex justify-between items-start mb-3">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-primary-500 rounded-full"></div>
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  {{ formatDate(prompt.createdAt) }}
                </span>
              </div>
              <button
                @click.stop="removeSavedPrompt(prompt.id)"
                class="opacity-0 group-hover:opacity-100 p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-700"
                title="Remove prompt"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
              </button>
            </div>

            <!-- Content Preview -->
            <div class="text-sm text-gray-700 dark:text-gray-300 line-clamp-4 leading-relaxed">
              {{ prompt.content }}
            </div>

            <!-- Footer -->
            <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-600">
              <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"/>
                </svg>
                <span>{{ prompt.content.length }} chars</span>
              </div>
              <div class="text-xs text-primary-600 dark:text-primary-400 font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                Click to load
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from '../store';

const store = useAppStore();

const copyToClipboard = () => {
  if (!store.mainPrompt.trim()) {
    alert('No prompt to copy!');
    return;
  }

  navigator.clipboard.writeText(store.mainPrompt).then(() => {
    alert('Prompt copied to clipboard!');
  }).catch(err => {
    console.error('Failed to copy text: ', err);
    alert('Failed to copy prompt.');
  });
};

const clearPrompt = () => {
  if (store.mainPrompt.trim() && !confirm('Are you sure you want to clear the prompt?')) {
    return;
  }
  store.clearMainPrompt();
};

const savePrompt = async () => {
  if (!store.mainPrompt.trim()) {
    alert('No prompt to save!');
    return;
  }

  const success = await store.saveMainPromptToList();
  if (success) {
    alert('Prompt saved successfully!');
  } else {
    alert('Failed to save prompt.');
  }
};

const loadSavedPrompt = (promptId) => {
  if (store.mainPrompt.trim() && !confirm('Loading a saved prompt will replace the current content. Continue?')) {
    return;
  }

  const success = store.loadPromptFromSaved(promptId);
  if (!success) {
    alert('Failed to load prompt.');
  }
};

const removeSavedPrompt = (promptId) => {
  if (!confirm('Are you sure you want to delete this saved prompt?')) {
    return;
  }

  const success = store.removeSavedPrompt(promptId);
  if (!success) {
    alert('Failed to remove prompt.');
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<style scoped>
/* Custom scrollbar for saved prompts list */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
}

/* Firefox scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');
}

.dark .custom-scrollbar {
  scrollbar-color: theme('colors.gray.600') theme('colors.gray.800');
}

/* Line clamp utility */
.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
