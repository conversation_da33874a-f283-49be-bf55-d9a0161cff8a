<template>
  <div class="space-y-4 p-4">

    <!-- Categories Section -->
    <div v-for="category in store.categories" :key="category.id" class="space-y-2">
      <label :for="category.id" class="block text-xs font-semibold text-gray-800 dark:text-white tracking-wide">
        {{ category.name }}
      </label>
      <div class="space-y-2">
        <!-- Dropdown with Add to Prompt Button -->
        <div class="flex items-center space-x-2">
          <select
            :id="category.id"
            v-model="category.selected"
            class="flex-1 px-3 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-xs text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
            :class="{
              'border-emerald-500 dark:border-emerald-400 ring-2 ring-emerald-200 dark:ring-emerald-800': store.isItemUsed(`category-${category.id}`)
            }"
          >
            <option value="" class="text-gray-500 dark:text-gray-400">Select {{ category.name.toLowerCase() }}</option>
            <option v-for="(item, index) in category.items" :key="index" :value="item" class="text-gray-900 dark:text-white">
              {{ item }}
            </option>
          </select>

          <!-- Add to Prompt Arrow Button -->
          <button
            @click="handleAdd(category)"
            class="px-2.5 py-1.5 bg-purple-600 hover:bg-purple-700 dark:bg-purple-600 dark:hover:bg-purple-700 text-white rounded-lg shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-105 active:scale-95"
            :class="{
              'bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-600 dark:hover:bg-emerald-700 ring-2 ring-emerald-200 dark:ring-emerald-800': store.isItemUsed(`category-${category.id}`)
            }"
            :disabled="!category.selected"
            :title="store.isItemUsed(`category-${category.id}`) ? 'Added to prompt' : 'Add to prompt'"
          >
            <svg v-if="store.isItemUsed(`category-${category.id}`)" class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            <svg v-else class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>

        <div class="flex flex-col space-y-2">

          <!-- Add/Remove Controls -->
          <div class="flex items-center space-x-2">
            <input
              v-model="newItems[category.id]"
              @keyup.enter="addDropdownItem(category)"
              type="text"
              class="flex-1 px-2.5 py-1.5 bg-white dark:bg-black border border-gray-300 dark:border-gray-600 rounded-lg text-xs text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 transition-all duration-200"
              :placeholder="`Add new ${category.name.toLowerCase()}`"
            />
            <button
              @click="addDropdownItem(category)"
              class="px-2.5 py-1.5 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded-lg text-xs font-medium hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200"
              title="Add item"
            >
              <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
            </button>
            <button
              v-if="category.items.length > 0"
              @click="removeDropdownItem(category)"
              class="px-2.5 py-1.5 bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg text-xs font-medium hover:bg-red-100 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200"
              title="Remove selected"
            >
              <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Divider -->
    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
      </div>
      <div class="relative flex justify-center">
        <span class="px-3 bg-white dark:bg-black text-xs font-medium text-gray-500 dark:text-gray-400">Options</span>
      </div>
    </div>

    <!-- Checkboxes Section -->
    <div class="space-y-3">
      <div v-for="checkbox in store.checkboxes" :key="checkbox.id" class="group">
        <div class="flex items-center justify-between p-2.5 bg-gray-50 dark:bg-black rounded-lg border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-200 hover:shadow-sm">
          <div class="flex items-center space-x-3">
            <CheckboxRoot
              v-model:checked="checkbox.checked"
              :id="checkbox.id"
              class="flex items-center justify-center w-5 h-5 bg-white dark:bg-black border-2 border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 hover:border-primary-400 dark:hover:border-primary-400 data-[state=checked]:bg-primary-600 data-[state=checked]:border-primary-600 dark:data-[state=checked]:bg-primary-500 dark:data-[state=checked]:border-primary-500"
            >
              <CheckboxIndicator>
                <svg class="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 6.293a1 1 0 00-1.414 0L9 12.586l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z" clip-rule="evenodd" />
                </svg>
              </CheckboxIndicator>
            </CheckboxRoot>
            <label :for="checkbox.id" class="text-xs font-medium text-gray-800 dark:text-white cursor-pointer select-none">
              {{ checkbox.label }}
            </label>
          </div>
          <button
            @click="handleAddCheckbox(checkbox)"
            class="px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transform hover:scale-105 active:scale-95"
            :class="checkbox.used
              ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-200 dark:hover:bg-emerald-900/50 focus:ring-emerald-500'
              : 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/50 focus:ring-purple-500'"
            :disabled="!checkbox.checked"
          >
            <span class="flex items-center space-x-1">
              <svg v-if="checkbox.used" class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <svg v-else class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              <span>{{ checkbox.used ? 'Added' : 'Add' }}</span>
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue';
import { useAppStore } from '../store';
import { CheckboxRoot, CheckboxIndicator } from 'radix-vue';

const store = useAppStore();

const newItems = reactive({});

const addDropdownItem = (category) => {
  const val = (newItems[category.id] || '').trim();
  if (val && !category.items.includes(val)) {
    category.items.push(val);
    newItems[category.id] = '';
  }
};

const removeDropdownItem = (category) => {
  if (category.selected) {
    const idx = category.items.indexOf(category.selected);
    if (idx !== -1) {
      category.items.splice(idx, 1);
      category.selected = '';
    }
  }
};

const handleAdd = (category) => {
  if (category.selected) {
    const key = `category-${category.id}`;
    // Add to the main prompt editor with formatted category name
    store.appendFormattedToMainPrompt(category.name, category.selected);
    // Also track usage for UI feedback
    store.usedItems.add(key);
  }
};

const handleAddCheckbox = (checkbox) => {
  if (checkbox.checked) {
    // Add to the main prompt editor with formatted category name
    store.appendFormattedToMainPrompt('Instructions', checkbox.label);
    // Track usage for UI feedback
    store.usedItems.add(`checkbox-${checkbox.id}`);
    checkbox.used = true;
  }
};

onMounted(() => {
  store.fetchData();
  
  watch(() => store.categories, (newCategories) => {
    newCategories.forEach(cat => {
      if (newItems[cat.id] === undefined) {
        newItems[cat.id] = '';
      }
    });
  }, { immediate: true, deep: true });
});
</script>

<style scoped>
/* No additional styles needed as Tailwind CSS is used. */
</style>
